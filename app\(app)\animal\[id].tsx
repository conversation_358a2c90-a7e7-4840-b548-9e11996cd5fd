import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Alert as RNAlert,
  ActivityIndicator,
  Modal,
  Alert,
  FlatList,
} from 'react-native';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import Button from '@/components/Button';
import {
  Rabbit,
  Heart,
  Calendar,
  Leaf,
  Tag,
  Edit,
  Trash2,
  ChevronDown,
  Check,
  X,
  Camera,
  Image as ImageIcon,
  User,
  CheckSquare,
  Square,
  AlertCircle,
  Thermometer,
  Droplets,
  Pill,
  QrCode,
  Archive,
  ClipboardList,
  Plus,
  DollarSign,
  Milk,
  Droplet,
} from 'lucide-react-native';
import ImagePicker from '@/components/ImagePicker';
import { uploadImageAsync } from '@/utils/firebase-storage';
import { TextInput } from 'react-native';
import { analyzeAnimalHealth } from '@/utils/openai-vision';
import QRCodeDisplay from '@/components/QRCodeDisplay';
import { generateUniversalLink } from '@/utils/qrcode';
import InactiveStatusModal from '@/components/InactiveStatusModal';
import Toast from 'react-native-toast-message';
import { useTranslation } from '@/i18n/useTranslation';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import AnimalChecklistList from '@/components/AnimalChecklistList';
import { HealthCardAnimal } from '@/components/HealthCardAnimal';
import { EntityGallery } from '@/components/EntityGallery';
import { useEntityGallery } from '@/hooks/useEntityGallery';
import { AnimalHealthChecksSection } from '@/components/animal/AnimalHealthChecksSection';
import { AnimalPregnanciesSection } from '@/components/animal/AnimalPregnanciesSection';
import { AnimalRecordsSection } from '@/components/animal/AnimalRecordsSection';
import { AnimalMilkingSection } from '@/components/animal/AnimalMilkingSection';

export default function AnimalDetailsScreen() {
  const { id } = useLocalSearchParams();
  const {
    animals,
    fields,
    getAnimal,
    deleteAnimal,
    updateAnimal,
    currentFarm,
    animalCheckList,
    markAnimalInactive,
    saveAnimalChecklist,
    getAnimalChecklists,
    // loadEntityGallery
  } = useFarmStore();
  const { language } = useAuthStore();
  const { user } = useAuthStore();
  const [animal, setAnimal] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showStatusDropdown, setShowStatusDropdown] = useState(false);
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState('');
  const [newImageUri, setNewImageUri] = useState('');
  const [isAddingImage, setIsAddingImage] = useState(false);
  const [activeTab, setActiveTab] = useState('details');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [healthAnalysis, setHealthAnalysis] = useState<any>(null);
  const [showQRModal, setShowQRModal] = useState(false);
  const [showInactiveModal, setShowInactiveModal] = useState(false);
  const { t, isRTL } = useTranslation()
  // Editable fields
  const [status, setStatus] = useState<'healthy' | 'sick' | 'pregnant' | 'nursing' | 'quarantined'>('healthy');
  const [notes, setNotes] = useState('');

  // Checklist items
  const [checklist, setChecklist] = useState([
    { id: '1', title: t('entity.animal.checklistArray.dailyFeeding'), value: "dailyFeeding", completed: false },
    { id: '2', title: t('entity.animal.checklistArray.checkWater'), value: "checkWater", completed: false },
    { id: '3', title: t('entity.animal.checklistArray.healthInspection'), value: "healthInspection", completed: false },
    { id: '4', title: t('entity.animal.checklistArray.checkParasites'), value: "checkParasites", completed: false },
    { id: '5', title: t('entity.animal.checklistArray.cleanArea'), value: "cleanArea", completed: false },
    { id: '6', title: t('entity.animal.checklistArray.medication'), value: "medication", completed: false },
    { id: '7', title: t('entity.animal.checklistArray.checkTemperature'), value: "checkTemperature", completed: false },
  ]);

  const isInactive = animal?.isInactive === true;
  const [animalCheckListData, setAnimalCheckListData] = useState('')
  useEffect(() => {
    if (id) {
      const animalData = getAnimal(id as string);
      // console.log(animalData?.healthChecks)
      getAnimalChecklists(currentFarm?.id, id)
      // console.log({ checksData })
      // setAnimalCheckListData(checksData)
      if (animalData) {
        setAnimal(animalData);
        setStatus(animalData.status || 'healthy');
        setNotes(animalData.notes || '');
      }
      setLoading(false);
    }
  }, [id, animals]);

  // useEffect(() => {
  //   console.log({ animalCheckList })
  // }, [animalCheckList])

  // Move hooks before conditional returns - use id from params instead of animal?.id
  const {
    photos: galleryPhotos,
    loading: galleryLoading,
    addImage: addGalleryImage,
  } = useEntityGallery({
    entityId: id as string || '',
    entityType: 'animal',
    autoLoad: !!id && !!animal // Only load when both id and animal exist
  });

  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </SafeAreaView>
    );
  }

  if (!animal) {
    return (
      <SafeAreaView style={styles.errorContainer}>
        <Text style={styles.errorText}>Animal not found</Text>
        <Button
          title="Go Back"
          onPress={() => router.back()}
          style={styles.errorButton}
        />
      </SafeAreaView>
    );
  }

  const getField = () => {
    if (animal.fieldId) {
      return fields.find(field => field.id === animal.fieldId);
    }
    return null;
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Not specified';
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusColor = (statusValue: string) => {
    switch (statusValue) {
      case 'healthy':
        return colors.success;
      case 'sick':
        return colors.danger;
      case 'pregnant':
        return colors.info;
      case 'nursing':
        return colors.warning;
      case 'quarantined':
        return colors.secondary;
      default:
        return colors.gray[500];
    }
  };

  const handleDelete = async () => {
    try {
      await deleteAnimal(animal.id);
      RNAlert.alert('Success', 'Animal deleted successfully');
      router.back();
    } catch (error) {
      console.error('Error deleting animal:', error);
      RNAlert.alert('Error', 'Failed to delete animal');
    }
  };

  const handleSaveChanges = async () => {
    try {
      const updatedAnimal = {
        ...animal,
        status,
        notes,
        updatedAt: new Date()
      };

      await updateAnimal(animal.id, updatedAnimal);
      setIsEditing(false);
      RNAlert.alert('Success', 'Animal updated successfully');
    } catch (error) {
      console.error('Error updating animal:', error);
      RNAlert.alert('Error', 'Failed to update animal');
    }
  };

  const handleAddImage = async () => {
    if (!newImageUri) {
      RNAlert.alert('Error', 'Please select an image first');
      return;
    }

    try {
      setIsAddingImage(true);
      // Upload the new image
      const imageUrl = await uploadImageAsync(newImageUri, 'animals');

      // Add the new photo to the animal's photos array
      const updatedPhotos = [
        ...(animal.photos || []),
        {
          url: imageUrl,
          timestamp: new Date(),
          takenBy: ''
        }
      ];

      // Update the animal with the new photos array
      const updatedAnimal = {
        ...animal,
        photos: updatedPhotos,
        updatedAt: new Date()
      };

      await updateAnimal(animal.id, updatedAnimal);
      setAnimal(updatedAnimal);
      setNewImageUri('');
      setIsAddingImage(false);
      RNAlert.alert('Success', 'Image added successfully');
    } catch (error) {
      console.error('Error adding image:', error);
      RNAlert.alert('Error', 'Failed to add image');
      setIsAddingImage(false);
    }
  };

  const toggleChecklistItem = (id: string) => {
    setChecklist(checklist.map(item =>
      item.id === id ? { ...item, completed: !item.completed } : item
    ));
  };

  const handleAnalyzeHealth = async () => {
    if (!animal.image && (!animal.photos || animal.photos.length === 0)) {
      RNAlert.alert('Error', 'No image available for analysis');
      return;
    }

    try {
      setIsAnalyzing(true);

      // Use the main image or the first photo
      const imageToAnalyze = animal.image || animal.photos[0].url;

      // Call the OpenAI Vision API
      const analysis = await analyzeAnimalHealth(imageToAnalyze);

      if (analysis) {
        setHealthAnalysis(analysis);
        setActiveTab('health');
      } else {
        RNAlert.alert('Error', 'Failed to analyze animal health');
      }
    } catch (error) {
      console.error('Error analyzing animal health:', error);
      RNAlert.alert('Error', 'Failed to analyze animal health');
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Generate QR code value
  const getQRCodeValue = () => {
    if (!animal || !currentFarm) return '';
    return generateUniversalLink('animal', animal.id, currentFarm.id);
  };

  const handleMarkInactive = async (data: {
    reason: string;
    notes?: string;
    image?: string;
  }) => {
    try {
      await markAnimalInactive(animal.id, data);
      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'Animal marked as inactive',
      });
    } catch (error) {
      console.error('Error marking animal as inactive:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to mark animal as inactive',
      });
    }
  };

  const handleAddExpense = () => {
    router.push(`/finance/create?id=${id}&entity=animal`)
  };
  const handleAddMilkRecord = () => {
    router.push(`/animal/milk/add?id=${id}`);
  };

  const handleAddCleanlinessRecord = () => {
    router.push(`/animal/cleanliness/add?id=${id}`);
  };

  const renderDetailsTab = () => {
    // Calculate animal statistics (mock data - replace with actual data from your store)
    const animalStats = {
      healthChecks: 12, // Total health checks
      lastCheckDays: 5, // Days since last health check
      pregnancies: animal.gender === 'female' ? 2 : 0, // Total pregnancies
      milkRecords: animal.species === 'cow' || animal.species === 'goat' ? 45 : 0, // Total milk records
    };

    return (
      <View style={styles.tabContent}>
        {/* Animal Statistics */}
        <View style={styles.statsContainer}>
          <View style={[styles.statCard, isRTL && { alignItems: 'flex-end' }]}>
            <Text style={[styles.statNumber, { color: colors.primary }]}>{animalStats.healthChecks}</Text>
            <Text style={[styles.statLabel, isRTL && { textAlign: 'right' }]}>{t('entity.animal.healthChecks.total')}</Text>
          </View>
          <View style={[styles.statCard, isRTL && { alignItems: 'flex-end' }]}>
            <Text style={[styles.statNumber, { color: animalStats.lastCheckDays <= 7 ? colors.success : colors.warning }]}>{animalStats.lastCheckDays}</Text>
            <Text style={[styles.statLabel, isRTL && { textAlign: 'right' }]}>{t('entity.animal.daysSinceCheck')}</Text>
          </View>
          {animal.gender === 'female' && (
            <View style={[styles.statCard, isRTL && { alignItems: 'flex-end' }]}>
              <Text style={[styles.statNumber, { color: colors.info }]}>{animalStats.pregnancies}</Text>
              <Text style={[styles.statLabel, isRTL && { textAlign: 'right' }]}>{t('entity.animal.pregnancies.total')}</Text>
            </View>
          )}
          {(animal.species === 'cow' || animal.species === 'goat') && (
            <View style={[styles.statCard, isRTL && { alignItems: 'flex-end' }]}>
              <Text style={[styles.statNumber, { color: colors.success }]}>{animalStats.milkRecords}</Text>
              <Text style={[styles.statLabel, isRTL && { textAlign: 'right' }]}>{t('entity.animal.milkRecords.total')}</Text>
            </View>
          )}
        </View>

        <View style={styles.infoSection}>
        <View style={[styles.infoRow, isRTL && { flexDirection: 'row-reverse' }]}>
          <View style={[styles.infoItem, isRTL && { flexDirection: 'row-reverse' }]}>
            <MaterialIcons
              name="wc"
              size={20}
              color={colors.gray[600]}
              style={[styles.infoIcon, isRTL && { marginLeft: 0, marginRight: 8 }]}
            />
            <View>
              <Text style={[styles.infoLabel, isRTL && { textAlign: 'right', marginRight: 8 }]}>{t('entity.animal.gender')}</Text>
              <Text style={[styles.infoValue, isRTL && { textAlign: 'right', marginRight: 8 }]}>
                {animal.gender
                  ? t(`entity.animal.gender_${animal.gender.toLowerCase()}`)
                  : t('common.unknown')}
              </Text>
            </View>
          </View>

          <View style={[styles.infoItem, isRTL && { flexDirection: 'row-reverse' }]}>
            <MaterialIcons
              name="calendar-today"
              size={20}
              color={colors.gray[600]}
              style={[styles.infoIcon, isRTL && { marginLeft: 0, marginRight: 8 }]}
            />
            <View>
              <Text style={[styles.infoLabel, isRTL && { textAlign: 'right', marginRight: 8 }]}>{t('entity.animal.birthDate')}</Text>
              <Text style={[styles.infoValue, isRTL && { textAlign: 'right', marginRight: 8 }]}>
                {animal.birthDate ? formatDate(animal.birthDate) : t('common.unknown')}
              </Text>
            </View>
          </View>
        </View>

        <View style={[styles.infoRow, isRTL && { flexDirection: 'row-reverse' }]}>
          <View style={[styles.infoItem, isRTL && { flexDirection: 'row-reverse' }]}>
            <MaterialIcons
              name="tag"
              size={20}
              color={colors.gray[600]}
              style={[styles.infoIcon, isRTL && { marginLeft: 0, marginRight: 8 }]}
            />
            <View>
              <Text style={[styles.infoLabel, isRTL && { textAlign: 'right', marginRight: 8 }]}>{t('entity.animal.idNumber')}</Text>
              <Text style={[styles.infoValue, isRTL && { textAlign: 'right', marginRight: 8 }]}>
                {animal.identificationNumber || t('common.notAssigned')}
              </Text>
            </View>
          </View>

          <View style={[styles.infoItem, isRTL && { flexDirection: 'row-reverse' }]}>
            <MaterialIcons
              name="agriculture"
              size={20}
              color={getField() ? colors.success : colors.gray[400]}
              style={[styles.infoIcon, isRTL && { marginLeft: 0, marginRight: 8 }]}
            />
            <View>
              <Text style={[styles.infoLabel, isRTL && { textAlign: 'right', marginRight: 8 }]}>
                {getField() ? t('entity.animal.field') : t('entity.animal.location')}
              </Text>
              <Text style={[styles.infoValue, isRTL && { textAlign: 'right', marginRight: 8 }]}>
                {getField() ? getField().name : t('common.notSpecified')}
              </Text>
            </View>
          </View>
        </View>


      </View>

      {isInactive && (
        <View style={styles.inactiveDetailsContainer}>
          <Text style={styles.inactiveDetailsTitle}>
            {t('entity.animal.inactiveDetailsTitle')}
          </Text>

          <View style={styles.inactiveDetailsRow}>
            <MaterialIcons
              name="info"
              size={18}
              color={colors.gray[600]}
              style={[styles.infoIcon, isRTL && { marginLeft: 0, marginRight: 8 }]}
            />
            <Text style={styles.inactiveDetailsLabel}>{t('entity.animal.inactiveReason')}:</Text>
            <Text style={styles.inactiveDetailsValue}>
              {animal.inactiveReason || t('common.notSpecified')}
            </Text>
          </View>

          {animal.inactiveDate && (
            <View style={styles.inactiveDetailsRow}>
              <MaterialIcons
                name="calendar-today"
                size={18}
                color={colors.gray[600]}
                style={[styles.infoIcon, isRTL && { marginLeft: 0, marginRight: 8 }]}
              />
              <Text style={styles.inactiveDetailsLabel}>{t('entity.animal.inactiveDate')}:</Text>
              <Text style={styles.inactiveDetailsValue}>
                {new Date(animal.inactiveDate).toLocaleDateString()}
              </Text>
            </View>
          )}

          {animal.inactiveNotes && (
            <View style={styles.inactiveDetailsRow}>
              <MaterialIcons
                name="notes"
                size={18}
                color={colors.gray[600]}
                style={[styles.infoIcon, isRTL && { marginLeft: 0, marginRight: 8 }]}
              />
              <Text style={styles.inactiveDetailsLabel}>{t('entity.animal.inactiveNotes')}:</Text>
              <Text style={styles.inactiveDetailsValue}>{animal.inactiveNotes}</Text>
            </View>
          )}

          {animal.inactiveImage && (
            <View style={styles.inactiveImageContainer}>
              <MaterialIcons
                name="image"
                size={18}
                color={colors.gray[600]}
                style={[styles.infoIcon, isRTL && { marginLeft: 0, marginRight: 8 }]}
              />
              <Text style={styles.inactiveDetailsLabel}>{t('entity.animal.inactiveImage')}:</Text>
              <Image
                source={{ uri: animal.inactiveImage }}
                style={styles.inactiveImage}
                resizeMode="cover"
              />
            </View>
          )}
        </View>
      )}

      <View style={styles.quickActionsContainer}>
        <Text style={[styles.sectionTitle, isRTL && { textAlign: 'right', marginRight: 8 }]}>{t('entity.animal.quickActions')}</Text>

        <View style={[styles.quickActions, isRTL && { flexDirection: 'row-reverse' }]}>
          <TouchableOpacity
            disabled={isInactive}
            style={[
              styles.quickActionButton,
              isInactive && styles.disabledActionButton,
              isRTL && { flexDirection: 'row-reverse' }
            ]}
            onPress={() => router.push({
              pathname: '/animal/create',
              params: { editMode: true, id: animal.id }
            })}
          >
            <View style={[styles.quickActionIcon, isRTL && { flexDirection: 'row-reverse' }, { backgroundColor: colors.success }]}>
              <MaterialIcons name="edit" size={20} color={colors.white} />
            </View>
            <Text style={[styles.quickActionText, isRTL && { textAlign: 'right', marginRight: 8 }]}>{t('common.edit')}</Text>
          </TouchableOpacity>

          {/* Mark as Inactive */}
          <TouchableOpacity
            disabled={isInactive}
            style={[
              styles.quickActionButton,
              isInactive && styles.disabledActionButton,
              isRTL && { flexDirection: 'row-reverse' }
            ]}
            onPress={() => setShowInactiveModal(true)}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: colors.gray[500] }]}>
              <MaterialIcons name="archive" size={20} color={colors.white} />
            </View>
            <Text style={[styles.quickActionText, isRTL && { textAlign: 'right', marginRight: 8 }]}>{t('entity.animal.markInactive')}</Text>
          </TouchableOpacity>

          {/* <TouchableOpacity
            // style={styles.actionButton}
            style={[
              styles.quickActionButton,
              isInactive && styles.disabledActionButton,
              isRTL && { flexDirection: 'row-reverse' }
            ]}
            onPress={handleAddExpense}
          >
            <DollarSign size={24} color={colors.primary} />
            <Text style={[styles.quickActionText, isRTL && { textAlign: 'right', marginRight: 8 }]}>{t('entity.animal.addExpense')}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            // style={styles.actionButton}
            style={[
              styles.quickActionButton,
              isInactive && styles.disabledActionButton,
              isRTL && { flexDirection: 'row-reverse' }
            ]}
            onPress={handleAddMilkRecord}
          >
            <Milk size={24} color={colors.primary} />
            <Text style={[styles.quickActionText, isRTL && { textAlign: 'right', marginRight: 8 }]}>{t('entity.animal.addMilkRecord')}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            // style={styles.actionButton}
            style={[
              styles.quickActionButton,
              isInactive && styles.disabledActionButton,
              isRTL && { flexDirection: 'row-reverse' }
            ]}
            onPress={handleAddCleanlinessRecord}
          >
            <Droplet size={24} color={colors.primary} />
            <Text style={[styles.quickActionText, isRTL && { textAlign: 'right', marginRight: 8 }]}>{t('entity.animal.addCleanliness')}</Text>
          </TouchableOpacity> */}
        </View>
      </View>

      <View style={styles.notesSection}>
        <Text style={[styles.sectionTitle, isRTL && { textAlign: 'right', marginRight: 8 }]}>{t('entity.animal.notes')}</Text>

        <Text style={[styles.notesText, isRTL && { textAlign: 'right', marginRight: 8 }]}>
          {animal.notes || t('entity.animal.noNotes')}
        </Text>
      </View>

      {/* <View style={styles.qrCodeSection}>
        <View style={[styles.qrCodeHeader, isRTL && { flexDirection: 'row-reverse' }]}>
          <Text style={styles.sectionTitle}>{t('entity.animal.qrCodeTitle')}</Text>
          <TouchableOpacity
            style={styles.viewQRButton}
            onPress={() => setShowQRModal(true)}
          >
            <QrCode size={16} color={colors.primary} style={[styles.viewQRIcon, isRTL && { marginLeft: 4 }]} />
            <Text style={[styles.viewQRText, isRTL && { textAlign: 'right' }]}>
              {t('entity.animal.viewQRCode')}
            </Text>
          </TouchableOpacity>
        </View>

        <Text style={[styles.qrCodeDescription, isRTL && { textAlign: 'right' }]}>
          {t('entity.animal.qrCodeDescription')}
        </Text>
      </View> */}
      {/* <View style={styles.qrCodeSection}>
        <View style={styles.qrCodeHeader}>
          <Text style={styles.sectionTitle}>QR Code</Text>
          <TouchableOpacity
            style={styles.viewQRButton}
            onPress={() => setShowQRModal(true)}
          >
            <QrCode size={16} color={colors.primary} style={styles.viewQRIcon} />
            <Text style={styles.viewQRText}>View QR Code</Text>
          </TouchableOpacity>
        </View>
        <Text style={styles.qrCodeDescription}>
          Scan this QR code to quickly access this animal's information.
        </Text>
      </View> */}
    </View>
  );
  }
  const renderChecklistTab = () => (
    <View style={styles.tabContent}>
      {
        user && user.role === "caretaker" ?

          <>
            <View style={styles.checklistContainer}>
              {checklist.map((item) => (
                <TouchableOpacity
                  key={item.id}
                  style={[styles.checklistItem, isRTL && { flexDirection: 'row-reverse' }]}
                  disabled={isInactive}
                  onPress={() => toggleChecklistItem(item.id)}
                >
                  <View style={styles.checkbox}>
                    {item.completed ? (
                      <CheckSquare size={20} color={colors.primary} />
                    ) : (
                      <Square size={20} color={colors.gray[400]} />
                    )}
                  </View>
                  <View style={styles.checklistItemContent}>
                    <Text style={[
                      styles.checklistItemTitle,
                      item.completed && styles.checklistItemCompleted,
                      isRTL && { textAlign: 'right', marginRight: 8 },
                    ]}>
                      {item.title}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}

              <View style={styles.checklistSummary}>
                <Text style={[styles.checklistSummaryText, isRTL && { textAlign: 'right', marginRight: 8 }]}>
                  {t('entity.animal.tasksCompleted', {
                    completed: checklist.filter(item => item.completed).length,
                    total: checklist.length
                  })}
                </Text>

                <View style={styles.progressBarContainer}>
                  <View
                    style={[
                      styles.progressBar,
                      {
                        width: `${(checklist.filter(item => item.completed).length / checklist.length) * 100}%`,
                        backgroundColor: colors.primary
                      }
                    ]}
                  />
                </View>
              </View>

            </View>

            <View style={styles.checklistActions}>
              <Button
                title={t('entity.animal.saveChecklist')}
                disabled={isInactive}
                onPress={async () => {
                  try {
                    const res: any = await saveAnimalChecklist(currentFarm.id, animal.id, checklist, user.id)
                    // console.log({ res })
                    if (res.status !== -1)
                      Alert.alert('Success', 'Checklist saved successfully.');
                    else
                      Alert.alert('Error', res.message);
                  } catch (error) {
                    console.error('Error saving checklist:', error);
                    Alert.alert('Error', 'Failed to save checklist. Please try again.');
                  }
                  // let updatedAnimal = {
                  //   ...animal,
                  //   checklist: checklist
                  // }
                  // updateAnimal(animal.Id, updatedAnimal)
                  // RNAlert.alert(t('common.success'), t('entity.animal.savedMessage'));
                }}
                style={styles.saveChecklistButton}
              />
            </View>
          </> :
          <>
            <AnimalChecklistList checklists={animalCheckList || []} />
          </>
      }
    </View>
  );

  const renderHealthTab = () => (
    <View style={styles.tabContent}>
      {/* {!healthAnalysis ? (
        <View style={styles.healthAnalysisContainer}>
          <View style={styles.noAnalysisContainer}>
            <AlertCircle size={40} color={colors.gray[400]} />
            <Text style={styles.noAnalysisText}>
              {t('entity.animal.noAnalysis')}
            </Text>
            <Text style={styles.noAnalysisSubtext}>
              {t('entity.animal.noAnalysisHint')}
            </Text>
            <Button
              title={isAnalyzing ? t('entity.animal.analyzing') : t('entity.animal.analyze')}
              onPress={handleAnalyzeHealth}
              disabled={isAnalyzing || isInactive}
              style={styles.analyzeButton}
              leftIcon={
                isAnalyzing ? <ActivityIndicator size="small" color={colors.white} /> : undefined
              }
            />
          </View>
        </View>

      ) : (
        <View style={styles.healthAnalysisContainer}>
          <FlatList
            data={animal?.healthChecks || []}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <View style={styles.card}>
                <Text>Date: {new Date(item.date).toLocaleDateString()}</Text>
                <Text>Temperature: {item.temperature}</Text>
                <Text>Weight: {item.weight}</Text>
                <Text>Respiration: {item.respiration}</Text>
                <Text>Abnormalities: {item.abnormalities ? 'Yes' : 'No'}</Text>
              </View>
            )}
          />
          {/* <View style={styles.healthStatusContainer}>
            <View style={[
              styles.healthStatusBadge,
              {
                backgroundColor:
                  healthAnalysis.healthStatus === 'excellent' ? colors.success + '20' :
                    healthAnalysis.healthStatus === 'good' ? colors.primary + '20' :
                      healthAnalysis.healthStatus === 'fair' ? colors.warning + '20' :
                        healthAnalysis.healthStatus === 'poor' ? colors.danger + '20' :
                          colors.gray[400] + '20'
              }
            ]}>
              <Heart
                size={20}
                color={
                  healthAnalysis.healthStatus === 'excellent' ? colors.success :
                    healthAnalysis.healthStatus === 'good' ? colors.primary :
                      healthAnalysis.healthStatus === 'fair' ? colors.warning :
                        healthAnalysis.healthStatus === 'poor' ? colors.danger :
                          colors.gray[400]
                }
              />
              <Text
                style={[
                  styles.healthStatusText,
                  {
                    color:
                      healthAnalysis.healthStatus === 'excellent' ? colors.success :
                        healthAnalysis.healthStatus === 'good' ? colors.primary :
                          healthAnalysis.healthStatus === 'fair' ? colors.warning :
                            healthAnalysis.healthStatus === 'poor' ? colors.danger :
                              colors.gray[400],
                    textAlign: isRTL ? 'right' : 'left'
                  }
                ]}
              >
                {t(`entity.animal.status_${healthAnalysis.healthStatus}`)}
              </Text>
            </View>
          </View>

          {healthAnalysis.conditions && healthAnalysis.conditions.length > 0 && (
            <View style={styles.healthSection}>
              <Text style={[styles.healthSectionTitle, { textAlign: isRTL ? 'right' : 'left' }]}>
                {t('entity.animal.conditionsTitle')}
              </Text>
              {healthAnalysis.conditions.map((condition: any, index: number) => (
                <View key={index} style={styles.conditionItem}>
                  <AlertCircle size={16} color={colors.danger} style={styles.conditionIcon} />
                  <Text style={[styles.conditionText, { textAlign: isRTL ? 'right' : 'left' }]}>
                    {t(`entity.animal.${condition}`, condition)}
                  </Text>
                </View>
              ))}
            </View>
          )}


          <View style={styles.healthSection}>
            <Text style={[styles.healthSectionTitle, { textAlign: isRTL ? 'right' : 'left' }]}>
              {t('entity.animal.recommendationsTitle')}
            </Text>

            {healthAnalysis.recommendations.map((recommendation: string, index: number) => (
              <View key={index} style={styles.recommendationItem}>
                <Check size={16} color={colors.primary} style={styles.recommendationIcon} />
                <Text style={[styles.recommendationText, { textAlign: isRTL ? 'right' : 'left' }]}>
                  {t(`entity.animal.${recommendation}`, recommendation)}
                </Text>
              </View>
            ))}
          </View>

          <View style={styles.healthSection}>
            <Text style={[styles.healthSectionTitle, { textAlign: isRTL ? 'right' : 'left' }]}>
              {t('entity.animal.detailsTitle')}
            </Text>
            <Text style={[styles.healthDetailsText, { textAlign: isRTL ? 'right' : 'left' }]}>
              {healthAnalysis.details}
            </Text>
          </View> 

          <View style={styles.healthActions}>
            <Button
              title={t('entity.animal.scheduleCheckup')}
              disabled={isInactive}
              onPress={() => {
                router.push({
                  pathname: '/task/create',
                  params: {
                    title: t('entity.animal.vetCheckupTitle', { name: animal.name || animal.species }),
                    category: 'health',
                    priority: 'medium'
                  }
                });
              }}
              style={styles.healthActionButton}
              leftIcon={<Thermometer size={20} color={colors.white} />}
            />

            <Button
              title={t('entity.animal.recordTreatment')}
              disabled={isInactive}
              onPress={() => {
                RNAlert.alert(t('common.featureComingSoon'), t('entity.animal.treatmentSoon'));
              }}
              style={[styles.healthActionButton, { backgroundColor: colors.secondary }]}
              leftIcon={<Pill size={20} color={colors.white} />}
            />
          </View>


          {/* {healthAnalysis.conditions && healthAnalysis.conditions.length > 0 && (
            <View style={styles.healthSection}>
              <Text style={styles.healthSectionTitle}>Conditions</Text>
              {healthAnalysis.conditions.map((condition: any, index: number) => (
                <View key={index} style={styles.conditionItem}>
                  <AlertCircle size={16} color={colors.danger} style={styles.conditionIcon} />
                  <Text style={styles.conditionText}>{condition}</Text>
                </View>
              ))}
            </View>
          )} 

          {/* <View style={styles.healthSection}>
            <Text style={styles.healthSectionTitle}>Recommendations</Text>
            {healthAnalysis.recommendations.map((recommendation: string, index: number) => (
              <View key={index} style={styles.recommendationItem}>
                <Check size={16} color={colors.primary} style={styles.recommendationIcon} />
                <Text style={styles.recommendationText}>{recommendation}</Text>
              </View>
            ))}
          </View> 

          

          {/* <View style={styles.healthActions}>
            <Button
              title="Schedule Checkup"
              disabled={isInactive}
              onPress={() => {
                router.push({
                  pathname: '/task/create',
                  params: {
                    title: `Veterinary checkup for ${animal.name || animal.species}`,
                    category: 'health',
                    priority: 'medium'
                  }
                });
              }}
              style={styles.healthActionButton}
              leftIcon={<Thermometer size={20} color={colors.white} />}
            />

            <Button
              title="Record Treatment"
              disabled={isInactive}
              onPress={() => {
                RNAlert.alert('Feature Coming Soon', 'Treatment recording will be available in the next update.');
              }}
              style={[styles.healthActionButton, { backgroundColor: colors.secondary }]}
              leftIcon={<Pill size={20} color={colors.white} />}
            />
          </View> 
        </View>
      )} */}

      <FlatList
        data={animal?.healthChecks || []}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => <HealthCardAnimal check={item} key={item?.id} />}
        contentContainerStyle={{ padding: 16 }}
      />
    </View>
  );

  const renderQRCodeDetail = () => {
    return (
      <View style={styles.tabContent}>
        <View style={styles.qrCodeSection}>
          <View style={[styles.qrCodeHeader, isRTL && { flexDirection: 'row-reverse' }]}>
            <Text style={styles.sectionTitle}>{t('entity.animal.qrCodeTitle')}</Text>
            <TouchableOpacity
              style={styles.viewQRButton}
              onPress={() => setShowQRModal(true)}
            >
              <QrCode size={16} color={colors.primary} style={[styles.viewQRIcon, isRTL && { marginLeft: 4 }]} />
              <Text style={[styles.viewQRText, isRTL && { textAlign: 'right' }]}>
                {t('entity.animal.viewQRCode')}
              </Text>
            </TouchableOpacity>
          </View>
          <View style={{ alignItems: 'center', marginVertical: 24 }}>
            <QRCodeDisplay
              value={getQRCodeValue()}
              size={200}
              itemType="animal"
              itemName={animal.name || animal.species}
            />
          </View>
          <Text style={[styles.qrCodeDescription, isRTL && { textAlign: 'right' }]}>
            {t('entity.animal.qrCodeDescription')}
          </Text>
        </View>
      </View>
    );
  };



  const renderPhotosSection = () => {
    if (!animal?.id) return null;

    return (
      <EntityGallery
        entityId={animal.id}
        entityType="animal"
        isEditable={!animal.isInactive}
        maxPhotos={15}
        showTimestamp={true}
        showDeleteOption={true}
        gridColumns={2}
        imageSize="large"
        style={styles.photosSection}
        onPhotosChange={(photos) => {
          // Don't update animal state to avoid infinite loops
          // The EntityGallery component handles its own state
          console.log('Photos updated:', photos.length);
        }}
      />
    );
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: animal.name || animal.species,
        }}
      />

      <SafeAreaView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContent}>

          <View style={styles.imageContainer}>
            <Image
              source={{
                uri:
                  animal.image ||
                  animal.photos?.[0]?.url ||
                  'https://images.unsplash.com/photo-1591824438708-ce405f36ba3d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8NHx8Y293fGVufDB8fDB8fA%3D%3D&auto=format&fit=crop&w=800&q=60',
              }}
              style={styles.coverImage}
              resizeMode="cover"
            />

            <View style={styles.imageOverlay}>
              <TouchableOpacity
                style={[
                  styles.viewImagesButton,
                  isRTL && { flexDirection: 'row-reverse' },
                ]}
                onPress={() => {
                  if (animal.photos && animal.photos.length > 0) {
                    setSelectedImage(animal.photos[0].url);
                    setShowImageModal(true);
                  }
                }}
              >
                <MaterialIcons name="photo-library" size={20} color={colors.white} />
                <Text style={styles.viewImagesText}>
                  {t('animal.photos', { count: animal.photos?.length || 0 })}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {isInactive && (
            <View style={styles.inactiveBanner}>
              <MaterialIcons name="archive" size={16} color={colors.white} />
              <Text style={styles.inactiveBannerText}>
                {t('animal.inactiveMessage')}
                {animal.inactiveReason ? `: ${animal.inactiveReason}` : ''}
                {animal.inactiveDate
                  ? ` (${new Date(animal.inactiveDate).toLocaleDateString()})`
                  : ''}
              </Text>
            </View>
          )}

          <View style={styles.contentContainer}>

            <View style={[styles.header, isRTL && { flexDirection: 'row-reverse' }]}>
              <View style={[styles.titleContainer, isRTL && { flexDirection: 'row-reverse' }]}>
                <MaterialIcons
                  name="pets"
                  size={24}
                  color={colors.warning}
                  style={[styles.titleIcon, isRTL && { marginLeft: 8, marginRight: 0 }]}
                />
                <View>
                  <Text style={[styles.title, isRTL && { textAlign: 'right', marginRight: 8 }]}>{animal.name || animal.species}</Text>
                  <Text style={styles.subtitle}>
                    {animal.species}
                    {animal.breed ? ` (${animal.breed})` : ''}
                  </Text>
                </View>
              </View>

              <View style={styles.statusContainer}>
                <View
                  style={[
                    styles.statusBadge,
                    { backgroundColor: getStatusColor(animal.status) },
                  ]}
                >
                  <Text style={styles.statusText}>
                    {t(`entity.animal.${animal.status}`)}
                  </Text>
                </View>
                {/* {isEditing ? (
                  <TouchableOpacity
                    style={[
                      styles.statusDropdownContainer,
                      isRTL && { flexDirection: 'row-reverse' },
                    ]}
                    onPress={() => setShowStatusDropdown(!showStatusDropdown)}
                  >
                    <View
                      style={[
                        styles.statusBadge,
                        { backgroundColor: getStatusColor(status) },
                      ]}
                    >
                      <Text style={styles.statusText}>
                        {t(`entity.animal.${status}`)}
                      </Text>
                      <MaterialIcons name="arrow-drop-down" size={16} color={colors.white} />
                    </View>

                    {showStatusDropdown && (
                      <View style={styles.dropdownMenu}>
                        {['healthy', 'sick', 'pregnant', 'nursing', 'quarantined'].map(
                          (item) => (
                            <TouchableOpacity
                              key={item}
                              style={styles.dropdownItem}
                              onPress={() => {
                                setStatus(item);
                                setShowStatusDropdown(false);
                              }}
                            >
                              <View
                                style={[
                                  styles.statusIndicator,
                                  { backgroundColor: getStatusColor(item) },
                                ]}
                              />
                              <Text style={styles.dropdownItemText}>
                                {t(`entity.animal.${item}`)}
                              </Text>
                            </TouchableOpacity>
                          )
                        )}
                      </View>
                    )}
                  </TouchableOpacity>
                ) : (
                  
                )} */}
              </View>
            </View>

            <View style={[styles.tabsContainer, isRTL && { flexDirection: 'row-reverse' }]}>
              {/*  'gallery',*/}
              {['details','healthChecks', 'pregnancies', 'records' ].map((tabKey) => (
                <TouchableOpacity
                  key={tabKey}
                  style={[styles.tab, activeTab === tabKey && styles.activeTab]}
                  onPress={() => setActiveTab(tabKey)}
                >
                  <Text
                    style={[
                      styles.tabText,
                      activeTab === tabKey && styles.activeTabText,
                    ]}
                  >
                    {tabKey === 'healthChecks' ? t('entity.animal.healthChecks.title') :
                     tabKey === 'pregnancies' ? t('entity.animal.pregnancies.title') :
                     tabKey === 'records' ? t('entity.animal.records.title') :
                     tabKey === 'milkRecords' ? t('entity.animal.milkRecords.title') :
                     t(`entity.animal.${tabKey}`)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {activeTab === 'details' && renderDetailsTab()}
            {/* {activeTab === 'checklist' && renderChecklistTab()}
            {activeTab === 'health' && renderHealthTab()} */}
            {activeTab === 'healthChecks' && (
              <AnimalHealthChecksSection
                animalId={animal.id}
                isEditable={!animal.isInactive}
              />
            )}
             {activeTab === 'pregnancies' && (
              <AnimalPregnanciesSection
                animalId={animal.id}
                isEditable={!animal.isInactive}
              />
            )}
            {activeTab === 'records' && (
              <AnimalRecordsSection
                animalId={animal.id}
                isEditable={!animal.isInactive}
              />
            )}
            {activeTab === 'milkRecords' && (
              <AnimalMilkingSection
                animalId={animal.id}
                isEditable={!animal.isInactive}
              />
            )} 
            {/* {activeTab === 'gallery' && renderPhotosSection()} */}
            {activeTab === "qrCode" && renderQRCodeDetail()}
          </View>
        </ScrollView>

        {/* Delete Confirmation Modal */}
        <Modal
          visible={showDeleteConfirm}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowDeleteConfirm(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.confirmModal}>
              <Text style={styles.confirmTitle}>Delete Animal</Text>
              <Text style={styles.confirmText}>
                Are you sure you want to delete this animal? This action cannot be undone.
              </Text>

              <View style={styles.confirmButtons}>
                <TouchableOpacity
                  style={[styles.confirmButton, styles.cancelButton]}
                  onPress={() => setShowDeleteConfirm(false)}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.confirmButton, styles.deleteButton]}
                  onPress={handleDelete}
                >
                  <Text style={styles.deleteButtonText}>Delete</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>

        {/* Image Viewer Modal */}
        <Modal
          visible={showImageModal}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowImageModal(false)}
        >
          <View style={styles.imageModalOverlay}>
            <TouchableOpacity
              style={styles.closeImageButton}
              onPress={() => setShowImageModal(false)}
            >
              <X size={24} color={colors.white} />
            </TouchableOpacity>

            <Image
              source={{ uri: selectedImage }}
              style={styles.fullImage}
              resizeMode="contain"
            />

            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.imageThumbnails}
            >
              {animal.photos && animal.photos.map((photo: any, index: number) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.thumbnailContainer,
                    selectedImage === photo.url && styles.selectedThumbnail
                  ]}
                  onPress={() => setSelectedImage(photo.url)}
                >
                  <Image
                    source={{ uri: photo.url }}
                    style={styles.thumbnail}
                    resizeMode="cover"
                  />
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </Modal>

        {/* QR Code Modal */}
        <Modal
          visible={showQRModal}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowQRModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.qrModal}>
              <View style={styles.qrModalHeader}>
                <Text style={styles.qrModalTitle}>Animal QR Code</Text>
                <TouchableOpacity
                  style={styles.closeQrButton}
                  onPress={() => setShowQRModal(false)}
                >
                  <X size={20} color={colors.gray[600]} />
                </TouchableOpacity>
              </View>

              <Text style={styles.qrModalDescription}>
                Scan this QR code to quickly access information about {animal.name || animal.species}.
              </Text>

              <View style={styles.qrCodeContainer}>
                <QRCodeDisplay
                  value={getQRCodeValue()}
                  size={200}
                  itemType="animal"
                  itemName={animal.name || animal.species}
                />
              </View>

              <Text style={styles.qrModalInstructions}>
                You can save this QR code to your device or share it with others.
              </Text>
            </View>
          </View>
        </Modal>

        {/* Inactive Status Modal */}
        <InactiveStatusModal
          visible={showInactiveModal}
          onClose={() => setShowInactiveModal(false)}
          onSubmit={handleMarkInactive}
          entityType="animal"
          showCascadeOption={false}
        />
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    marginRight: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: colors.white,
  },
  errorText: {
    fontSize: 18,
    color: colors.gray[700],
    marginBottom: 20,
    textAlign: 'center',
  },
  errorButton: {
    width: 200,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 40,
  },
  imageContainer: {
    position: 'relative',
    height: 200,
  },
  coverImage: {
    width: '100%',
    height: '100%',
  },
  imageOverlay: {
    position: 'absolute',
    flexDirection: 'row',
    bottom: 0,
    right: 0,
    padding: 16,
  },
  viewImagesButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  viewImagesText: {
    color: colors.white,
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  contentContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: -20,
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  titleIcon: {
    marginRight: 12,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: colors.gray[600],
  },
  statusContainer: {
    position: 'relative',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    color: colors.white,
    fontWeight: '500',
    fontSize: 14,
    marginRight: 4,
  },
  statusDropdownContainer: {
    position: 'relative',
  },
  dropdownMenu: {
    position: 'absolute',
    top: '100%',
    right: 0,
    width: 150,
    backgroundColor: colors.white,
    borderRadius: 8,
    shadowColor: colors.gray[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    zIndex: 10,
    marginTop: 4,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[100],
  },
  dropdownItemIcon: {
    marginRight: 8,
  },
  dropdownItemText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  tabsContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
    marginBottom: 16,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  tabText: {
    fontSize: 14,
    color: colors.gray[600],
  },
  activeTabText: {
    color: colors.primary,
    fontWeight: '600',
  },
  tabContent: {
    marginBottom: 16,
  },
  infoSection: {
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
  },
  infoIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  infoLabel: {
    fontSize: 12,
    color: colors.gray[500],
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
  },
  purposeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8, // Optional: requires React Native 0.71+, else use margin
    marginTop: 8,
  },

  purposeBadge: {
    backgroundColor: colors.primary,
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },

  purposeText: {
    fontSize: 14,
    color: colors.white,
  },
  // purposeContainer: {
  //   marginTop: 8,
  // },
  purposeLabel: {
    fontSize: 12,
    color: colors.gray[500],
    marginBottom: 4,
  },
  // purposeBadge: {
  //   backgroundColor: colors.primary,
  //   paddingHorizontal: 12,
  //   paddingVertical: 6,
  //   borderRadius: 16,
  //   alignSelf: 'flex-start',
  // },
  // purposeText: {
  //   color: colors.white,
  //   fontWeight: '500',
  //   fontSize: 14,
  // },
  notesSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  notesText: {
    fontSize: 14,
    lineHeight: 20,
    color: colors.gray[700],
  },
  notesInput: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    color: colors.gray[800],
    minHeight: 100,
    textAlignVertical: 'top',
  },
  photosSection: {
    marginBottom: 20,
  },
  photosContainer: {
    paddingBottom: 16,
  },
  photoItem: {
    marginRight: 12,
    width: 120,
    height: 120,
    borderRadius: 8,
    overflow: 'hidden',
  },
  photoImage: {
    width: '100%',
    height: '100%',
  },
  photoDate: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    color: colors.white,
    fontSize: 10,
    padding: 4,
    textAlign: 'center',
  },
  noPhotosContainer: {
    width: 120,
    height: 120,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  noPhotosText: {
    fontSize: 12,
    color: colors.gray[500],
    marginTop: 8,
    textAlign: 'center',
  },
  addPhotoContainer: {
    marginBottom: 16,
  },
  addPhotoButton: {
    marginTop: 12,
  },
  headerButtons: {
    flexDirection: 'row',
  },
  headerButton: {
    marginLeft: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmModal: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 20,
    width: '80%',
    maxWidth: 400,
  },
  confirmTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  confirmText: {
    fontSize: 14,
    color: colors.gray[700],
    marginBottom: 20,
  },
  confirmButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  confirmButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginLeft: 12,
  },
  cancelButton: {
    backgroundColor: colors.gray[200],
  },
  cancelButtonText: {
    color: colors.gray[800],
    fontWeight: '500',
  },
  deleteButton: {
    backgroundColor: colors.danger,
  },
  deleteButtonText: {
    color: colors.white,
    fontWeight: '500',
  },
  imageModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeImageButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    zIndex: 10,
  },
  fullImage: {
    width: '100%',
    height: '70%',
  },
  imageThumbnails: {
    position: 'absolute',
    bottom: 20,
    paddingHorizontal: 20,
  },
  thumbnailContainer: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginHorizontal: 8,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedThumbnail: {
    borderColor: colors.primary,
  },
  thumbnail: {
    width: '100%',
    height: '100%',
  },
  checklistContainer: {
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  checklistItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  checkbox: {
    marginRight: 12,
  },
  checklistItemContent: {
    flex: 1,
  },
  checklistItemTitle: {
    fontSize: 14,
    color: colors.gray[800],
  },
  checklistItemCompleted: {
    textDecorationLine: 'line-through',
    color: colors.gray[500],
  },
  checklistSummary: {
    marginTop: 16,
  },
  checklistSummaryText: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 8,
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: colors.gray[200],
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 4,
  },
  checklistActions: {
    marginBottom: 20,
  },
  saveChecklistButton: {
    backgroundColor: colors.success,
  },
  healthAnalysisContainer: {
    marginBottom: 20,
  },
  noAnalysisContainer: {
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
  },
  noAnalysisText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginTop: 12,
    marginBottom: 8,
  },
  noAnalysisSubtext: {
    fontSize: 14,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: 20,
  },
  analyzeButton: {
    width: '100%',
    backgroundColor: colors.primary,
  },
  healthStatusContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  healthStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  healthStatusText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  healthSection: {
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  healthSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  conditionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  conditionIcon: {
    marginRight: 8,
  },
  conditionText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  recommendationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  recommendationIcon: {
    marginRight: 8,
  },
  recommendationText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  healthDetailsText: {
    fontSize: 14,
    lineHeight: 20,
    color: colors.gray[700],
  },
  healthActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  healthActionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  qrCodeSection: {
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  qrCodeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  qrCodeDescription: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 8,
  },
  viewQRButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primaryLight,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  viewQRIcon: {
    marginRight: 6,
  },
  viewQRText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
  },
  qrModal: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 24,
    width: '90%',
    maxWidth: 400,
    alignItems: 'center',
  },
  qrModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: 16,
  },
  qrModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  closeQrButton: {
    padding: 4,
  },
  qrModalDescription: {
    fontSize: 14,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: 24,
  },
  qrCodeContainer: {
    marginBottom: 24,
  },
  qrModalInstructions: {
    fontSize: 14,
    color: colors.gray[600],
    textAlign: 'center',
  },
  quickActions: {
    // flexDirection: 'row',
    // justifyContent: 'space-between',
    // marginBottom: 16,
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionsContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },

  quickActionButton: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    padding: 8,
    marginBottom: 12,
  },
  quickActionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  quickActionText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
  },
  disabledActionButton: {
    opacity: 0.5,
  },

  inactiveBanner: {
    backgroundColor: colors.gray[700],
    padding: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  inactiveBannerText: {
    color: colors.white,
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  inactiveDetailsContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.gray[500],
  },
  inactiveDetailsTitle: {
    fontSize: 16,
  },
  inactiveDetailsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  inactiveDetailsValue: {
    fontSize: 14,
    color: colors.gray[700],
  },
  inactiveDetailsLabel: {
    fontSize: 14,
    color: colors.gray[700],
    fontWeight: '500',
    marginRight: 4,
  },
  inactiveImage: {
    width: 120,
    height: 120,
    borderRadius: 8,
    marginTop: 8,
  },
  inactiveImageContainer: {
    marginTop: 8,
    alignItems: 'flex-start',
  },
  // qrCodeSection: {
  //   backgroundColor: colors.gray[50],
  //   borderRadius: 12,
  //   padding: 16,
  //   marginBottom: 20,
  // },
  // qrCodeHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   marginBottom: 8,
  // },
  // qrCodeDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   marginBottom: 8,
  // },
  // viewQRButton: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   backgroundColor: colors.primaryLight,
  //   paddingHorizontal: 12,
  //   paddingVertical: 6,
  //   borderRadius: 8,
  // },
  // viewQRIcon: {
  //   marginRight: 6,
  // },
  // viewQRText: {
  //   fontSize: 14,
  //   color: colors.primary,
  //   fontWeight: '500',
  // },
  // qrModal: {
  //   backgroundColor: colors.white,
  //   borderRadius: 16,
  //   padding: 24,
  //   width: '90%',
  //   maxWidth: 400,
  //   alignItems: 'center',
  // },
  // qrModalHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   width: '100%',
  //   marginBottom: 16,
  // },
  // qrModalTitle: {
  //   fontSize: 18,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // closeQrButton: {
  //   padding: 4,
  // },
  // qrModalDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  //   marginBottom: 24,
  // },
  // qrCodeContainer: {
  //   marginBottom: 24,
  // },
  // qrModalInstructions: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  // },
  // quickActions: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   marginBottom: 16,
  // },
  // quickActionButton: {
  //   flex: 1,
  //   marginHorizontal: 8,
  //   backgroundColor: colors.gray[100],
  //   borderRadius: 8,
  //   paddingVertical: 12,
  //   alignItems: 'center',
  //   justifyContent: 'center',
  // },
  // quickActionIcon: {
  //   width: 40,
  //   height: 40,
  //   borderRadius: 20,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   marginRight: 12,
  // },
  // quickActionText: {
  //   fontSize: 16,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // qrCodeSection: {
  //   backgroundColor: colors.gray[50],
  //   borderRadius: 12,
  //   padding: 16,
  //   marginBottom: 20,
  // },
  // qrCodeHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   marginBottom: 8,
  // },
  // qrCodeDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   marginBottom: 8,
  // },
  // viewQRButton: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   backgroundColor: colors.primaryLight,
  //   paddingHorizontal: 12,
  //   paddingVertical: 6,
  //   borderRadius: 8,
  // },
  // viewQRIcon: {
  //   marginRight: 6,
  // },
  // viewQRText: {
  //   fontSize: 14,
  //   color: colors.primary,
  //   fontWeight: '500',
  // },
  // qrModal: {
  //   backgroundColor: colors.white,
  //   borderRadius: 16,
  //   padding: 24,
  //   width: '90%',
  //   maxWidth: 400,
  //   alignItems: 'center',
  // },
  // qrModalHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   width: '100%',
  //   marginBottom: 16,
  // },
  // qrModalTitle: {
  //   fontSize: 18,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // closeQrButton: {
  //   padding: 4,
  // },
  // qrModalDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  //   marginBottom: 24,
  // },
  // qrCodeContainer: {
  //   marginBottom: 24,
  // },
  // qrModalInstructions: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  // },
  // quickActions: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   marginBottom: 16,
  // },
  // quickActionButton: {
  //   flex: 1,
  //   marginHorizontal: 8,
  //   backgroundColor: colors.gray[100],
  //   borderRadius: 8,
  //   paddingVertical: 12,
  //   alignItems: 'center',
  //   justifyContent: 'center',
  // },
  // quickActionIcon: {
  //   width: 40,
  //   height: 40,
  //   borderRadius: 20,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   marginRight: 12,
  // },
  // quickActionText: {
  //   fontSize: 16,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // qrCodeSection: {
  //   backgroundColor: colors.gray[50],
  //   borderRadius: 12,
  //   padding: 16,
  //   marginBottom: 20,
  // },
  // qrCodeHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   marginBottom: 8,
  // },
  // qrCodeDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   marginBottom: 8,
  // },
  // viewQRButton: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   backgroundColor: colors.primaryLight,
  //   paddingHorizontal: 12,
  //   paddingVertical: 6,
  //   borderRadius: 8,
  // },
  // viewQRIcon: {
  //   marginRight: 6,
  // },
  // viewQRText: {
  //   fontSize: 14,
  //   color: colors.primary,
  //   fontWeight: '500',
  // },
  // qrModal: {
  //   backgroundColor: colors.white,
  //   borderRadius: 16,
  //   padding: 24,
  //   width: '90%',
  //   maxWidth: 400,
  //   alignItems: 'center',
  // },
  // qrModalHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   width: '100%',
  //   marginBottom: 16,
  // },
  // qrModalTitle: {
  //   fontSize: 18,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // closeQrButton: {
  //   padding: 4,
  // },
  // qrModalDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  //   marginBottom: 24,
  // },
  // qrCodeContainer: {
  //   marginBottom: 24,
  // },
  // qrModalInstructions: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  // },
  // quickActions: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   marginBottom: 16,
  // },
  // quickActionButton: {
  //   flex: 1,
  //   marginHorizontal: 8,
  //   backgroundColor: colors.gray[100],
  //   borderRadius: 8,
  //   paddingVertical: 12,
  //   alignItems: 'center',
  //   justifyContent: 'center',
  // },
  // quickActionIcon: {
  //   width: 40,
  //   height: 40,
  //   borderRadius: 20,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   marginRight: 12,
  // },
  // quickActionText: {
  //   fontSize: 16,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // qrCodeSection: {
  //   backgroundColor: colors.gray[50],
  //   borderRadius: 12,
  //   padding: 16,
  //   marginBottom: 20,
  // },
  // qrCodeHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   marginBottom: 8,
  // },
  // qrCodeDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   marginBottom: 8,
  // },
  // viewQRButton: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   backgroundColor: colors.primaryLight,
  //   paddingHorizontal: 12,
  //   paddingVertical: 6,
  //   borderRadius: 8,
  // },
  // viewQRIcon: {
  //   marginRight: 6,
  // },
  // viewQRText: {
  //   fontSize: 14,
  //   color: colors.primary,
  //   fontWeight: '500',
  // },
  // qrModal: {
  //   backgroundColor: colors.white,
  //   borderRadius: 16,
  //   padding: 24,
  //   width: '90%',
  //   maxWidth: 400,
  //   alignItems: 'center',
  // },
  // qrModalHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   width: '100%',
  //   marginBottom: 16,
  // },
  // qrModalTitle: {
  //   fontSize: 18,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // closeQrButton: {
  //   padding: 4,
  // },
  // qrModalDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  //   marginBottom: 24,
  // },
  // qrCodeContainer: {
  //   marginBottom: 24,
  // },
  // qrModalInstructions: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  // },
  // quickActions: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   marginBottom: 16,
  // },
  // quickActionButton: {
  //   flex: 1,
  //   marginHorizontal: 8,
  //   backgroundColor: colors.gray[100],
  //   borderRadius: 8,
  //   paddingVertical: 12,
  //   alignItems: 'center',
  //   justifyContent: 'center',
  // },
  // quickActionIcon: {
  //   width: 40,
  //   height: 40,
  //   borderRadius: 20,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   marginRight: 12,
  // },
  // quickActionText: {
  //   fontSize: 16,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // qrCodeSection: {
  //   backgroundColor: colors.gray[50],
  //   borderRadius: 12,
  //   padding: 16,
  //   marginBottom: 20,
  // },
  // qrCodeHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   marginBottom: 8,
  // },
  // qrCodeDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   marginBottom: 8,
  // },
  // viewQRButton: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   backgroundColor: colors.primaryLight,
  //   paddingHorizontal: 12,
  //   paddingVertical: 6,
  //   borderRadius: 8,
  // },
  // viewQRIcon: {
  //   marginRight: 6,
  // },
  // viewQRText: {
  //   fontSize: 14,
  //   color: colors.primary,
  //   fontWeight: '500',
  // },
  // qrModal: {
  //   backgroundColor: colors.white,
  //   borderRadius: 16,
  //   padding: 24,
  //   width: '90%',
  //   maxWidth: 400,
  //   alignItems: 'center',
  // },
  // qrModalHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   width: '100%',
  //   marginBottom: 16,
  // },
  // qrModalTitle: {
  //   fontSize: 18,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // closeQrButton: {
  //   padding: 4,
  // },
  // qrModalDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  //   marginBottom: 24,
  // },
  // qrCodeContainer: {
  //   marginBottom: 24,
  // },
  // qrModalInstructions: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  // },
  // quickActions: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   marginBottom: 16,
  // },
  // quickActionButton: {
  //   flex: 1,
  //   marginHorizontal: 8,
  //   backgroundColor: colors.gray[100],
  //   borderRadius: 8,
  //   paddingVertical: 12,
  //   alignItems: 'center',
  //   justifyContent: 'center',
  // },
  // quickActionIcon: {
  //   width: 40,
  //   height: 40,
  //   borderRadius: 20,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   marginRight: 12,
  // },
  // quickActionText: {
  //   fontSize: 16,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // qrCodeSection: {
  //   backgroundColor: colors.gray[50],
  //   borderRadius: 12,
  //   padding: 16,
  //   marginBottom: 20,
  // },
  // qrCodeHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   marginBottom: 8,
  // },
  // qrCodeDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   marginBottom: 8,
  // },
  // viewQRButton: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   backgroundColor: colors.primaryLight,
  //   paddingHorizontal: 12,
  //   paddingVertical: 6,
  //   borderRadius: 8,
  // },
  // viewQRIcon: {
  //   marginRight: 6,
  // },
  // viewQRText: {
  //   fontSize: 14,
  //   color: colors.primary,
  //   fontWeight: '500',
  // },
  // qrModal: {
  //   backgroundColor: colors.white,
  //   borderRadius: 16,
  //   padding: 24,
  //   width: '90%',
  //   maxWidth: 400,
  //   alignItems: 'center',
  // },
  // qrModalHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   width: '100%',
  //   marginBottom: 16,
  // },
  // qrModalTitle: {
  //   fontSize: 18,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // closeQrButton: {
  //   padding: 4,
  // },
  // qrModalDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  //   marginBottom: 24,
  // },
  // qrCodeContainer: {
  //   marginBottom: 24,
  // },
  // qrModalInstructions: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  // },
  // quickActions: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   marginBottom: 16,
  // },
  // quickActionButton: {
  //   flex: 1,
  //   marginHorizontal: 8,
  //   backgroundColor: colors.gray[100],
  //   borderRadius: 8,
  //   paddingVertical: 12,
  //   alignItems: 'center',
  //   justifyContent: 'center',
  // },
  // quickActionIcon: {
  //   width: 40,
  //   height: 40,
  //   borderRadius: 20,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   marginRight: 12,
  // },
  // quickActionText: {
  //   fontSize: 16,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // qrCodeSection: {
  //   backgroundColor: colors.gray[50],
  //   borderRadius: 12,
  //   padding: 16,
  //   marginBottom: 20,
  // },
  // qrCodeHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   marginBottom: 8,
  // },
  // qrCodeDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   marginBottom: 8,
  // },
  // viewQRButton: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   backgroundColor: colors.primaryLight,
  //   paddingHorizontal: 12,
  //   paddingVertical: 6,
  //   borderRadius: 8,
  // },
  // viewQRIcon: {
  //   marginRight: 6,
  // },
  // viewQRText: {
  //   fontSize: 14,
  //   color: colors.primary,
  //   fontWeight: '500',
  // },
  // qrModal: {
  //   backgroundColor: colors.white,
  //   borderRadius: 16,
  //   padding: 24,
  //   width: '90%',
  //   maxWidth: 400,
  //   alignItems: 'center',
  // },
  // qrModalHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   width: '100%',
  //   marginBottom: 16,
  // },
  // qrModalTitle: {
  //   fontSize: 18,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // closeQrButton: {
  //   padding: 4,
  // },
  // qrModalDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  //   marginBottom: 24,
  // },
  // qrCodeContainer: {
  //   marginBottom: 24,
  // },
  // qrModalInstructions: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  // },
  // quickActions: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   marginBottom: 16,
  // },
  // quickActionButton: {
  //   flex: 1,
  //   marginHorizontal: 8,
  //   backgroundColor: colors.gray[100],
  //   borderRadius: 8,
  //   paddingVertical: 12,
  //   alignItems: 'center',
  //   justifyContent: 'center',
  // },
  // quickActionIcon: {
  //   width: 40,
  //   height: 40,
  //   borderRadius: 20,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   marginRight: 12,
  // },
  // quickActionText: {
  //   fontSize: 16,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // qrCodeSection: {
  //   backgroundColor: colors.gray[50],
  //   borderRadius: 12,
  //   padding: 16,
  //   marginBottom: 20,
  // },
  // qrCodeHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   marginBottom: 8,
  // },
  // qrCodeDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   marginBottom: 8,
  // },
  // viewQRButton: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   backgroundColor: colors.primaryLight,
  //   paddingHorizontal: 12,
  //   paddingVertical: 6,
  //   borderRadius: 8,
  // },
  // viewQRIcon: {
  //   marginRight: 6,
  // },
  // viewQRText: {
  //   fontSize: 14,
  //   color: colors.primary,
  //   fontWeight: '500',
  // },
  // qrModal: {
  //   backgroundColor: colors.white,
  //   borderRadius: 16,
  //   padding: 24,
  //   width: '90%',
  //   maxWidth: 400,
  //   alignItems: 'center',
  // },
  // qrModalHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   width: '100%',
  //   marginBottom: 16,
  // },
  // qrModalTitle: {
  //   fontSize: 18,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // closeQrButton: {
  //   padding: 4,
  // },
  // qrModalDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  //   marginBottom: 24,
  // },
  // qrCodeContainer: {
  //   marginBottom: 24,
  // },
  // qrModalInstructions: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  // },
  // quickActions: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   marginBottom: 16,
  // },
  // quickActionButton: {
  //   flex: 1,
  //   marginHorizontal: 8,
  //   backgroundColor: colors.gray[100],
  //   borderRadius: 8,
  //   paddingVertical: 12,
  //   alignItems: 'center',
  //   justifyContent: 'center',
  // },
  // quickActionIcon: {
  //   width: 40,
  //   height: 40,
  //   borderRadius: 20,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   marginRight: 12,
  // },
  // quickActionText: {
  //   fontSize: 16,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // qrCodeSection: {
  //   backgroundColor: colors.gray[50],
  //   borderRadius: 12,
  //   padding: 16,
  //   marginBottom: 20,
  // },
  // qrCodeHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   marginBottom: 8,
  // },
  // qrCodeDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   marginBottom: 8,
  // },
  // viewQRButton: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   backgroundColor: colors.primaryLight,
  //   paddingHorizontal: 12,
  //   padding
  //   marginBottom: 16,
  // },
  // qrModalTitle: {
  //   fontSize: 18,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // closeQrButton: {
  //   padding: 4,
  // },
  // qrModalDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  //   marginBottom: 24,
  // },
  // qrCodeContainer: {
  //   marginBottom: 24,
  // },
  // qrModalInstructions: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  // },
  // quickActions: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   marginBottom: 16,
  // },
  // quickActionButton: {
  //   flex: 1,
  //   marginHorizontal: 8,
  //   backgroundColor: colors.gray[100],
  //   borderRadius: 8,
  //   paddingVertical: 12,
  //   alignItems: 'center',
  //   justifyContent: 'center',
  // },
  // quickActionIcon: {
  //   width: 40,
  //   height: 40,
  //   borderRadius: 20,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   marginRight: 12,
  // },
  // quickActionText: {
  //   fontSize: 16,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // qrCodeSection: {
  //   backgroundColor: colors.gray[50],
  //   borderRadius: 12,
  //   padding: 16,
  //   marginBottom: 20,
  // },
  // qrCodeHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   marginBottom: 8,
  // },
  // qrCodeDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   marginBottom: 8,
  // },
  // viewQRButton: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   backgroundColor: colors.primaryLight,
  //   paddingHorizontal: 12,
  //   paddingVertical: 6,
  //   borderRadius: 8,
  // },
  // viewQRIcon: {
  //   marginRight: 6,
  // },
  // viewQRText: {
  //   fontSize: 14,
  //   color: colors.primary,
  //   fontWeight: '500',
  // },
  // qrModal: {
  //   backgroundColor: colors.white,
  //   borderRadius: 16,
  //   padding: 24,
  //   width: '90%',
  //   maxWidth: 400,
  //   alignItems: 'center',
  // },
  // qrModalHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   width: '100%',
  //   marginBottom: 16,
  // },
  // qrModalTitle: {
  //   fontSize: 18,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // closeQrButton: {
  //   padding: 4,
  // },
  // qrModalDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  //   marginBottom: 24,
  // },
  // qrCodeContainer: {
  //   marginBottom: 24,
  // },
  // qrModalInstructions: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  // },
  // quickActions: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   marginBottom: 16,
  // },
  // quickActionButton: {
  //   flex: 1,
  //   marginHorizontal: 8,
  //   backgroundColor: colors.gray[100],
  //   borderRadius: 8,
  //   paddingVertical: 12,
  //   alignItems: 'center',
  //   justifyContent: 'center',
  // },
  // quickActionIcon: {
  //   width: 40,
  //   height: 40,
  //   borderRadius: 20,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   marginRight: 12,
  // },
  // quickActionText: {
  //   fontSize: 16,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // qrCodeSection: {
  //   backgroundColor: colors.gray[50],
  //   borderRadius: 12,
  //   padding: 16,
  //   marginBottom: 20,
  // },
  // qrCodeHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   marginBottom: 8,
  // },
  // qrCodeDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   marginBottom: 8,
  // },
  // viewQRButton: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   backgroundColor: colors.primaryLight,
  //   paddingHorizontal: 12,
  //   padding
  //   marginBottom: 16,
  // },
  // qrModalTitle: {
  //   fontSize: 18,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // closeQrButton: {
  //   padding: 4,
  // },
  // qrModalDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  //   marginBottom: 24,
  // },
  // qrCodeContainer: {
  //   marginBottom: 24,
  // },
  // qrModalInstructions: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  // },
  // quickActions: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   marginBottom: 16,
  // },
  // quickActionButton: {
  //   flex: 1,
  //   marginHorizontal: 8,
  //   backgroundColor: colors.gray[100],
  //   borderRadius: 8,
  //   paddingVertical: 12,
  //   alignItems: 'center',
  //   justifyContent: 'center',
  // },
  // quickActionIcon: {
  //   width: 40,
  //   height: 40,
  //   borderRadius: 20,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   marginRight: 12,
  // },
  // quickActionText: {
  //   fontSize: 16,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // qrCodeSection: {
  //   backgroundColor: colors.gray[50],
  //   borderRadius: 12,
  //   padding: 16,
  //   marginBottom: 20,
  // },
  // qrCodeHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   marginBottom: 8,
  // },
  // qrCodeDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   marginBottom: 8,
  // },
  // viewQRButton: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   backgroundColor: colors.primaryLight,
  //   paddingHorizontal: 12,
  //   paddingVertical: 6,
  //   borderRadius: 8,
  // },
  // viewQRIcon: {
  //   marginRight: 6,
  // },
  // viewQRText: {
  //   fontSize: 14,
  //   color: colors.primary,
  //   fontWeight: '500',
  // },
  // qrModal: {
  //   backgroundColor: colors.white,
  //   borderRadius: 16,
  //   padding: 24,
  //   width: '90%',
  //   maxWidth: 400,
  //   alignItems: 'center',
  // },
  // qrModalHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   width: '100%',
  //   marginBottom: 16,
  // },
  // qrModalTitle: {
  //   fontSize: 18,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // closeQrButton: {
  //   padding: 4,
  // },
  // qrModalDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  //   marginBottom: 24,
  // },
  // qrCodeContainer: {
  //   marginBottom: 24,
  // },
  // qrModalInstructions: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  // },
  // quickActions: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   marginBottom: 16,
  // },
  // quickActionButton: {
  //   flex: 1,
  //   marginHorizontal: 8,
  //   backgroundColor: colors.gray[100],
  //   borderRadius: 8,
  //   paddingVertical: 12,
  //   alignItems: 'center',
  //   justifyContent: 'center',
  // },
  // quickActionIcon: {
  //   width: 40,
  //   height: 40,
  //   borderRadius: 20,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   marginRight: 12,
  // },
  // quickActionText: {
  //   fontSize: 16,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // qrCodeSection: {
  //   backgroundColor: colors.gray[50],
  //   borderRadius: 12,
  //   padding: 16,
  //   marginBottom: 20,
  // },
  // qrCodeHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   marginBottom: 8,
  // },
  // qrCodeDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   marginBottom: 8,
  // },
  // viewQRButton: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   backgroundColor: colors.primaryLight,
  //   paddingHorizontal: 12,
  //   paddingVertical
  // qrModalTitle: {
  //   fontSize: 18,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // closeQrButton: {
  //   padding: 4,
  // },
  // qrModalDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  //   marginBottom: 24,
  // },
  // qrCodeContainer: {
  //   marginBottom: 24,
  // },
  // qrModalInstructions: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  // },
  // quickActions: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   marginBottom: 16,
  // },
  // quickActionButton: {
  //   flex: 1,
  //   marginHorizontal: 8,
  //   backgroundColor: colors.gray[100],
  //   borderRadius: 8,
  //   paddingVertical: 12,
  //   alignItems: 'center',
  //   justifyContent: 'center',
  // },
  // quickActionIcon: {
  //   width: 40,
  //   height: 40,
  //   borderRadius: 20,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   marginRight: 12,
  // },
  // quickActionText: {
  //   fontSize: 16,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // qrCodeSection: {
  //   backgroundColor: colors.gray[50],
  //   borderRadius: 12,
  //   padding: 16,
  //   marginBottom: 20,
  // },
  // qrCodeHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   marginBottom: 8,
  // },
  // qrCodeDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   marginBottom: 8,
  // },
  // viewQRButton: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   backgroundColor: colors.primaryLight,
  //   paddingHorizontal: 12,
  //   paddingVertical: 6,
  //   borderRadius: 8,
  // },
  // viewQRIcon: {
  //   marginRight: 6,
  // },
  // viewQRText: {
  //   fontSize: 14,
  //   color: colors.primary,
  //   fontWeight: '500',
  // },
  // qrModal: {
  //   backgroundColor: colors.white,
  //   borderRadius: 16,
  //   padding: 24,
  //   width: '90%',
  //   maxWidth: 400,
  //   alignItems: 'center',
  // },
  // qrModalHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   width: '100%',
  //   marginBottom: 16,
  // },
  // qrModalTitle: {
  //   fontSize: 18,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // closeQrButton: {
  //   padding: 4,
  // },
  // qrModalDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  //   marginBottom: 24,
  // },
  // qrCodeContainer: {
  //   marginBottom: 24,
  // },
  // qrModalInstructions: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  // },
  // quickActions: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   marginBottom: 16,
  // },
  // quickActionButton: {
  //   flex: 1,
  //   marginHorizontal: 8,
  //   backgroundColor: colors.gray[100],
  //   borderRadius: 8,
  //   paddingVertical: 12,
  //   alignItems: 'center',
  //   justifyContent: 'center',
  // },
  // quickActionIcon: {
  //   width: 40,
  //   height: 40,
  //   borderRadius: 20,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   marginRight: 12,
  // },
  // quickActionText: {
  //   fontSize: 16,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // qrCodeSection: {
  //   backgroundColor: colors.gray[50],
  //   borderRadius: 12,
  //   padding: 16,
  //   marginBottom: 20,
  // },
  // qrCodeHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   marginBottom: 8,
  // },
  // qrCodeDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   marginBottom: 8,
  // },
  // viewQRButton: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   backgroundColor: colors.primaryLight,
  //   paddingHorizontal: 12,
  //   paddingVertical: 6,
  //   borderRadius: 8,
  // },
  // viewQRIcon: {
  //   marginRight: 6,
  // },
  // viewQRText: {
  //   fontSize: 14,
  //   color: colors.primary,
  //   fontWeight: '500',
  // },
  // qrModal: {
  //   backgroundColor: colors.white,
  //   borderRadius: 16,
  //   padding: 24,
  //   width: '90%',
  //   maxWidth: 400,
  //   alignItems: 'center',
  // },
  // qrModalHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   width: '100%',
  //   marginBottom: 16,
  // },
  // qrModalTitle: {
  //   fontSize: 18,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // closeQrButton: {
  //   padding: 4,
  // },
  // qrModalDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  //   marginBottom: 24,
  // },
  // qrCodeContainer: {
  //   marginBottom: 24,
  // },
  // qrModalInstructions: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  // },
  // quickActions: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   marginBottom: 16,
  // },
  // quickActionButton: {
  //   flex: 1,
  //   marginHorizontal: 8,
  //   backgroundColor: colors.gray[100],
  //   borderRadius: 8,
  //   paddingVertical: 12,
  //   alignItems: 'center',
  //   justifyContent: 'center',
  // },
  // quickActionIcon: {
  //   width: 40,
  //   height: 40,
  //   borderRadius: 20,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   marginRight: 12,
  // },
  // quickActionText: {
  //   fontSize: 16,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // qrCodeSection: {
  //   backgroundColor: colors.gray[50],
  //   borderRadius: 12,
  //   padding: 16,
  //   marginBottom: 20,
  // },
  // qrCodeHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   marginBottom: 8,
  // },
  // qrCodeDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   marginBottom: 8,
  // },
  // viewQRButton: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   backgroundColor: colors.primaryLight,
  //   paddingHorizontal: 12,
  //   paddingVertical: 6,
  //   borderRadius: 8,
  // },
  // viewQRIcon: {
  //   marginRight: 6,
  // },
  // viewQRText: {
  //   fontSize: 14,
  //   color: colors.primary,
  //   fontWeight: '500',
  // },
  // qrModal: {
  //   backgroundColor: colors.white,
  //   borderRadius: 16,
  //   padding: 24,
  //   width: '90%',
  //   maxWidth: 400,
  //   alignItems: 'center',
  // },
  // qrModalHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   width: '100%',
  //   marginBottom: 16,
  // },
  // qrModalTitle: {
  //   fontSize: 18,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // closeQrButton: {
  //   padding: 4,
  // },
  // qrModalDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  //   marginBottom: 24,
  // },
  // qrCodeContainer: {
  //   marginBottom: 24,
  // },
  // qrModalInstructions: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  // },
  // quickActions: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   marginBottom: 16,
  // },
  // quickActionButton: {
  //   flex: 1,
  //   marginHorizontal: 8,
  //   backgroundColor: colors.gray[100],
  //   borderRadius: 8,
  //   paddingVertical: 12,
  //   alignItems: 'center',
  //   justifyContent: 'center',
  // },
  // quickActionIcon: {
  //   width: 40,
  //   height: 40,
  //   borderRadius: 20,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   marginRight: 12,
  // },
  // quickActionText: {
  //   fontSize: 16,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // qrCodeSection: {
  //   backgroundColor: colors.gray[50],
  //   borderRadius: 12,
  //   padding: 16,
  //   marginBottom: 20,
  // },
  // qrCodeHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   marginBottom: 8,
  // },
  // qrCodeDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   marginBottom: 8,
  // },
  // viewQRButton: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   backgroundColor: colors.primaryLight,
  //   paddingHorizontal: 12,
  //   paddingVertical: 6,
  //   borderRadius: 8,
  // },
  // viewQRIcon: {
  //   marginRight: 6,
  // },
  // viewQRText: {
  //   fontSize: 14,
  //   color: colors.primary,
  //   fontWeight: '500',
  // },
  // qrModal: {
  //   backgroundColor: colors.white,
  //   borderRadius: 16,
  //   padding: 24,
  //   width: '90%',
  //   maxWidth: 400,
  //   alignItems: 'center',
  // },
  // qrModalHeader: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   width: '100%',
  //   marginBottom: 16,
  // },
  // qrModalTitle: {
  //   fontSize: 18,
  //   fontWeight: '600',
  //   color: colors.gray[800],
  // },
  // closeQrButton: {
  //   padding: 4,
  // },
  // qrModalDescription: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  //   marginBottom: 24,
  // },
  // qrCodeContainer: {
  //   marginBottom: 24,
  // },
  // qrModalInstructions: {
  //   fontSize: 14,
  //   color: colors.gray[600],
  //   textAlign: 'center',
  // },
  // quickActions: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   marginBottom: 16,
  // },
  // quickActionButton: {
  //   flex: 1,
  //   marginHorizontal: 8,
  //   backgroundColor: colors.gray[100],
  //   borderRadius: 8,
  //   paddingVertical: 12,
  //   alignItems: 'center',
  //   justifyContent: 'center',
  // },
  // quickActionIcon: {
  //   width: 40,
  //   height: 40,
  //   borderRadius: 20,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   marginRight: 12,
  // },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
    paddingHorizontal: 4,
  },
  statCard: {
    flex: 1,
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 4,
    alignItems: 'center',
    shadowColor: colors.gray[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.gray[600],
    textAlign: 'center',
  },
});



