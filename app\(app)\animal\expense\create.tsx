import React, { useState } from 'react';
import { View, Text, TextInput, Button, StyleSheet, Alert } from 'react-native';
import { addDoc, collection } from 'firebase/firestore';
import { useLocalSearchParams } from 'expo-router';
import Input from '@/components/Input';
// import { db } from '../firebaseConfig';
export default function AnimalDetailsScreen() {
  const { Id } =useLocalSearchParams()

  const [costType, setCostType] = useState('');
  const [amount, setAmount] = useState('');
  const [notes, setNotes] = useState('');

  const handleAddExpense = async () => {
    if (!costType || !amount) {
      Alert.alert('Please enter both cost type and amount');
      return;
    }

    // try {
    //   await addDoc(collection(db, 'animal_costs'), {
    //     animal_id: Id,
    //     cost_type: costType,
    //     amount: parseFloat(amount),
    //     date: new Date().toISOString(),
    //     notes,
    //   });

    //   Alert.alert('Expense added successfully!');
    //   navigation.goBack();
    // } catch (error) {
    //   console.error('Error adding expense:', error);
    //   Alert.alert('Failed to add expense');
    // }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>Cost Type (e.g. Feed, Medicine)</Text>
      <Input
        style={styles.input}
        value={costType}
        onChangeText={setCostType}
        placeholder="e.g. Feed"
      />

      <Text style={styles.label}>Amount (₹)</Text>
      <Input
        style={styles.input}
        value={amount}
        onChangeText={setAmount}
        keyboardType="numeric"
        placeholder="e.g. 150"
      />

      <Text style={styles.label}>Notes (optional)</Text>
      <Input
        style={styles.input}
        value={notes}
        onChangeText={setNotes}
        placeholder="e.g. 5kg feed"
      />

      <Button title="Save Expense" onPress={handleAddExpense} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    flex: 1,
    backgroundColor: '#fff',
  },
  label: {
    fontWeight: '600',
    marginTop: 16,
  },
  input: {
    borderWidth: 1,
    padding: 10,
    borderRadius: 8,
    marginTop: 8,
    borderColor: '#ccc',
  },
});

// export default AddAnimalExpenseScreen;
