import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

export type AIModel = 'openai' | 'openrouter';

interface SettingsState {
  // AI Model Selection
  selectedAIModel: AIModel;
  
  // API Keys
  openaiApiKey: string;
  openrouterApiKey: string;
  
  // Actions
  setSelectedAIModel: (model: AIModel) => void;
  setOpenAIApiKey: (key: string) => void;
  setOpenRouterApiKey: (key: string) => void;
  
  // Getters
  getCurrentApiKey: () => string;
  getCurrentBaseUrl: () => string;
}

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set, get) => ({
      // Default to OpenRouter as requested
      selectedAIModel: 'openrouter',
      
      // API Keys
      openaiApiKey: '********************************************************************************************************************************************************************',
      openrouterApiKey: '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',
      
      // Actions
      setSelectedAIModel: (model: AIModel) => {
        set({ selectedAIModel: model });
      },
      
      setOpenAIApiKey: (key: string) => {
        set({ openaiApiKey: key });
      },
      
      setOpenRouterApiKey: (key: string) => {
        set({ openrouterApiKey: key });
      },
      
      // Getters
      getCurrentApiKey: () => {
        const state = get();
        return state.selectedAIModel === 'openai' 
          ? state.openaiApiKey 
          : state.openrouterApiKey;
      },
      
      getCurrentBaseUrl: () => {
        const state = get();
        return state.selectedAIModel === 'openai'
          ? 'https://api.openai.com/v1'
          : 'https://openrouter.ai/api/v1';
      },
    }),
    {
      name: 'settings-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);
