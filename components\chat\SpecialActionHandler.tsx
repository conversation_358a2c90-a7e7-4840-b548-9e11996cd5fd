import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';
import { useFarmStore } from '@/store/farm-store';
import { 
  Sprout, 
  TreePine, 
  Tractor,
  Scissors
} from 'lucide-react-native';

interface SpecialActionHandlerProps {
  message: string;
  onActionTaken?: (action: string) => void;
}

export const SpecialActionHandler: React.FC<SpecialActionHandlerProps> = ({
  message,
  onActionTaken,
}) => {
  const { t, isRTL } = useTranslation();
  const { currentFarm, fields, gardens } = useFarmStore();

  const lowerMessage = message.toLowerCase();

  const handleHarvestFromField = () => {
    // Find fields with active crops
    const fieldsWithCrops = fields.filter(field => field.activeCropId);
    
    if (fieldsWithCrops.length === 0) {
      Alert.alert(
        t('field.noActiveCrops'),
        t('field.noActiveCropsMessage'),
        [{ text: t('common.ok') }]
      );
      return;
    }

    if (fieldsWithCrops.length === 1) {
      // Navigate directly to harvest screen for the single field
      router.push(`/field/harvest?fieldId=${fieldsWithCrops[0].id}&cropId=${fieldsWithCrops[0].activeCropId}`);
    } else {
      // Navigate to field list to choose which field to harvest
      router.push('/field');
    }
    
    onActionTaken?.('harvest_crop_from_field');
  };

  const handleAddCropToField = () => {
    // Find available fields (without active crops)
    const availableFields = fields.filter(field => !field.activeCropId && field.status === 'active');
    
    if (availableFields.length === 0) {
      Alert.alert(
        t('field.noAvailableFields'),
        t('field.noAvailableFieldsMessage'),
        [{ text: t('common.ok') }]
      );
      return;
    }

    if (availableFields.length === 1) {
      // Navigate directly to add crop screen for the single field
      router.push(`/field/add-crop?fieldId=${availableFields[0].id}`);
    } else {
      // Navigate to field list to choose which field to plant
      router.push('/field');
    }
    
    onActionTaken?.('add_crop_to_field');
  };

  const handleAddPlantToGarden = () => {
    // Find active gardens
    const activeGardens = gardens.filter(garden => garden.status === 'active');
    
    if (activeGardens.length === 0) {
      Alert.alert(
        t('garden.noActiveGardens'),
        t('garden.noActiveGardensMessage'),
        [{ text: t('common.ok') }]
      );
      return;
    }

    // Navigate to plant creation with garden context
    router.push('/plant/create');
    
    onActionTaken?.('add_plant_to_garden');
  };

  const handleLoadEquipment = (category?: string) => {
    if (category) {
      router.push(`/equipment?category=${category}`);
    } else {
      router.push('/equipment');
    }
    
    onActionTaken?.('load_equipment');
  };

  const getActionButtons = () => {
    const buttons = [];

    // Harvest crop actions
    if (lowerMessage.includes('harvest') || lowerMessage.includes('ready to harvest')) {
      buttons.push({
        key: 'harvest',
        title: t('field.harvestCrop'),
        description: t('field.harvestCropDescription'),
        icon: <Scissors size={20} color={colors.white} />,
        color: colors.warning,
        onPress: handleHarvestFromField,
      });
    }

    // Add crop to field actions
    if (lowerMessage.includes('plant crop') || lowerMessage.includes('add crop') || lowerMessage.includes('crop to field')) {
      buttons.push({
        key: 'addCrop',
        title: t('field.addCrop'),
        description: t('field.addCropDescription'),
        icon: <Sprout size={20} color={colors.white} />,
        color: colors.success,
        onPress: handleAddCropToField,
      });
    }

    // Add plant to garden actions
    if (lowerMessage.includes('add plant') || lowerMessage.includes('plant in garden')) {
      buttons.push({
        key: 'addPlant',
        title: t('plant.addToGarden'),
        description: t('plant.addToGardenDescription'),
        icon: <TreePine size={20} color={colors.white} />,
        color: colors.primary,
        onPress: handleAddPlantToGarden,
      });
    }

    // Equipment actions
    if (lowerMessage.includes('equipment') || lowerMessage.includes('tools') || lowerMessage.includes('machinery')) {
      let category = undefined;
      let title = t('equipment.viewAll');
      
      if (lowerMessage.includes('tools')) {
        category = 'Tools';
        title = t('equipment.viewTools');
      } else if (lowerMessage.includes('machinery')) {
        category = 'Equipment';
        title = t('equipment.viewMachinery');
      }

      buttons.push({
        key: 'equipment',
        title,
        description: t('equipment.viewDescription'),
        icon: <Tractor size={20} color={colors.white} />,
        color: colors.info,
        onPress: () => handleLoadEquipment(category),
      });
    }

    return buttons;
  };

  const actionButtons = getActionButtons();

  if (actionButtons.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      <Text style={[styles.title, isRTL && styles.textRtl]}>
        {t('chat.quickActions')}
      </Text>
      
      {actionButtons.map((button) => (
        <TouchableOpacity
          key={button.key}
          style={[styles.actionButton, { backgroundColor: button.color }]}
          onPress={button.onPress}
          activeOpacity={0.8}
        >
          <View style={styles.buttonContent}>
            <View style={styles.iconContainer}>
              {button.icon}
            </View>
            <View style={styles.textContainer}>
              <Text style={[styles.buttonTitle, isRTL && styles.textRtl]}>
                {button.title}
              </Text>
              <Text style={[styles.buttonDescription, isRTL && styles.textRtl]}>
                {button.description}
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
    paddingHorizontal: 4,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[700],
    marginBottom: 8,
  },
  actionButton: {
    borderRadius: 12,
    marginVertical: 4,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  buttonTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
    marginBottom: 2,
  },
  buttonDescription: {
    fontSize: 12,
    color: colors.white,
    opacity: 0.9,
  },
  textRtl: {
    textAlign: 'right',
  },
});

export default SpecialActionHandler;
