import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Modal,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { router, Stack } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import Button from '@/components/Button';
import Input from '@/components/Input';
import ImagePicker from '@/components/ImagePicker';
import DatePicker from '@/components/DatePicker';
import { uploadImageAsync } from '@/utils/firebase-storage';
import { 
  ChevronDown,
  Wheat,
  Check,
  Leaf,
  Home,
  TreeDeciduous,
  Star,
  Scale,
  Ruler,
} from 'lucide-react-native';
import { Field, Garden } from '@/types';
import { analyzeImageWithVision } from '@/utils/openai-vision';

export default function CreateYieldScreen() {
  const { user } = useAuthStore();
  const { 
    addYield, 
    fields, 
    gardens, 
    fetchFields, 
    fetchGardens, 
    currentFarm, 
    farms, 
    fetchFarms, 
    setCurrentFarm 
  } = useFarmStore();
  
  const [name, setName] = useState('');
  const [cropType, setCropType] = useState('');
  const [harvestDate, setHarvestDate] = useState(new Date());
  const [quantity, setQuantity] = useState('');
  const [unit, setUnit] = useState<'kg' | 'lb' | 'ton' | 'bushel' | 'count'>('kg');
  const [quality, setQuality] = useState<'excellent' | 'good' | 'fair' | 'poor'>('good');
  const [fieldId, setFieldId] = useState('');
  const [fieldName, setFieldName] = useState('');
  const [gardenId, setGardenId] = useState('');
  const [gardenName, setGardenName] = useState('');
  const [notes, setNotes] = useState('');
  const [imageUri, setImageUri] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  
  const [showUnitDropdown, setShowUnitDropdown] = useState(false);
  const [showQualityDropdown, setShowQualityDropdown] = useState(false);
  const [showFieldModal, setShowFieldModal] = useState(false);
  const [showGardenModal, setShowGardenModal] = useState(false);
  const [showFarmModal, setShowFarmModal] = useState(false);
  
  // useEffect(() => {
  //   if (user?.id) {
  //     fetchFarms(user.id);
  //   }
  // }, [user]);

  useEffect(() => {
    if (currentFarm?.id) {
      fetchFields(currentFarm.id);
      fetchGardens(currentFarm.id);
    }
  }, [currentFarm]);
  
  const handleCreateYield = async () => {
    if (!cropType) {
      Alert.alert('Error', 'Please enter the crop type');
      return;
    }
    
    if (!quantity || isNaN(Number(quantity))) {
      Alert.alert('Error', 'Please enter a valid quantity');
      return;
    }
    
    if (!fieldId && !gardenId) {
      Alert.alert('Error', 'Please select either a field or garden');
      return;
    }
    
    if (!currentFarm) {
      Alert.alert('Error', 'Please select a farm first');
      return;
    }
    
    try {
      setIsLoading(true);
      
      // Upload image if selected
      let imageUrl = '';
      if (imageUri) {
        imageUrl = await uploadImageAsync(imageUri, 'yields');
      } else {
        // Default image if none selected
        imageUrl = 'https://images.unsplash.com/photo-1523741543316-beb7fc7023d8?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8aGFydmVzdHxlbnwwfHwwfHw%3D&w=1000&q=80';
      }
      
      // Create yield data object
      const yieldData: any = {
        name: name || `${cropType} Harvest`,
        cropType,
        harvestDate: harvestDate.toISOString(),
        quantity: Number(quantity),
        unit,
        quality,
        farmId: currentFarm.id,
        image: imageUrl,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      // Only add these fields if they have values
      if (fieldId && fieldId.trim() !== '') yieldData.fieldId = fieldId;
      if (gardenId && gardenId.trim() !== '') yieldData.gardenId = gardenId;
      if (notes) yieldData.notes = notes;
      
      await addYield(yieldData);
      
      Alert.alert('Success', 'Yield added successfully', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    } catch (error: any) {
      console.error('Error creating yield:', error);
      Alert.alert('Error', error.message || 'Failed to add yield. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  const renderFieldItem = ({ item }: { item: Field }) => (
    <TouchableOpacity 
      style={styles.modalItem}
      onPress={() => {
        setFieldId(item.id);
        setFieldName(item.name);
        setGardenId('');
        setGardenName('');
        setShowFieldModal(false);
      }}
    >
      <View style={styles.modalItemContent}>
        <Leaf size={20} color={colors.gray[600]} style={styles.modalItemIcon} />
        <Text style={styles.modalItemText}>{item.name}</Text>
      </View>
      {fieldId === item.id && (
        <Check size={20} color={colors.primary} />
      )}
    </TouchableOpacity>
  );

  const renderGardenItem = ({ item }: { item: Garden }) => (
    <TouchableOpacity 
      style={styles.modalItem}
      onPress={() => {
        setGardenId(item.id);
        setGardenName(item.name);
        setFieldId('');
        setFieldName('');
        setShowGardenModal(false);
      }}
    >
      <View style={styles.modalItemContent}>
        <TreeDeciduous size={20} color={colors.gray[600]} style={styles.modalItemIcon} />
        <Text style={styles.modalItemText}>{item.name}</Text>
      </View>
      {gardenId === item.id && (
        <Check size={20} color={colors.primary} />
      )}
    </TouchableOpacity>
  );

  const renderFarmItem = ({ item }: { item: any }) => (
    <TouchableOpacity 
      style={styles.modalItem}
      onPress={() => {
        setCurrentFarm(item.id);
        setShowFarmModal(false);
      }}
    >
      <View style={styles.modalItemContent}>
        <Home size={20} color={colors.gray[600]} style={styles.modalItemIcon} />
        <Text style={styles.modalItemText}>{item.name}</Text>
      </View>
      {currentFarm?.id === item.id && (
        <Check size={20} color={colors.primary} />
      )}
    </TouchableOpacity>
  );
  
  // Helper function to get quality color
  const getQualityColor = (quality: string) => {
    switch (quality) {
      case 'excellent':
        return colors.success;
      case 'good':
        return colors.primary;
      case 'fair':
        return colors.warning;
      case 'poor':
        return colors.danger;
      default:
        return colors.gray[500];
    }
  };
  
  // Handle image analysis results
  const handleAnalysisComplete = (analysis: any) => {
    if (!analysis) return;
    
    // Only apply results if it's a yield/harvest
    if (analysis.category === 'yield') {
      // Set crop type if empty
      if (!cropType && analysis.type) {
        setCropType(analysis.type.charAt(0).toUpperCase() + analysis.type.slice(1));
      }
      
      // Set name if empty
      if (!name && analysis.subtype) {
        setName(`${analysis.subtype} Harvest`);
      }
      
      // Set quality based on details
      if (analysis.details) {
        const details = analysis.details.toLowerCase();
        
        if (details.includes('excellent') || details.includes('premium')) {
          setQuality('excellent');
        } else if (details.includes('good')) {
          setQuality('good');
        } else if (details.includes('fair') || details.includes('average')) {
          setQuality('fair');
        } else if (details.includes('poor') || details.includes('low')) {
          setQuality('poor');
        }
        
        // Try to extract quantity
        const quantityMatch = details.match(/(\d+(\.\d+)?)\s*(kg|kilograms|pounds|lbs|tons|bushels)/i);
        if (quantityMatch) {
          setQuantity(quantityMatch[1]);
          
          // Set unit based on extracted text
          const unitText = quantityMatch[3].toLowerCase();
          if (unitText.includes('kg') || unitText.includes('kilo')) {
            setUnit('kg');
          } else if (unitText.includes('lb') || unitText.includes('pound')) {
            setUnit('lb');
          } else if (unitText.includes('ton')) {
            setUnit('ton');
          } else if (unitText.includes('bushel')) {
            setUnit('bushel');
          }
        }
        
        // Add details to notes if empty
        if (!notes) {
          setNotes(analysis.details);
        }
      }
    }
  };
  
  return (
    <>
      <Stack.Screen 
        options={{
          title: 'Add New Yield',
          headerShown: true,
        }}
      />
      
      <SafeAreaView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          {/* Farm Selection */}
          {farms.length > 0 && (
            <View style={styles.farmSelectionContainer}>
              <Text style={styles.farmSelectionLabel}>Select Farm</Text>
              <TouchableOpacity 
                style={styles.farmSelectionButton}
                onPress={() => setShowFarmModal(true)}
              >
                <Home size={20} color={colors.gray[500]} style={styles.farmIcon} />
                <Text style={styles.farmSelectionText}>
                  {currentFarm?.name || "Select a farm"}
                </Text>
                <ChevronDown size={20} color={colors.gray[500]} />
              </TouchableOpacity>
            </View>
          )}

          <ImagePicker 
            image={imageUri}
            onImageSelected={setImageUri}
            placeholder="Add Harvest Photo"
            showAnalyzeOption={true}
            onAnalyzeImage={analyzeImageWithVision}
            onAnalysisComplete={handleAnalysisComplete}
          />
          
          <View style={styles.formContainer}>
            <Input
              label="Harvest Name (Optional)"
              placeholder="Enter harvest name"
              value={name}
              onChangeText={setName}
              containerStyle={styles.inputContainer}
              leftIcon={<Wheat size={20} color={colors.gray[500]} />}
            />
            
            <Input
              label="Crop Type"
              placeholder="Enter crop type (e.g., Wheat, Corn)"
              value={cropType}
              onChangeText={setCropType}
              containerStyle={styles.inputContainer}
            />
            
            <DatePicker
              label="Harvest Date"
              value={harvestDate}
              onChange={setHarvestDate}
              required={true}
            />
            
            <View style={styles.rowContainer}>
              <View style={styles.quantityContainer}>
                <Input
                  label="Quantity"
                  placeholder="Enter quantity"
                  value={quantity}
                  onChangeText={setQuantity}
                  keyboardType="numeric"
                  containerStyle={styles.quantityInput}
                  leftIcon={<Scale size={20} color={colors.gray[500]} />}
                />
              </View>
              
              <View style={styles.unitContainer}>
                <Text style={styles.label}>Unit</Text>
                <TouchableOpacity 
                  style={styles.unitDropdown}
                  onPress={() => setShowUnitDropdown(!showUnitDropdown)}
                >
                  <View style={styles.unitDropdownHeader}>
                    <Ruler size={20} color={colors.gray[500]} style={styles.unitIcon} />
                    <Text style={styles.unitText}>{unit}</Text>
                    <ChevronDown size={20} color={colors.gray[500]} />
                  </View>
                  
                  {showUnitDropdown && (
                    <View style={styles.dropdownMenu}>
                      <TouchableOpacity 
                        style={styles.dropdownItem}
                        onPress={() => {
                          setUnit('kg');
                          setShowUnitDropdown(false);
                        }}
                      >
                        <Text style={styles.dropdownItemText}>kg</Text>
                      </TouchableOpacity>
                      
                      <TouchableOpacity 
                        style={styles.dropdownItem}
                        onPress={() => {
                          setUnit('lb');
                          setShowUnitDropdown(false);
                        }}
                      >
                        <Text style={styles.dropdownItemText}>lb</Text>
                      </TouchableOpacity>
                      
                      <TouchableOpacity 
                        style={styles.dropdownItem}
                        onPress={() => {
                          setUnit('ton');
                          setShowUnitDropdown(false);
                        }}
                      >
                        <Text style={styles.dropdownItemText}>ton</Text>
                      </TouchableOpacity>
                      
                      <TouchableOpacity 
                        style={styles.dropdownItem}
                        onPress={() => {
                          setUnit('bushel');
                          setShowUnitDropdown(false);
                        }}
                      >
                        <Text style={styles.dropdownItemText}>bushel</Text>
                      </TouchableOpacity>
                      
                      <TouchableOpacity 
                        style={styles.dropdownItem}
                        onPress={() => {
                          setUnit('count');
                          setShowUnitDropdown(false);
                        }}
                      >
                        <Text style={styles.dropdownItemText}>count</Text>
                      </TouchableOpacity>
                    </View>
                  )}
                </TouchableOpacity>
              </View>
            </View>
            
            <Text style={styles.label}>Quality</Text>
            <TouchableOpacity 
              style={styles.qualityDropdown}
              onPress={() => setShowQualityDropdown(!showQualityDropdown)}
            >
              <View style={styles.qualityDropdownHeader}>
                <View style={[styles.qualityIndicator, { backgroundColor: getQualityColor(quality) }]} />
                <Star size={20} color={getQualityColor(quality)} style={styles.qualityIcon} />
                <Text style={styles.qualityText}>
                  {quality.charAt(0).toUpperCase() + quality.slice(1)}
                </Text>
                <ChevronDown size={20} color={colors.gray[500]} />
              </View>
              
              {showQualityDropdown && (
                <View style={styles.dropdownMenu}>
                  <TouchableOpacity 
                    style={styles.dropdownItem}
                    onPress={() => {
                      setQuality('excellent');
                      setShowQualityDropdown(false);
                    }}
                  >
                    <View style={[styles.qualityIndicator, { backgroundColor: getQualityColor('excellent') }]} />
                    <Star size={20} color={getQualityColor('excellent')} style={styles.qualityIcon} />
                    <Text style={styles.dropdownItemText}>Excellent</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity 
                    style={styles.dropdownItem}
                    onPress={() => {
                      setQuality('good');
                      setShowQualityDropdown(false);
                    }}
                  >
                    <View style={[styles.qualityIndicator, { backgroundColor: getQualityColor('good') }]} />
                    <Star size={20} color={getQualityColor('good')} style={styles.qualityIcon} />
                    <Text style={styles.dropdownItemText}>Good</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity 
                    style={styles.dropdownItem}
                    onPress={() => {
                      setQuality('fair');
                      setShowQualityDropdown(false);
                    }}
                  >
                    <View style={[styles.qualityIndicator, { backgroundColor: getQualityColor('fair') }]} />
                    <Star size={20} color={getQualityColor('fair')} style={styles.qualityIcon} />
                    <Text style={styles.dropdownItemText}>Fair</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity 
                    style={styles.dropdownItem}
                    onPress={() => {
                      setQuality('poor');
                      setShowQualityDropdown(false);
                    }}
                  >
                    <View style={[styles.qualityIndicator, { backgroundColor: getQualityColor('poor') }]} />
                    <Star size={20} color={getQualityColor('poor')} style={styles.qualityIcon} />
                    <Text style={styles.dropdownItemText}>Poor</Text>
                  </TouchableOpacity>
                </View>
              )}
            </TouchableOpacity>
            
            <Text style={styles.label}>Location</Text>
            <View style={styles.locationContainer}>
              <TouchableOpacity 
                style={[
                  styles.locationButton, 
                  fieldId ? styles.locationButtonActive : null
                ]}
                onPress={() => setShowFieldModal(true)}
              >
                <Leaf size={20} color={fieldId ? colors.white : colors.gray[600]} />
                <Text style={[
                  styles.locationButtonText,
                  fieldId ? styles.locationButtonTextActive : null
                ]}>
                  {fieldName || "Select Field"}
                </Text>
              </TouchableOpacity>
              
              <Text style={styles.orText}>OR</Text>
              
              <TouchableOpacity 
                style={[
                  styles.locationButton,
                  gardenId ? styles.locationButtonActive : null
                ]}
                onPress={() => setShowGardenModal(true)}
              >
                <TreeDeciduous size={20} color={gardenId ? colors.white : colors.gray[600]} />
                <Text style={[
                  styles.locationButtonText,
                  gardenId ? styles.locationButtonTextActive : null
                ]}>
                  {gardenName || "Select Garden"}
                </Text>
              </TouchableOpacity>
            </View>
            
            <Input
              label="Notes (Optional)"
              placeholder="Enter any additional notes"
              value={notes}
              onChangeText={setNotes}
              multiline
              numberOfLines={4}
              containerStyle={styles.inputContainer}
              inputStyle={styles.textArea}
            />
          </View>
          
          <View style={styles.buttonContainer}>
            <Button
              title="Cancel"
              variant="outline"
              onPress={() => router.back()}
              style={styles.cancelButton}
              disabled={isLoading}
            />
            <Button
              title={isLoading ? "Adding..." : "Add Yield"}
              onPress={handleCreateYield}
              style={styles.createButton}
              disabled={isLoading}
              leftIcon={isLoading ? <ActivityIndicator size="small" color={colors.white} /> : undefined}
            />
          </View>
        </ScrollView>
        
        {/* Field Selection Modal */}
        <Modal
          visible={showFieldModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowFieldModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Select Field</Text>
                <TouchableOpacity onPress={() => setShowFieldModal(false)}>
                  <Text style={styles.modalCloseText}>Close</Text>
                </TouchableOpacity>
              </View>
              {fields.length > 0 ? (
                <FlatList
                  data={fields}
                  renderItem={renderFieldItem}
                  keyExtractor={item => item.id}
                  style={styles.modalList}
                />
              ) : (
                <View style={styles.emptyListContainer}>
                  <Text style={styles.emptyListText}>No fields available. Please create a field first.</Text>
                </View>
              )}
            </View>
          </View>
        </Modal>
        
        {/* Garden Selection Modal */}
        <Modal
          visible={showGardenModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowGardenModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Select Garden</Text>
                <TouchableOpacity onPress={() => setShowGardenModal(false)}>
                  <Text style={styles.modalCloseText}>Close</Text>
                </TouchableOpacity>
              </View>
              {gardens.length > 0 ? (
                <FlatList
                  data={gardens}
                  renderItem={renderGardenItem}
                  keyExtractor={item => item.id}
                  style={styles.modalList}
                />
              ) : (
                <View style={styles.emptyListContainer}>
                  <Text style={styles.emptyListText}>No gardens available. Please create a garden first.</Text>
                </View>
              )}
            </View>
          </View>
        </Modal>

        {/* Farm Selection Modal */}
        <Modal
          visible={showFarmModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowFarmModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Select Farm</Text>
                <TouchableOpacity onPress={() => setShowFarmModal(false)}>
                  <Text style={styles.modalCloseText}>Close</Text>
                </TouchableOpacity>
              </View>
              {farms.length > 0 ? (
                <FlatList
                  data={farms}
                  renderItem={renderFarmItem}
                  keyExtractor={item => item.id}
                  style={styles.modalList}
                />
              ) : (
                <View style={styles.emptyListContainer}>
                  <Text style={styles.emptyListText}>No farms available. Please create a farm first.</Text>
                </View>
              )}
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  formContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  inputContainer: {
    marginBottom: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
    paddingTop: 12,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
  },
  rowContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  quantityContainer: {
    flex: 2,
    marginRight: 8,
  },
  quantityInput: {
    flex: 1,
  },
  unitContainer: {
    flex: 1,
  },
  unitDropdown: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    position: 'relative',
  },
  unitDropdownHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  unitIcon: {
    marginRight: 8,
  },
  unitText: {
    fontSize: 14,
    color: colors.gray[800],
    flex: 1,
  },
  qualityDropdown: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    marginBottom: 16,
    position: 'relative',
  },
  qualityDropdownHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  qualityIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  qualityIcon: {
    marginRight: 8,
  },
  qualityText: {
    fontSize: 14,
    color: colors.gray[800],
    flex: 1,
  },
  dropdownMenu: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: colors.white,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    borderWidth: 1,
    borderTopWidth: 0,
    borderColor: colors.gray[300],
    zIndex: 10,
    elevation: 3,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  dropdownItemText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  locationContainer: {
    marginBottom: 16,
  },
  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    marginBottom: 8,
  },
  locationButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  locationButtonText: {
    fontSize: 14,
    color: colors.gray[800],
    marginLeft: 8,
  },
  locationButtonTextActive: {
    color: colors.white,
  },
  orText: {
    textAlign: 'center',
    fontSize: 14,
    color: colors.gray[500],
    marginVertical: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  createButton: {
    flex: 1,
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  modalCloseText: {
    fontSize: 16,
    color: colors.primary,
  },
  modalList: {
    maxHeight: '80%',
  },
  modalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalItemIcon: {
    marginRight: 12,
  },
  modalItemText: {
    fontSize: 16,
    color: colors.gray[800],
  },
  emptyListContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyListText: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
  },
  farmSelectionContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  farmSelectionLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
  },
  farmSelectionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  farmIcon: {
    marginRight: 8,
  },
  farmSelectionText: {
    fontSize: 14,
    color: colors.gray[800],
    flex: 1,
  },
});