import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Alert,
  ActivityIndicator,
  Modal,
} from 'react-native';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import { useTranslation } from '@/i18n/useTranslation';
import { Ionicons } from '@expo/vector-icons';
import { Machinery, MaintenanceRecord } from '@/types';

export default function MachineryDetailScreen() {
  const { id } = useLocalSearchParams();
  const { t, isRTL } = useTranslation();
  const { machinery, fetchMachinery, fetchMaintenanceRecords, maintenanceRecords, currentFarm } = useFarmStore();
  const { user } = useAuthStore();
  
  const [machineryData, setMachineryData] = useState<Machinery | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('details');
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState('');

  useEffect(() => {
    if (id && currentFarm?.id) {
      loadMachineryData();
    }
  }, [id, currentFarm?.id]);

  const loadMachineryData = async () => {
    try {
      setLoading(true);
      
      // Find machinery in store or fetch if not available
      let foundMachinery = machinery.find(m => m.id === id);
      
      if (!foundMachinery) {
        await fetchMachinery();
        foundMachinery = machinery.find(m => m.id === id);
      }
      
      if (foundMachinery) {
        setMachineryData(foundMachinery);
        // Load maintenance records
        await fetchMaintenanceRecords(foundMachinery.id);
      } else {
        Alert.alert(t('common.error'), t('machinery.notFound'));
        router.back();
      }
    } catch (error) {
      console.error('Error loading machinery:', error);
      Alert.alert(t('common.error'), t('machinery.loadError'));
    } finally {
      setLoading(false);
    }
  };

  const getMachineryIcon = (type: string) => {
    switch (type) {
      case 'tractor': return 'car-sport';
      case 'harvester': return 'leaf';
      case 'planter': return 'flower';
      case 'cultivator': return 'grid';
      case 'sprayer': return 'water';
      case 'mower': return 'cut';
      default: return 'construct';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'operational': return colors.success;
      case 'maintenance': return colors.warning;
      case 'repair': return colors.danger;
      case 'inactive': return colors.gray[500];
      default: return colors.gray[500];
    }
  };

  const renderDetailsTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.infoSection}>
        <Text style={[styles.sectionTitle, isRTL && styles.textRTL]}>
          {t('machinery.basicInfo')}
        </Text>
        
        <View style={styles.infoGrid}>
          <View style={[styles.infoItem, isRTL && styles.infoItemRTL]}>
            <Ionicons name="construct" size={20} color={colors.gray[600]} />
            <View>
              <Text style={[styles.infoLabel, isRTL && styles.textRTL]}>
                {t('machinery.type')}
              </Text>
              <Text style={[styles.infoValue, isRTL && styles.textRTL]}>
                {t(`machinery.type.${machineryData?.type}`)}
              </Text>
            </View>
          </View>

          <View style={[styles.infoItem, isRTL && styles.infoItemRTL]}>
            <Ionicons name="business" size={20} color={colors.gray[600]} />
            <View>
              <Text style={[styles.infoLabel, isRTL && styles.textRTL]}>
                {t('machinery.manufacturer')}
              </Text>
              <Text style={[styles.infoValue, isRTL && styles.textRTL]}>
                {machineryData?.manufacturer || t('common.notSpecified')}
              </Text>
            </View>
          </View>

          <View style={[styles.infoItem, isRTL && styles.infoItemRTL]}>
            <Ionicons name="car" size={20} color={colors.gray[600]} />
            <View>
              <Text style={[styles.infoLabel, isRTL && styles.textRTL]}>
                {t('machinery.model')}
              </Text>
              <Text style={[styles.infoValue, isRTL && styles.textRTL]}>
                {machineryData?.model || t('common.notSpecified')}
              </Text>
            </View>
          </View>

          {machineryData?.serialNumber && (
            <View style={[styles.infoItem, isRTL && styles.infoItemRTL]}>
              <Ionicons name="barcode" size={20} color={colors.gray[600]} />
              <View>
                <Text style={[styles.infoLabel, isRTL && styles.textRTL]}>
                  {t('machinery.serialNumber')}
                </Text>
                <Text style={[styles.infoValue, isRTL && styles.textRTL]}>
                  {machineryData.serialNumber}
                </Text>
              </View>
            </View>
          )}

          {machineryData?.horsepower && (
            <View style={[styles.infoItem, isRTL && styles.infoItemRTL]}>
              <Ionicons name="flash" size={20} color={colors.gray[600]} />
              <View>
                <Text style={[styles.infoLabel, isRTL && styles.textRTL]}>
                  {t('machinery.horsepower')}
                </Text>
                <Text style={[styles.infoValue, isRTL && styles.textRTL]}>
                  {machineryData.horsepower} HP
                </Text>
              </View>
            </View>
          )}

          {machineryData?.fuelType && (
            <View style={[styles.infoItem, isRTL && styles.infoItemRTL]}>
              <Ionicons name="car" size={20} color={colors.gray[600]} />
              <View>
                <Text style={[styles.infoLabel, isRTL && styles.textRTL]}>
                  {t('machinery.fuelType')}
                </Text>
                <Text style={[styles.infoValue, isRTL && styles.textRTL]}>
                  {t(`machinery.fuel.${machineryData.fuelType}`)}
                </Text>
              </View>
            </View>
          )}
        </View>
      </View>

      {machineryData?.notes && (
        <View style={styles.notesSection}>
          <Text style={[styles.sectionTitle, isRTL && styles.textRTL]}>
            {t('common.notes')}
          </Text>
          <Text style={[styles.notesText, isRTL && styles.textRTL]}>
            {machineryData.notes}
          </Text>
        </View>
      )}
    </View>
  );

  const renderMaintenanceTab = () => {
    const machineryMaintenanceRecords = maintenanceRecords.filter(
      record => record.machineryId === machineryData?.id
    );

    return (
      <View style={styles.tabContent}>
        <View style={[styles.maintenanceHeader, isRTL && styles.maintenanceHeaderRTL]}>
          <Text style={[styles.sectionTitle, isRTL && styles.textRTL]}>
            {t('machinery.maintenanceHistory')}
          </Text>
          <TouchableOpacity
            style={styles.addMaintenanceButton}
            onPress={() => router.push(`/machinery/${id}/maintenance/create`)}
          >
            <Ionicons name="add" size={20} color={colors.primary} />
            <Text style={[styles.addMaintenanceText, isRTL && styles.textRTL]}>
              {t('machinery.addMaintenance')}
            </Text>
          </TouchableOpacity>
        </View>

        {machineryMaintenanceRecords.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Ionicons name="build" size={48} color={colors.gray[400]} />
            <Text style={[styles.emptyText, isRTL && styles.textRTL]}>
              {t('machinery.noMaintenanceRecords')}
            </Text>
          </View>
        ) : (
          <View style={styles.maintenanceList}>
            {machineryMaintenanceRecords.map((record) => (
              <View key={record.id} style={styles.maintenanceCard}>
                <View style={[styles.maintenanceCardHeader, isRTL && styles.maintenanceCardHeaderRTL]}>
                  <View style={[styles.maintenanceType, { backgroundColor: getMaintenanceTypeColor(record.type) + '20' }]}>
                    <Ionicons
                      name={getMaintenanceTypeIcon(record.type) as any}
                      size={16}
                      color={getMaintenanceTypeColor(record.type)}
                    />
                    <Text style={[styles.maintenanceTypeText, { color: getMaintenanceTypeColor(record.type) }, isRTL && styles.textRTL]}>
                      {t(`machinery.maintenanceType.${record.type}`)}
                    </Text>
                  </View>
                  <Text style={[styles.maintenanceDate, isRTL && styles.textRTL]}>
                    {new Date(record.date).toLocaleDateString()}
                  </Text>
                </View>
                
                <Text style={[styles.maintenanceDescription, isRTL && styles.textRTL]}>
                  {record.description}
                </Text>
                
                {record.cost && (
                  <Text style={[styles.maintenanceCost, isRTL && styles.textRTL]}>
                    {t('common.cost')}: ${record.cost.toLocaleString()}
                  </Text>
                )}
              </View>
            ))}
          </View>
        )}
      </View>
    );
  };

  const getMaintenanceTypeColor = (type: string) => {
    switch (type) {
      case 'routine': return colors.primary;
      case 'repair': return colors.danger;
      case 'inspection': return colors.info;
      case 'upgrade': return colors.success;
      default: return colors.gray[600];
    }
  };

  const getMaintenanceTypeIcon = (type: string) => {
    switch (type) {
      case 'routine': return 'refresh';
      case 'repair': return 'build';
      case 'inspection': return 'eye';
      case 'upgrade': return 'arrow-up';
      default: return 'construct';
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: t('machinery.loading') }} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, isRTL && styles.textRTL]}>
            {t('machinery.loading')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!machineryData) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: t('machinery.notFound') }} />
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color={colors.danger} />
          <Text style={[styles.errorText, isRTL && styles.textRTL]}>
            {t('machinery.notFound')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen 
        options={{ 
          title: machineryData.name,
          headerRight: () => (
            <TouchableOpacity
              onPress={() => router.push(`/machinery/${id}/edit`)}
              style={styles.editButton}
            >
              <Ionicons name="create" size={24} color={colors.primary} />
            </TouchableOpacity>
          ),
        }} 
      />
      
      <ScrollView style={styles.scrollView}>
        {/* Header Section */}
        <View style={styles.headerSection}>
          <View style={[styles.machineryHeader, isRTL && styles.machineryHeaderRTL]}>
            <View style={[styles.iconContainer, { backgroundColor: colors.primary + '20' }]}>
              <Ionicons
                name={getMachineryIcon(machineryData.type) as any}
                size={32}
                color={colors.primary}
              />
            </View>
            
            <View style={styles.headerInfo}>
              <Text style={[styles.machineryName, isRTL && styles.textRTL]}>
                {machineryData.name}
              </Text>
              <Text style={[styles.machinerySubtitle, isRTL && styles.textRTL]}>
                {machineryData.manufacturer} {machineryData.model}
              </Text>
              
              <View style={[styles.statusBadge, { backgroundColor: getStatusColor(machineryData.status) + '20' }]}>
                <Ionicons
                  name="checkmark-circle"
                  size={16}
                  color={getStatusColor(machineryData.status)}
                />
                <Text style={[styles.statusText, { color: getStatusColor(machineryData.status) }, isRTL && styles.textRTL]}>
                  {t(`machinery.status.${machineryData.status}`)}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Tabs */}
        <View style={[styles.tabsContainer, isRTL && styles.tabsContainerRTL]}>
          {['details', 'maintenance'].map((tabKey) => (
            <TouchableOpacity
              key={tabKey}
              style={[styles.tab, activeTab === tabKey && styles.activeTab]}
              onPress={() => setActiveTab(tabKey)}
            >
              <Text
                style={[
                  styles.tabText,
                  activeTab === tabKey && styles.activeTabText,
                  isRTL && styles.textRTL,
                ]}
              >
                {t(`machinery.tabs.${tabKey}`)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Tab Content */}
        {activeTab === 'details' && renderDetailsTab()}
        {activeTab === 'maintenance' && renderMaintenanceTab()}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.gray[600],
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.danger,
    textAlign: 'center',
  },
  editButton: {
    padding: 8,
  },
  headerSection: {
    backgroundColor: colors.white,
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  machineryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  machineryHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  headerInfo: {
    flex: 1,
  },
  machineryName: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.gray[800],
    marginBottom: 4,
  },
  machinerySubtitle: {
    fontSize: 16,
    color: colors.gray[600],
    marginBottom: 8,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
    gap: 6,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  tabsContainerRTL: {
    flexDirection: 'row-reverse',
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: colors.primary,
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.gray[600],
  },
  activeTabText: {
    color: colors.primary,
    fontWeight: '600',
  },
  tabContent: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 16,
  },
  infoSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  infoGrid: {
    gap: 16,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  infoItemRTL: {
    flexDirection: 'row-reverse',
  },
  infoLabel: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.gray[800],
  },
  notesSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
  },
  notesText: {
    fontSize: 14,
    color: colors.gray[700],
    lineHeight: 20,
  },
  maintenanceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  maintenanceHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  addMaintenanceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: colors.primary + '20',
    gap: 6,
  },
  addMaintenanceText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.primary,
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
  },
  maintenanceList: {
    gap: 12,
  },
  maintenanceCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  maintenanceCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  maintenanceCardHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  maintenanceType: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  maintenanceTypeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  maintenanceDate: {
    fontSize: 12,
    color: colors.gray[600],
  },
  maintenanceDescription: {
    fontSize: 14,
    color: colors.gray[700],
    marginBottom: 4,
  },
  maintenanceCost: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.primary,
  },
  textRTL: {
    textAlign: 'right',
  },
});
