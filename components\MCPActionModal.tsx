import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  FlatList,
  Alert,
  ActivityIndicator,
  ScrollView,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { mcpServerService } from '@/services/mcp-server';
import { MCPSuggestedAction } from '@/services/mcp-types';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import { colors } from '@/constants/colors';
import MCPEntityListView from './mcp/MCPEntityListView';

interface MCPActionModalProps {
  visible: boolean;
  actions: MCPSuggestedAction[];
  entityData?: any; // Entity data to be passed to actions
  onClose: () => void;
  onConfirm: (confirmedActions: MCPSuggestedAction[]) => void;
}

export default function MCPActionModal({
  visible,
  actions,
  entityData,
  onClose,
  onConfirm,
}: MCPActionModalProps) {
  const [completedActions, setCompletedActions] = useState<MCPSuggestedAction[]>([]);
  const [executingActions, setExecutingActions] = useState<Set<string>>(new Set());
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [selectedAction, setSelectedAction] = useState<MCPSuggestedAction | null>(null);
  const [showSelectionModal, setShowSelectionModal] = useState(false);
  const [selectionData, setSelectionData] = useState<any>(null);

  const { currentFarm } = useFarmStore();
  const { user } = useAuthStore();

  // Helper function to determine entity type from data structure
  const getEntityTypeFromData = (data: any[]): string => {
    if (!data || data.length === 0) return 'unknown';

    const firstItem = data[0];

    // Check for plant-specific fields
    if (firstItem.species && (firstItem.plantedDate || firstItem.variety)) {
      return 'plant';
    }

    // Check for animal-specific fields
    if (firstItem.species && (firstItem.breed || firstItem.gender || firstItem.weight)) {
      return 'animal';
    }

    // Check for garden-specific fields
    if (firstItem.type && (firstItem.soilType || firstItem.irrigationSystem)) {
      return 'garden';
    }

    // Check for field-specific fields
    if (firstItem.type && (firstItem.cropType || firstItem.harvestDate)) {
      return 'field';
    }

    // Check for equipment-specific fields
    if (firstItem.category && (firstItem.manufacturer || firstItem.model)) {
      return 'equipment';
    }

    // Default fallback
    return 'unknown';
  };

  const showActionConfirmation = (action: MCPSuggestedAction) => {
    let confirmMessage = `Are you sure you want to execute this action?\n\nAction: ${action.label}\nType: ${action.type}\nEntity: ${action.entityType}`;

    // Add data details if available
    if (action.data) {
      const dataKeys = Object.keys(action.data);
      if (dataKeys.length > 0) {
        confirmMessage += '\n\nData to be processed:';
        dataKeys.slice(0, 3).forEach(key => { // Show first 3 fields
          const value = action.data[key];
          if (value && typeof value === 'string' && value.length < 50) {
            confirmMessage += `\n• ${key}: ${value}`;
          }
        });
        if (dataKeys.length > 3) {
          confirmMessage += `\n• ... and ${dataKeys.length - 3} more fields`;
        }
      }
    }

    Alert.alert(
      'Confirm Action',
      confirmMessage,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Execute',
          style: 'default',
          onPress: () => executeAction(action),
        },
      ]
    );
  };

  const executeAction = async (action: MCPSuggestedAction) => {
    console.log('Executing action with entityData:', { action, entityData });
    if (!currentFarm || !user) {
      Alert.alert('Error', 'No farm or user selected');
      return;
    }

    // Generate a unique ID for the action if it doesn't have one
    const actionId = `${action.type}_${action.entityType}_${Date.now()}`;
    setExecutingActions(prev => new Set(prev).add(actionId));

    try {
      const context = {
        farmId: currentFarm.id,
        userId: user.id || '123456',
      };

      // Attach entityData to the action if available
      const actionWithData = {
        ...action,
        data: entityData || action.data || {}
      };

      console.log('Action with entityData attached:', actionWithData);

      const result = await mcpServerService.executeAction(actionWithData, context);

      // Handle selection requirements
      if (result.requiresSelection) {
        setSelectionData(result);
        setShowSelectionModal(true);
        return;
      }

      if (result.success) {
        // Mark action as completed
        const completedAction = { ...action, id: actionId };
        setCompletedActions(prev => [...prev, completedAction]);

        // Show success message with confirmation ID if available
        let successMessage = `Action "${action.label}" completed successfully!`;

        if (result.confirmationId) {
          successMessage += `\n\nConfirmation ID: ${result.confirmationId}`;
        }

        if (result.entityData && Array.isArray(result.entityData) && result.entityData.length > 0) {
          const entityCount = result.entityData.length;
          successMessage += `\n\n${entityCount} ${action.entityType}(s) processed`;
        }

        Alert.alert('Success', successMessage);
        console.log('MCP Action result:', result);

        // If we have entity data, trigger a callback to show it in chat
        if (result.entityData) {
          onConfirm([{ ...action, entityData: result.entityData }]);
        }
      } else {
        // Show error message but still check for entity data
        if (result.entityData && Array.isArray(result.entityData) && result.entityData.length > 0) {
          // Even if marked as failed, we have data to show
          Alert.alert('Action Result', `${result.message}\n\nFound ${result.entityData.length} ${action.entityType}(s). Data will be displayed in chat.`);
          onConfirm([{ ...action, entityData: result.entityData }]);
        } else {
          Alert.alert('Error', `Failed to execute action: ${result.message}`);
        }
        console.error('MCP Action failed:', result);
      }
    } catch (error) {
      console.error('Error executing MCP action:', error);
      Alert.alert('Error', `Failed to execute action: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setExecutingActions(prev => {
        const newSet = new Set(prev);
        newSet.delete(actionId);
        return newSet;
      });
    }
  };

  const handleSelectionConfirm = async (selectedId: string) => {
    if (!selectionData || !currentFarm || !user) return;

    try {
      setShowSelectionModal(false);

      const context = {
        farmId: currentFarm.id,
        userId: user.id || '123456',
      };

      const result = await mcpServerService.confirmSelection(
        selectedId,
        selectionData.pendingAction,
        selectionData.pendingEntityData,
        context
      );

      if (result.success) {
        let successMessage = `Selection confirmed and action completed!`;

        if (result.confirmationId) {
          successMessage += `\n\nConfirmation ID: ${result.confirmationId}`;
        }

        if (result.entityData && Array.isArray(result.entityData) && result.entityData.length > 0) {
          const entityCount = result.entityData.length;
          successMessage += `\n\n${entityCount} item(s) processed`;
        }

        Alert.alert('Success', successMessage);

        // If we have entity data, trigger a callback to show it in chat
        if (result.entityData) {
          onConfirm([{
            type: selectionData.pendingAction.type,
            entityType: selectionData.pendingAction.entityType,
            label: `${selectionData.pendingAction.label} (Selection Confirmed)`,
            entityData: result.entityData
          }]);
        }
      } else {
        Alert.alert('Error', `Selection confirmation failed: ${result.message}`);
      }
    } catch (error) {
      console.error('Selection confirmation error:', error);
      Alert.alert('Error', 'Failed to confirm selection');
    }
  };

  const handleFinish = () => {
    onConfirm(completedActions);
    setCompletedActions([]);
    setExecutingActions(new Set());
  };

  const getActionIcon = (action: MCPSuggestedAction) => {
    const iconMap: { [key: string]: string } = {
      animal: 'paw',
      plant: 'leaf',
      garden: 'flower',
      field: 'grid',
      equipment: 'construct',
      crop: 'nutrition',
      task: 'checkmark-circle',
    };
    return iconMap[action.entityType] || 'help';
  };

  const getActionColor = (action: MCPSuggestedAction) => {
    const colorMap: { [key: string]: string } = {
      fetch_plants: colors.primary,
      add_plant: colors.success,
      update_plant: colors.warning,
      delete_plant: colors.danger,
      fetch_animals: colors.primary,
      add_animal: colors.success,
      update_animal: colors.warning,
      delete_animal: colors.danger,
    };
    return colorMap[action.type] || colors.primary;
  };

  const getActionTypeIcon = (type: string) => {
    const iconMap: { [key: string]: string } = {
      fetch_plants: 'search',
      add_plant: 'add',
      update_plant: 'create',
      delete_plant: 'trash',
      fetch_animals: 'search',
      add_animal: 'add',
      update_animal: 'create',
      delete_animal: 'trash',
    };
    return iconMap[type] || 'help';
  };

  const isActionCompleted = (action: MCPSuggestedAction) => {
    const actionId = `${action.type}_${action.entityType}_`;
    return completedActions.some((completedAction: any) =>
      completedAction.id && completedAction.id.startsWith(actionId)
    );
  };

  const isActionExecuting = (action: MCPSuggestedAction) => {
    const actionId = `${action.type}_${action.entityType}_`;
    return Array.from(executingActions).some(id => id.startsWith(actionId));
  };

  const renderAction = ({ item }: { item: MCPSuggestedAction }) => {
    const isCompleted = isActionCompleted(item);
    const isExecuting = isActionExecuting(item);

    return (
      <TouchableOpacity
        style={[
          styles.actionItem,
          isCompleted && styles.actionItemCompleted,
          isExecuting && styles.actionItemExecuting,
        ]}
        onPress={() => !isCompleted && !isExecuting && showActionConfirmation(item)}
        disabled={isCompleted || isExecuting}
      >
        <View style={styles.actionHeader}>
          <View style={styles.actionIconContainer}>
            <Ionicons
              name={isCompleted ? 'checkmark-circle' : getActionIcon(item) as any}
              size={20}
              color={isCompleted ? colors.success : getActionColor(item)}
            />
          </View>

          <View style={styles.actionInfo}>
            <View style={styles.actionTitleRow}>
              <Text style={styles.actionTitle}>{item.label}</Text>
              <View style={styles.actionTypeContainer}>
                <Ionicons
                  name={getActionTypeIcon(item.type) as any}
                  size={14}
                  color={getActionColor(item)}
                />
                <Text style={[styles.actionType, { color: getActionColor(item) }]}>
                  {item.type.toUpperCase()}
                </Text>
              </View>
            </View>

            <Text style={styles.actionDescription}>
              Execute {item.type} for {item.entityType}
            </Text>

            <Text style={styles.actionMeta}>
              {item.entityType.toUpperCase()} • MCP Server Action
            </Text>

            {/* Show data preview if available */}
            {item.data && (
              <View style={styles.dataPreview}>
                <Text style={styles.dataPreviewTitle}>Action Data:</Text>
                <Text style={styles.dataPreviewText} numberOfLines={3}>
                  {typeof item.data === 'object' ?
                    Object.entries(item.data).map(([key, value]) => `${key}: ${value}`).join('\n') :
                    String(item.data)
                  }
                </Text>
              </View>
            )}
          </View>

          <View style={styles.actionStatus}>
            {isExecuting ? (
              <ActivityIndicator size="small" color={colors.primary} />
            ) : isCompleted ? (
              <Text style={styles.completedText}>✓ Done</Text>
            ) : (
              <Ionicons name="chevron-forward" size={20} color={colors.gray[500]} />
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <>
      <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.title}>MCP AI Actions</Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color={colors.gray[800]} />
            </TouchableOpacity>
          </View>

          <Text style={styles.subtitle}>
            Tap on each action to execute it. The MCP server will handle the operation:
          </Text>

          {/* Entity Data Preview */}
          {entityData && (
            <View style={styles.entityDataPreview}>
              <Text style={styles.entityDataPreviewTitle}>📋 Data to be processed:</Text>
              <ScrollView style={styles.entityDataScroll} nestedScrollEnabled>
                {Array.isArray(entityData) ? (
                  <MCPEntityListView
                    entityType={getEntityTypeFromData(entityData)}
                    data={entityData}
                    onItemPress={(selectedItem) => {
                      console.log('Preview selected item:', selectedItem);
                    }}
                  />
                ) : (
                  <Text style={styles.entityDataPreviewText}>
                    {JSON.stringify(entityData, null, 2)}
                  </Text>
                )}
              </ScrollView>
            </View>
          )}

          <FlatList
            data={actions}
            renderItem={renderAction}
            keyExtractor={(item, index) => `${item.type}_${item.entityType}_${index}`}
            style={styles.actionsList}
          />

          <View style={styles.footer}>
            <TouchableOpacity style={styles.finishButton} onPress={handleFinish}>
              <Text style={styles.finishButtonText}>
                Finish ({completedActions.length}/{actions.length} completed)
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Selection Modal for Garden/Field Selection */}
      <Modal
        visible={showSelectionModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowSelectionModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {selectionData?.selectionType === 'garden' ? 'Select Garden' : 'Select Field'}
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowSelectionModal(false)}
              >
                <Ionicons name="close" size={24} color={colors.gray[600]} />
              </TouchableOpacity>
            </View>

            <Text style={styles.selectionMessage}>
              {selectionData?.message}
            </Text>

            <ScrollView style={styles.selectionList}>
              {selectionData?.selectionOptions?.map((option: any, index: number) => (
                <TouchableOpacity
                  key={option.id || index}
                  style={styles.selectionItem}
                  onPress={() => handleSelectionConfirm(option.id)}
                >
                  <View style={styles.selectionItemContent}>
                    <Text style={styles.selectionItemTitle}>
                      {option.name || option.title}
                    </Text>
                    {option.description && (
                      <Text style={styles.selectionItemDescription}>
                        {option.description}
                      </Text>
                    )}
                    {option.type && (
                      <Text style={styles.selectionItemType}>
                        Type: {option.type}
                      </Text>
                    )}
                  </View>
                  <Ionicons name="chevron-forward" size={20} color={colors.gray[400]} />
                </TouchableOpacity>
              ))}
            </ScrollView>

            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => setShowSelectionModal(false)}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.gray[800],
  },
  subtitle: {
    fontSize: 14,
    color: colors.gray[600],
    padding: 20,
    paddingTop: 10,
  },
  actionsList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  actionItem: {
    backgroundColor: colors.white,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.gray[200],
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  actionItemCompleted: {
    backgroundColor: colors.success + '10',
    borderColor: colors.success,
  },
  actionItemExecuting: {
    backgroundColor: colors.primary + '10',
    borderColor: colors.primary,
  },
  actionHeader: {
    flexDirection: 'row',
    padding: 16,
  },
  actionIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  actionInfo: {
    flex: 1,
  },
  actionTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    flex: 1,
    marginRight: 8,
  },
  actionTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  actionType: {
    fontSize: 10,
    fontWeight: '600',
    marginLeft: 4,
  },
  actionDescription: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 8,
    lineHeight: 20,
  },
  actionMeta: {
    fontSize: 12,
    color: colors.gray[500],
    marginBottom: 8,
  },
  dataPreview: {
    backgroundColor: colors.gray[50],
    padding: 8,
    borderRadius: 6,
    marginTop: 4,
  },
  dataPreviewTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.gray[700],
    marginBottom: 4,
  },
  dataPreviewText: {
    fontSize: 11,
    color: colors.gray[600],
    fontFamily: 'monospace',
  },
  actionStatus: {
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  completedText: {
    fontSize: 12,
    color: colors.success,
    fontWeight: '600',
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  finishButton: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  finishButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  // Selection Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: colors.white,
    borderRadius: 16,
    margin: 20,
    maxHeight: '80%',
    width: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.gray[800],
  },
  closeButton: {
    padding: 4,
  },
  selectionMessage: {
    fontSize: 14,
    color: colors.gray[600],
    padding: 20,
    paddingBottom: 10,
  },
  selectionList: {
    maxHeight: 300,
  },
  selectionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginHorizontal: 20,
    marginVertical: 4,
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  selectionItemContent: {
    flex: 1,
  },
  selectionItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 4,
  },
  selectionItemDescription: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 2,
  },
  selectionItemType: {
    fontSize: 12,
    color: colors.gray[500],
  },
  cancelButton: {
    margin: 20,
    padding: 16,
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    color: colors.gray[700],
    fontWeight: '600',
  },
  entityDataPreview: {
    margin: 16,
    padding: 12,
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.gray[200],
    maxHeight: 200,
  },
  entityDataPreviewTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[700],
    marginBottom: 8,
  },
  entityDataScroll: {
    maxHeight: 150,
  },
  entityDataPreviewText: {
    fontSize: 12,
    color: colors.gray[600],
    fontFamily: 'monospace',
  },
});
