import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  Alert,
  Image,
  Modal,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { v4 as uuidv4 } from 'uuid';

// import { useTheme } from '../../contexts/ThemeContext';
// import { useAuth } from '../../contexts/AuthContext';
// import { useFarm } from '../../contexts/FarmContext';
// import { useI18n } from '../../contexts/I18nContext';
import { mcpServerService } from '../../services/mcp-server';
import { 
  MCPChatMessage, 
  MCPSuggestedAction, 
  MCPCandidate,
  MCPUIState 
} from '../../services/mcp-types';
import colors from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';
import { useAuthStore } from '@/store/auth-store';
import { useFarmStore } from '@/store/farm-store';

export default function SmartAIScreen() {
  // const { colors } = useTheme();
  const { user } = useAuthStore();
  const { currentFarm } = useFarmStore();
  const { t, isRTL } = useTranslation();

  // Chat state
  const [messages, setMessages] = useState<MCPChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  
  // UI state
  const [uiState, setUIState] = useState<MCPUIState>({
    isLoading: false,
    isTyping: false,
    showConfirmationModal: false,
    showSelectionModal: false,
  });

  // Refs
  const flatListRef = useRef<FlatList>(null);

  useEffect(() => {
    initializeChat();
  }, [currentFarm, user]);

  const initializeChat = () => {
    const welcomeMessage: MCPChatMessage = {
      id: uuidv4(),
      role: 'assistant',
      content: `Hello! I'm your Smart AI assistant. I can help you manage your farm, analyze images, and answer questions about your crops, animals, and equipment. How can I assist you today?`,
      timestamp: new Date(),
      metadata: { isGreeting: true },
    };
    setMessages([welcomeMessage]);
  };

  const handleSendMessage = async () => {
    if (!inputText.trim() && selectedImages.length === 0) return;
    if (!currentFarm || !user) {
      Alert.alert('Error', 'No farm or user selected');
      return;
    }

    const userMessage: MCPChatMessage = {
      id: uuidv4(),
      role: 'user',
      content: inputText,
      images: selectedImages,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = inputText;
    setInputText('');
    setSelectedImages([]);
    setUIState(prev => ({ ...prev, isLoading: true, isTyping: true }));

    try {
      // Check if we're in interview mode
      if (interviewState.isActive) {
        // Handle interview response
        await handleInterviewResponse(currentInput);
        setUIState(prev => ({ ...prev, isLoading: false, isTyping: false }));
        return;
      }

      const result = await mcpServerService.sendMessage({
        message: currentInput,
        images: selectedImages,
        farmId: currentFarm.id,
        userId: user.id || '',
        language: isRTL ? 'ur' : 'en',
        context: {
          farmId: currentFarm.id,
          userId: user.id || '',
          language: isRTL ? 'ur' : 'en',
        },
      });

      // Simulate typing delay
      setTimeout(() => {
        setUIState(prev => ({ ...prev, isTyping: false }));
        handleMCPResponse(result);
      }, 1000);

    } catch (error) {
      setUIState(prev => ({ ...prev, isTyping: false }));
      console.error('Failed to send message:', error);

      const errorMessage: MCPChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date(),
        metadata: { status: 'error' },
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setUIState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const handleMCPResponse = (result: any) => {
    let assistantMessage: MCPChatMessage;

    if (result.intent === 'list' && result.data) {
      // Handle list responses (plants/animals) with horizontal cards
      const entityType = result.entity || 'items';
      const count = result.count || result.data.length;

      assistantMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: `Found ${count} ${entityType}. Here's what I found:`,
        timestamp: new Date(),
        metadata: {
          status: 'list',
          entityType,
          entityData: result.data,
          count,
          timestamp: result.timestamp,
        },
      };
    } else if (result.status === 'validation_error' || result.errors) {
      // Handle validation errors - check if it's assignee selection
      const missingFields = result.errors || result.missingFields || [];
      const hasAssigneeField = missingFields.some((field: any) => {
        const fieldName = typeof field === 'string' ? field : field.field || field.name;
        return fieldName.toLowerCase().includes('assign') || fieldName.toLowerCase().includes('user');
      });

      if (hasAssigneeField && result.availableUsers) {
        // Show user selection modal instead of interview
        assistantMessage = {
          id: uuidv4(),
          role: 'assistant',
          content: `I've prepared the task details. Now please select who should be assigned to this task:`,
          timestamp: new Date(),
          metadata: {
            status: 'requires_selection',
            followUp: {
              type: 'choose_assignee',
              instructions: 'Select the user to assign this task to:',
              candidates: result.availableUsers.map((user: any) => ({
                id: user.id,
                displayName: user.name || user.displayName,
                role: user.role || 'Team Member',
                lastActive: user.lastActive || 'Recently active',
                avatar: user.photoURL || user.avatar,
              })),
            },
            pendingAction: result.action || 'assign_task',
            pendingData: {
              ...result.data,
              taskType: result.suggestedType || 'maintenance',
              description: result.suggestedDescription || result.data?.description,
            },
          },
        };

        // Show selection modal
        setUIState(prev => ({
          ...prev,
          showSelectionModal: true,
          selectionType: 'choose_assignee',
          candidates: result.availableUsers.map((user: any) => ({
            id: user.id,
            displayName: user.name || user.displayName,
            role: user.role || 'Team Member',
            lastActive: user.lastActive || 'Recently active',
            avatar: user.photoURL || user.avatar,
          })),
          pendingAction: result.action || 'assign_task',
          pendingData: {
            ...result.data,
            taskType: result.suggestedType || 'maintenance',
            description: result.suggestedDescription || result.data?.description,
          },
        }));
      } else {
        // Regular interview for other missing fields
        const fieldNames = missingFields.map((field: any) =>
          typeof field === 'string' ? field : field.field || field.name
        ).join(', ');

        assistantMessage = {
          id: uuidv4(),
          role: 'assistant',
          content: `I need some additional information to complete this action. Let me ask you about the missing details: ${fieldNames}`,
          timestamp: new Date(),
          metadata: {
            status: 'interview',
            missingFields,
            pendingAction: result.action || result.intent,
            pendingData: result.data,
            currentFieldIndex: 0,
          },
        };

        // Start interview process
        startFieldInterview(missingFields, result.data, result.action || result.intent);
      }
    } else if (result.status === 'analyzed' && result.suggestedActions) {
      // Response with suggested actions (data is complete)
      assistantMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: result.message || result.summary || 'Great! I have all the information needed. Here are the actions you can take:',
        timestamp: new Date(),
        metadata: {
          status: result.status,
          suggestedActions: result.suggestedActions,
          data: result.data,
          followUp: result.followUp,
          requiresConfirmation: result.meta?.requiresConfirmation,
        },
      };
    } else if (result.status === 'requires_selection' && result.followUp) {
      // Response requiring selection (e.g., choose assignee)
      assistantMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: result.followUp.instructions || 'Please make a selection:',
        timestamp: new Date(),
        metadata: {
          status: result.status,
          followUp: result.followUp,
          pendingAction: result.action,
          pendingData: result.data,
        },
      };

      // Show selection modal
      setUIState(prev => ({
        ...prev,
        showSelectionModal: true,
        selectionType: result.followUp.type,
        candidates: result.followUp.candidates,
        pendingAction: result.action,
        pendingData: result.data,
      }));
    } else if (result.status === 'success') {
      // Successful completion
      assistantMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: result.message || 'Action completed successfully!',
        timestamp: new Date(),
        metadata: {
          status: result.status,
          entityId: result.entityId,
        },
      };
    } else {
      // General response
      assistantMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: result.message || result.summary || 'I understand your request.',
        timestamp: new Date(),
        metadata: result,
      };
    }

    setMessages(prev => [...prev, assistantMessage]);
  };

  // Interview state
  const [interviewState, setInterviewState] = useState<{
    isActive: boolean;
    fields: any[];
    currentIndex: number;
    collectedData: any;
    pendingAction: string;
  }>({
    isActive: false,
    fields: [],
    currentIndex: 0,
    collectedData: {},
    pendingAction: '',
  });

  const startFieldInterview = (missingFields: any[], existingData: any, action: string) => {
    setInterviewState({
      isActive: true,
      fields: missingFields,
      currentIndex: 0,
      collectedData: existingData || {},
      pendingAction: action,
    });

    // Ask for the first missing field
    if (missingFields.length > 0) {
      const firstField = missingFields[0];
      const fieldName = typeof firstField === 'string' ? firstField : firstField.field || firstField.name;
      const fieldPrompt = typeof firstField === 'object' ? firstField.prompt : `Please provide the ${fieldName}:`;

      const interviewMessage: MCPChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: fieldPrompt,
        timestamp: new Date(),
        metadata: {
          status: 'interview_question',
          fieldName,
          fieldIndex: 0,
          totalFields: missingFields.length,
        },
      };

      setMessages(prev => [...prev, interviewMessage]);
    }
  };

  const handleInterviewResponse = async (userResponse: string) => {
    if (!interviewState.isActive) return;

    const currentField = interviewState.fields[interviewState.currentIndex];
    const fieldName = typeof currentField === 'string' ? currentField : currentField.field || currentField.name;

    // Collect the response
    const updatedData = {
      ...interviewState.collectedData,
      [fieldName]: userResponse,
    };

    const nextIndex = interviewState.currentIndex + 1;

    if (nextIndex < interviewState.fields.length) {
      // Ask next question
      const nextField = interviewState.fields[nextIndex];
      const nextFieldName = typeof nextField === 'string' ? nextField : nextField.field || nextField.name;
      const nextFieldPrompt = typeof nextField === 'object' ? nextField.prompt : `Please provide the ${nextFieldName}:`;

      setInterviewState(prev => ({
        ...prev,
        currentIndex: nextIndex,
        collectedData: updatedData,
      }));

      const nextQuestion: MCPChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: nextFieldPrompt,
        timestamp: new Date(),
        metadata: {
          status: 'interview_question',
          fieldName: nextFieldName,
          fieldIndex: nextIndex,
          totalFields: interviewState.fields.length,
        },
      };

      setMessages(prev => [...prev, nextQuestion]);
    } else {
      // Interview complete - send complete data to MCP
      setInterviewState(prev => ({ ...prev, isActive: false }));

      const completionMessage: MCPChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: 'Perfect! I now have all the information needed. Let me process this for you...',
        timestamp: new Date(),
        metadata: {
          status: 'interview_complete',
        },
      };

      setMessages(prev => [...prev, completionMessage]);

      // Send complete data back to MCP for processing
      try {
        const result = await mcpServerService.sendMessage({
          message: `Complete ${interviewState.pendingAction} with collected data`,
          images: [],
          farmId: currentFarm?.id || '',
          userId: user?.id || '',
          language: isRTL ? 'ur' : 'en',
          context: {
            farmId: currentFarm?.id || '',
            userId: user?.id || '',
            language: isRTL ? 'ur' : 'en',
            completeData: updatedData,
            action: interviewState.pendingAction,
          },
        });

        handleMCPResponse(result);
      } catch (error) {
        console.error('Failed to process complete data:', error);
        const errorMessage: MCPChatMessage = {
          id: uuidv4(),
          role: 'assistant',
          content: 'Sorry, I encountered an error processing your information. Please try again.',
          timestamp: new Date(),
          metadata: { status: 'error' },
        };
        setMessages(prev => [...prev, errorMessage]);
      }
    }
  };

  const handleActionPress = async (action: MCPSuggestedAction) => {
    if (!currentFarm || !user) return;
    
    try {
      setUIState(prev => ({ ...prev, isLoading: true }));

      // Check if action requires confirmation
      if (action.data && Object.keys(action.data).length > 0) {
        // Show confirmation modal
        setUIState(prev => ({
          ...prev,
          showConfirmationModal: true,
          pendingAction: action.action,
          pendingData: action.data,
          isLoading: false,
        }));
      } else {
        // Direct confirmation
        const result = await mcpServerService.confirmAction(
          action.action,
          action.data,
          currentFarm.id
        );

        handleMCPResponse(result);
      }
    } catch (error) {
      console.error('Action execution error:', error);
      
      const errorMessage: MCPChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: 'Failed to execute action. Please try again.',
        timestamp: new Date(),
        metadata: { status: 'error' },
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setUIState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const handleSelectionConfirm = async (candidate: MCPCandidate) => {
    if (!currentFarm || !uiState.pendingAction) return;

    try {
      setUIState(prev => ({ ...prev, showSelectionModal: false, isLoading: true }));

      const result = await mcpServerService.resolveSelection(
        uiState.pendingAction,
        candidate,
        currentFarm.id
      );

      handleMCPResponse(result);
    } catch (error) {
      console.error('Selection error:', error);
      Alert.alert('Error', 'Failed to process selection');
    } finally {
      setUIState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const handleConfirmAction = async () => {
    if (!currentFarm || !uiState.pendingAction || !uiState.pendingData) return;

    try {
      setUIState(prev => ({ ...prev, showConfirmationModal: false, isLoading: true }));

      const result = await mcpServerService.confirmAction(
        uiState.pendingAction,
        uiState.pendingData,
        currentFarm.id
      );

      handleMCPResponse(result);
    } catch (error) {
      console.error('Confirmation error:', error);
      Alert.alert('Error', 'Failed to confirm action');
    } finally {
      setUIState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const handleImagePicker = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      setSelectedImages([result.assets[0].uri]);
    }
  };

  const handleCamera = async () => {
    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      setSelectedImages([result.assets[0].uri]);
    }
  };

  return (
    <KeyboardAvoidingView 
      style={{ flex: 1, backgroundColor: colors.background }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Header */}
      <LinearGradient
        colors={[colors.primary, colors.secondary]}
        style={{
          paddingTop: 50,
          paddingBottom: 20,
          paddingHorizontal: 20,
        }}
      >
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Ionicons name="sparkles" size={24} color={colors.white} />
          <Text style={{
            fontSize: 20,
            fontWeight: 'bold',
            color: colors.white,
            marginLeft: 10,
          }}>
            Smart AI Assistant
          </Text>
        </View>
        <Text style={{
          fontSize: 14,
          color: colors.white,
          opacity: 0.8,
          marginTop: 4,
        }}>
          Powered by Advanced AI
        </Text>
      </LinearGradient>

      {/* Messages List */}
      <FlatList
        ref={flatListRef}
        data={messages}
        keyExtractor={(item) => item.id}
        renderItem={renderMessage}
        style={{ flex: 1, padding: 16 }}
        onContentSizeChange={() => flatListRef.current?.scrollToEnd()}
        showsVerticalScrollIndicator={false}
      />

      {/* Typing Indicator */}
      {uiState.isTyping && renderTypingIndicator()}

      {/* Input Area */}
      <View style={{
        flexDirection: 'row',
        padding: 16,
        backgroundColor: colors.surface,
        alignItems: 'flex-end',
      }}>
        {/* Image Preview */}
        {selectedImages.length > 0 && (
          <View style={{ marginRight: 8, marginBottom: 8 }}>
            <Image
              source={{ uri: selectedImages[0] }}
              style={{ width: 40, height: 40, borderRadius: 8 }}
            />
            <TouchableOpacity
              onPress={() => setSelectedImages([])}
              style={{
                position: 'absolute',
                top: -8,
                right: -8,
                backgroundColor: colors.error,
                borderRadius: 10,
                width: 20,
                height: 20,
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Ionicons name="close" size={12} color={colors.white} />
            </TouchableOpacity>
          </View>
        )}

        {/* Text Input */}
        <TextInput
          value={inputText}
          onChangeText={setInputText}
          placeholder="Type your message..."
          placeholderTextColor={colors.gray[400]}
          style={{
            flex: 1,
            borderWidth: 1,
            borderColor: colors.gray[300],
            borderRadius: 20,
            paddingHorizontal: 16,
            paddingVertical: 12,
            marginRight: 8,
            maxHeight: 100,
            backgroundColor: colors.white,
          }}
          multiline
        />

        {/* Action Buttons */}
        <View style={{ flexDirection: 'row' }}>
          <TouchableOpacity
            onPress={handleImagePicker}
            style={{
              padding: 12,
              backgroundColor: colors.gray[100],
              borderRadius: 20,
              marginRight: 8,
            }}
          >
            <Ionicons name="image" size={20} color={colors.primary} />
          </TouchableOpacity>

          <TouchableOpacity
            onPress={handleCamera}
            style={{
              padding: 12,
              backgroundColor: colors.gray[100],
              borderRadius: 20,
              marginRight: 8,
            }}
          >
            <Ionicons name="camera" size={20} color={colors.primary} />
          </TouchableOpacity>

          <TouchableOpacity
            onPress={handleSendMessage}
            disabled={uiState.isLoading || (!inputText.trim() && selectedImages.length === 0)}
            style={{
              padding: 12,
              backgroundColor: colors.primary,
              borderRadius: 20,
              opacity: uiState.isLoading || (!inputText.trim() && selectedImages.length === 0) ? 0.5 : 1,
            }}
          >
            <Ionicons name="send" size={20} color={colors.white} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Selection Modal */}
      {renderSelectionModal()}

      {/* Confirmation Modal */}
      {renderConfirmationModal()}
    </KeyboardAvoidingView>
  );

  // Render message item
  function renderMessage({ item }: { item: MCPChatMessage }) {
    return (
      <View style={{
        marginBottom: 16,
        alignSelf: item.role === 'user' ? 'flex-end' : 'flex-start',
        maxWidth: '80%',
      }}>
        <View style={{
          backgroundColor: item.role === 'user' ? colors.primary : colors.white,
          padding: 12,
          borderRadius: 16,
          borderWidth: item.role === 'assistant' ? 1 : 0,
          borderColor: colors.gray[200],
        }}>
          {/* Greeting Header */}
          {item.metadata?.isGreeting && (
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 8,
            }}>
              <Ionicons name="hand-left" size={18} color={colors.primary} />
              <Text style={{
                marginLeft: 8,
                fontSize: 12,
                color: colors.primary,
                fontWeight: '600',
              }}>
                Smart AI Assistant
              </Text>
            </View>
          )}

          {/* Message Content */}
          <Text style={{
            color: item.role === 'user' ? colors.white : colors.text,
            fontSize: 16,
            lineHeight: 22,
          }}>
            {item.content}
          </Text>

          {/* Images */}
          {item.images && item.images.length > 0 && (
            <View style={{ marginTop: 8 }}>
              {item.images.map((imageUri, index) => (
                <Image
                  key={index}
                  source={{ uri: imageUri }}
                  style={{
                    width: 200,
                    height: 150,
                    borderRadius: 8,
                    marginTop: index > 0 ? 8 : 0,
                  }}
                  resizeMode="cover"
                />
              ))}
            </View>
          )}

          {/* Entity Data Cards (Horizontal Scroll) */}
          {item.metadata?.status === 'list' && item.metadata.entityData && (
            <View style={{ marginTop: 12,maxHeight:200, }}>
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: colors.gray[600],
                marginBottom: 8,
              }}>
                {item.metadata.count} {item.metadata.entityType} found
              </Text>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={{ marginHorizontal: -12, }}
                contentContainerStyle={{ paddingHorizontal: 12, }}
              >
                {item.metadata.entityData.slice(0, 10).map((entity: any, index: number) => (
                  <View
                    key={entity.id || index}
                    style={{
                      backgroundColor: colors.white,
                      borderRadius: 12,
                      padding: 12,
                      marginRight: 12,
                      width: 200,
                      // height:150,
                      borderWidth: 1,
                      borderColor: colors.gray[200],
                      shadowColor: '#000',
                      shadowOffset: { width: 0, height: 2 },
                      shadowOpacity: 0.1,
                      shadowRadius: 4,
                      elevation: 3,
                    }}
                  >
                    {/* Entity Image */}
                    {entity.photoURL && (
                      <Image
                        source={{ uri: entity.photoURL }}
                        style={{
                          width: '100%',
                          height: 100,
                          borderRadius: 8,
                          marginBottom: 8,
                        }}
                        resizeMode="cover"
                      />
                    )}

                    {/* Entity Info */}
                    <Text style={{
                      fontSize: 16,
                      fontWeight: '600',
                      color: colors.gray[800],
                      marginBottom: 4,
                    }} numberOfLines={1}>
                      {entity.name || entity.species || 'Unknown'}
                    </Text>

                    {/* Entity Type/Breed */}
                    {(entity.type || entity.breed || entity.variety) && (
                      <Text style={{
                        fontSize: 12,
                        color: colors.gray[600],
                        marginBottom: 4,
                      }} numberOfLines={1}>
                        {entity.type || entity.breed || entity.variety}
                      </Text>
                    )}

                    {/* Status Badge */}
                    {(entity.status || entity.healthStatus) && (
                      <View style={{
                        backgroundColor:
                          (entity.status === 'healthy' || entity.healthStatus === 'healthy' || entity.healthStatus === 'Healthy')
                            ? colors.success
                            : colors.warning,
                        paddingHorizontal: 8,
                        paddingVertical: 4,
                        borderRadius: 12,
                        alignSelf: 'flex-start',
                        marginBottom: 4,
                      }}>
                        <Text style={{
                          fontSize: 10,
                          color: colors.white,
                          fontWeight: '600',
                        }}>
                          {entity.status || entity.healthStatus}
                        </Text>
                      </View>
                    )}

                    {/* Additional Info */}
                    {entity.quantity && (
                      <Text style={{
                        fontSize: 12,
                        color: colors.gray[500],
                      }}>
                        Qty: {entity.quantity} {entity.unitOfMeasure || ''}
                      </Text>
                    )}

                    {/* Date Info */}
                    {(entity.addedAt || entity.createdAt || entity.plantingDate || entity.acquisitionDate) && (
                      <Text style={{
                        fontSize: 10,
                        color: colors.gray[400],
                        marginTop: 4,
                      }}>
                        {(() => {
                          const dateField = entity.addedAt || entity.createdAt || entity.plantingDate || entity.acquisitionDate;
                          if (dateField?._seconds) {
                            return new Date(dateField._seconds * 1000).toLocaleDateString();
                          }
                          return 'Recently added';
                        })()}
                      </Text>
                    )}
                  </View>
                ))}

                {/* Show More Card */}
                {item.metadata.entityData.length > 10 && (
                  <TouchableOpacity
                    style={{
                      backgroundColor: colors.gray[50],
                      borderRadius: 12,
                      padding: 12,
                      width: 120,
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderWidth: 1,
                      borderColor: colors.gray[200],
                      borderStyle: 'dashed',
                    }}
                  >
                    <Ionicons name="add-circle-outline" size={24} color={colors.primary} />
                    <Text style={{
                      fontSize: 12,
                      color: colors.primary,
                      fontWeight: '600',
                      marginTop: 4,
                      textAlign: 'center',
                    }}>
                      +{item.metadata.entityData.length - 10} more
                    </Text>
                  </TouchableOpacity>
                )}
              </ScrollView>
            </View>
          )}

          {/* Suggested Actions */}
          {item.metadata?.suggestedActions && item.metadata.suggestedActions.length > 0 && (
            <View style={{ marginTop: 12 }}>
              {item.metadata.suggestedActions.map((action, index) => (
                <TouchableOpacity
                  key={index}
                  onPress={() => handleActionPress(action)}
                  style={{
                    backgroundColor: action.type === 'primary' ? colors.primary :
                                   action.type === 'danger' ? colors.danger : colors.gray[100],
                    paddingHorizontal: 16,
                    paddingVertical: 10,
                    borderRadius: 20,
                    marginTop: index > 0 ? 8 : 0,
                    alignSelf: 'flex-start',
                  }}
                >
                  <Text style={{
                    color: action.type === 'primary' || action.type === 'danger' ? colors.white : colors.gray[800],
                    fontWeight: '600',
                    fontSize: 14,
                  }}>
                    {action.title}
                  </Text>
                  {action.description && (
                    <Text style={{
                      color: action.type === 'primary' || action.type === 'danger' ? colors.white : colors.gray[600],
                      fontSize: 12,
                      marginTop: 2,
                    }}>
                      {action.description}
                    </Text>
                  )}
                </TouchableOpacity>
              ))}
            </View>
          )}

          {/* Follow-up Selection */}
          {item.metadata?.followUp && (
            <View style={{ marginTop: 12 }}>
              <Text style={{
                color: colors.gray[600],
                fontSize: 14,
                marginBottom: 8,
              }}>
                {item.metadata.followUp.instructions}
              </Text>
              {item.metadata.followUp.candidates?.slice(0, 3).map((candidate, index) => (
                <TouchableOpacity
                  key={candidate.id}
                  onPress={() => handleSelectionConfirm(candidate)}
                  style={{
                    backgroundColor: colors.gray[50],
                    padding: 12,
                    borderRadius: 8,
                    marginTop: index > 0 ? 8 : 0,
                    borderWidth: 1,
                    borderColor: colors.gray[200],
                  }}
                >
                  <Text style={{
                    color: colors.text,
                    fontWeight: '600',
                    fontSize: 14,
                  }}>
                    {candidate.displayName}
                  </Text>
                  {candidate.role && (
                    <Text style={{
                      color: colors.gray[600],
                      fontSize: 12,
                      marginTop: 2,
                    }}>
                      {candidate.role}
                    </Text>
                  )}
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>

        {/* Timestamp */}
        <Text style={{
          fontSize: 12,
          color: colors.gray[500],
          marginTop: 4,
          textAlign: item.role === 'user' ? 'right' : 'left',
        }}>
          {item.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </Text>
      </View>
    );
  }

  // Render typing indicator
  function renderTypingIndicator() {
    return (
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 8,
      }}>
        <View style={{
          backgroundColor: colors.white,
          padding: 12,
          borderRadius: 16,
          borderWidth: 1,
          borderColor: colors.gray[200],
          flexDirection: 'row',
          alignItems: 'center',
        }}>
          <View style={{
            width: 8,
            height: 8,
            borderRadius: 4,
            backgroundColor: colors.primary,
            marginRight: 4,
          }} />
          <View style={{
            width: 8,
            height: 8,
            borderRadius: 4,
            backgroundColor: colors.primary,
            marginRight: 4,
            opacity: 0.7,
          }} />
          <View style={{
            width: 8,
            height: 8,
            borderRadius: 4,
            backgroundColor: colors.primary,
            opacity: 0.4,
          }} />
          <Text style={{
            marginLeft: 8,
            color: colors.gray[600],
            fontSize: 14,
          }}>
            AI is thinking...
          </Text>
        </View>
      </View>
    );
  }

  // Render selection modal
  function renderSelectionModal() {
    return (
      <Modal
        visible={uiState.showSelectionModal}
        transparent
        animationType="slide"
        onRequestClose={() => setUIState(prev => ({ ...prev, showSelectionModal: false }))}
      >
        <View style={{
          flex: 1,
          backgroundColor: 'rgba(0,0,0,0.5)',
          justifyContent: 'flex-end',
        }}>
          <View style={{
            backgroundColor: colors.white,
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            padding: 20,
            maxHeight: '70%',
          }}>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: colors.text,
              marginBottom: 16,
            }}>
              Make a Selection
            </Text>

            <ScrollView>
              {uiState.candidates?.map((candidate) => (
                <TouchableOpacity
                  key={candidate.id}
                  onPress={() => handleSelectionConfirm(candidate)}
                  style={{
                    backgroundColor: colors.white,
                    padding: 16,
                    borderRadius: 12,
                    marginBottom: 12,
                    borderWidth: 1,
                    borderColor: colors.gray[200],
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 2 },
                    shadowOpacity: 0.1,
                    shadowRadius: 4,
                    elevation: 3,
                  }}
                >
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    {/* User Avatar */}
                    <View style={{
                      width: 48,
                      height: 48,
                      borderRadius: 24,
                      backgroundColor: colors.primary,
                      justifyContent: 'center',
                      alignItems: 'center',
                      marginRight: 12,
                    }}>
                      {candidate.avatar ? (
                        <Image
                          source={{ uri: candidate.avatar }}
                          style={{ width: 48, height: 48, borderRadius: 24 }}
                        />
                      ) : (
                        <Text style={{
                          color: colors.white,
                          fontSize: 18,
                          fontWeight: 'bold',
                        }}>
                          {candidate.displayName?.charAt(0)?.toUpperCase() || 'U'}
                        </Text>
                      )}
                    </View>

                    {/* User Info */}
                    <View style={{ flex: 1 }}>
                      <Text style={{
                        fontSize: 16,
                        fontWeight: '600',
                        color: colors.gray[800],
                      }}>
                        {candidate.displayName}
                      </Text>
                      {candidate.role && (
                        <Text style={{
                          fontSize: 14,
                          color: colors.gray[600],
                          marginTop: 2,
                        }}>
                          {candidate.role}
                        </Text>
                      )}
                      {candidate.lastActive && (
                        <Text style={{
                          fontSize: 12,
                          color: colors.gray[500],
                          marginTop: 2,
                        }}>
                          {candidate.lastActive}
                        </Text>
                      )}
                    </View>

                    {/* Selection Indicator */}
                    <View style={{
                      width: 24,
                      height: 24,
                      borderRadius: 12,
                      borderWidth: 2,
                      borderColor: colors.primary,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                      <View style={{
                        width: 12,
                        height: 12,
                        borderRadius: 6,
                        backgroundColor: colors.primary,
                      }} />
                    </View>
                  </View>
                </TouchableOpacity>
              ))}
            </ScrollView>

            <TouchableOpacity
              onPress={() => setUIState(prev => ({ ...prev, showSelectionModal: false }))}
              style={{
                backgroundColor: colors.gray[200],
                padding: 16,
                borderRadius: 12,
                marginTop: 16,
                alignItems: 'center',
              }}
            >
              <Text style={{
                color: colors.gray[800],
                fontWeight: '600',
              }}>
                Cancel
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  }

  // Render confirmation modal
  function renderConfirmationModal() {
    return (
      <Modal
        visible={uiState.showConfirmationModal}
        transparent
        animationType="slide"
        onRequestClose={() => setUIState(prev => ({ ...prev, showConfirmationModal: false }))}
      >
        <View style={{
          flex: 1,
          backgroundColor: 'rgba(0,0,0,0.5)',
          justifyContent: 'center',
          padding: 20,
        }}>
          <View style={{
            backgroundColor: colors.white,
            borderRadius: 16,
            padding: 20,
          }}>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: colors.gray[800],
              marginBottom: 16,
            }}>
              Confirm Action
            </Text>

            <Text style={{
              fontSize: 16,
              color: colors.gray[800],
              marginBottom: 20,
              lineHeight: 22,
            }}>
              Please review the task details and confirm to proceed:
            </Text>

            {/* Task Preview */}
            {uiState.pendingData && (
              <View style={{
                backgroundColor: colors.white,
                padding: 16,
                borderRadius: 12,
                marginBottom: 20,
                borderWidth: 1,
                borderColor: colors.gray[200],
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 4,
                elevation: 3,
              }}>
                <Text style={{
                  fontSize: 16,
                  fontWeight: '600',
                  color: colors.primary,
                  marginBottom: 12,
                }}>
                  📋 Task Preview
                </Text>

                {/* Task Title */}
                {uiState.pendingData.title && (
                  <View style={{ marginBottom: 8 }}>
                    <Text style={{ fontSize: 12, color: colors.gray[500], marginBottom: 2 }}>Title:</Text>
                    <Text style={{ fontSize: 14, fontWeight: '600', color: colors.gray[800] }}>
                      {uiState.pendingData.title}
                    </Text>
                  </View>
                )}

                {/* Task Description */}
                {uiState.pendingData.description && (
                  <View style={{ marginBottom: 8 }}>
                    <Text style={{ fontSize: 12, color: colors.gray[500], marginBottom: 2 }}>Description:</Text>
                    <Text style={{ fontSize: 14, color: colors.gray[700] }}>
                      {uiState.pendingData.description}
                    </Text>
                  </View>
                )}

                {/* Task Type */}
                {uiState.pendingData.taskType && (
                  <View style={{ marginBottom: 8 }}>
                    <Text style={{ fontSize: 12, color: colors.gray[500], marginBottom: 2 }}>Type:</Text>
                    <View style={{
                      backgroundColor: colors.primaryLight,
                      paddingHorizontal: 8,
                      paddingVertical: 4,
                      borderRadius: 12,
                      alignSelf: 'flex-start',
                    }}>
                      <Text style={{ fontSize: 12, color: colors.primary, fontWeight: '600' }}>
                        {uiState.pendingData.taskType}
                      </Text>
                    </View>
                  </View>
                )}

                {/* Assignee */}
                {uiState.pendingData.assignedTo && (
                  <View style={{ marginBottom: 8 }}>
                    <Text style={{ fontSize: 12, color: colors.gray[500], marginBottom: 2 }}>Assigned to:</Text>
                    <Text style={{ fontSize: 14, fontWeight: '600', color: colors.gray[800] }}>
                      {uiState.pendingData.assignedTo.displayName || uiState.pendingData.assignedTo.name}
                    </Text>
                  </View>
                )}
              </View>
            )}

            <View style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
              <TouchableOpacity
                onPress={() => setUIState(prev => ({ ...prev, showConfirmationModal: false }))}
                style={{
                  flex: 1,
                  backgroundColor: colors.gray[200],
                  padding: 16,
                  borderRadius: 12,
                  marginRight: 8,
                  alignItems: 'center',
                }}
              >
                <Text style={{
                  color: colors.gray[800],
                  fontWeight: '600',
                }}>
                  Cancel
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={handleConfirmAction}
                style={{
                  flex: 1,
                  backgroundColor: colors.primary,
                  padding: 16,
                  borderRadius: 12,
                  marginLeft: 8,
                  alignItems: 'center',
                }}
              >
                <Text style={{
                  color: colors.white,
                  fontWeight: '600',
                }}>
                  Confirm
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  }
}
