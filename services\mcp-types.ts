/**
 * MCP (Model Context Protocol) Types
 * 
 * Complete type definitions for the MCP chat assistant with conversational UI
 */

export interface MCPServerConfig {
  baseUrl: string;
  apiKey?: string;
  timeout?: number;
}

export interface MCPRequest {
  inputType: 'text' | 'image' | 'both';
  query?: string;
  imageBase64?: string;
  farmId: string;
  intent?: 'create' | 'list' | 'resolve' | 'confirm' | 'edit';
  action?: string;
  confirmedData?: any;
  selection?: any;
}

export interface MCPResponse {
  status: 'analyzed' | 'success' | 'error' | 'requires_selection' | 'requires_confirmation' | 'validation_error' | 'list';
  message?: string;
  summary?: string;
  data?: any;
  entityId?: string;
  suggestedActions?: MCPSuggestedAction[];
  followUp?: {
    type: 'choose_assignee' | 'choose_location' | 'choose_category';
    instructions: string;
    candidates: MCPCandidate[];
  };
  meta?: {
    requiresConfirmation?: boolean;
    missingFields?: string[];
    ambiguousFields?: string[];
  };
  error?: string;
  // List response fields
  intent?: string;
  entity?: string;
  count?: number;
  timestamp?: string;
  // Validation error fields
  errors?: any[];
  missingFields?: any[];
  action?: string;
  // Enhanced task assignment fields
  availableUsers?: any[];
  suggestedType?: string;
  suggestedDescription?: string;
}

export interface MCPSuggestedAction {
  action: string;
  title: string;
  description?: string;
  type: 'primary' | 'secondary' | 'danger';
  data?: any;
}

export interface MCPCandidate {
  id: string;
  displayName: string;
  role?: string;
  lastActive?: string;
  avatar?: string;
  metadata?: any;
}

export interface MCPMessageRequest {
  message: string;
  images?: string[];
  farmId: string;
  userId: string;
  language: string;
  context?: {
    farmId: string;
    userId: string;
    language: string;
    currentEntities?: any[];
    completeData?: any;
    action?: string;
  };
}

export interface MCPChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  images?: string[];
  timestamp: Date;
  metadata?: {
    status?: 'analyzed' | 'success' | 'error' | 'requires_selection' | 'requires_confirmation' | 'list' | 'interview' | 'interview_question' | 'interview_complete';
    summary?: string;
    entityData?: any;
    suggestedActions?: MCPSuggestedAction[];
    followUp?: {
      type: 'choose_assignee' | 'choose_location' | 'choose_category';
      instructions: string;
      candidates: MCPCandidate[];
    };
    data?: any;
    entityId?: string;
    isGreeting?: boolean;
    requiresConfirmation?: boolean;
    pendingAction?: string;
    pendingData?: any;
    // List metadata
    entityType?: string;
    count?: number;
    timestamp?: string;
    // Interview metadata
    missingFields?: any[];
    currentFieldIndex?: number;
    fieldName?: string;
    fieldIndex?: number;
    totalFields?: number;
  };
}

export interface MCPActionResponse {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
  entityId?: string;
}

// UI State Types
export interface MCPUIState {
  isLoading: boolean;
  isTyping: boolean;
  showConfirmationModal: boolean;
  showSelectionModal: boolean;
  pendingAction?: string;
  pendingData?: any;
  selectionType?: 'choose_assignee' | 'choose_location' | 'choose_category';
  candidates?: MCPCandidate[];
}
