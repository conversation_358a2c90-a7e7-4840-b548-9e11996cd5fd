import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  Image,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Stack } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { AIAssistantService, ChatMessage, SuggestedAction, EntityReference, EntityListing } from '@/services/ai-assistant';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import { colors } from '@/constants/colors';
import { StyleSheet } from 'react-native';
import { v4 as uuidv4 } from 'uuid';
import { EntityReferenceCard } from '@/components/chat/EntityReferenceCard';
import { EntityListingSection } from '@/components/chat/EntityListingSection';
import { SpecialActionHandler } from '@/components/chat/SpecialActionHandler';
import { useTranslation } from '@/i18n/useTranslation';
import ActionConfirmationModal from '@/components/ActionConfirmationModal';

export default function AIAssistantScreen() {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showActionModal, setShowActionModal] = useState(false);
  const [pendingActions, setPendingActions] = useState<SuggestedAction[]>([]);
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImageUrl, setSelectedImageUrl] = useState('');

  const flatListRef = useRef<FlatList>(null);
  const { currentFarm, animals, plants, gardens, fields, equipment, farmUsers } = useFarmStore();
  const { user } = useAuthStore();
  const { t, locale } = useTranslation();
  const aiService = AIAssistantService.getInstance();

  useEffect(() => {
    // Add welcome message based on user's language
    const welcomeMessage: ChatMessage = {
      id: uuidv4(),
      role: 'assistant',
      content: locale === 'ur'
        ? 'السلام علیکم! میں آپ کا AI فارم اسسٹنٹ ہوں۔ میں تصاویر کا تجزیہ کرنے، آپ کے فارم کے ڈیٹا کو منظم کرنے، اور زرعی مشورے فراہم کرنے میں آپ کی مدد کر سکتا ہوں۔ آج میں آپ کی کیسے مدد کر سکتا ہوں؟'
        : 'Hello! I\'m your AI farm assistant. I can help you analyze images, manage your farm data, and provide agricultural advice. How can I help you today?',
      timestamp: new Date(),
    };
    setMessages([welcomeMessage]);
  }, [locale]);

  // Scroll to end when messages change
  useEffect(() => {
    if (messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 200);
    }
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputText.trim() && selectedImages.length === 0) return;
    if (!currentFarm) {
      Alert.alert('Error', 'No farm selected');
      return;
    }

    const userMessage: ChatMessage = {
      id: uuidv4(),
      role: 'user',
      content: inputText,
      images: selectedImages,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setSelectedImages([]);
    setIsLoading(true);

    try {
      const context = {
        farmId: currentFarm.id,
        currentEntities: [
          ...animals,
          ...plants,
          ...gardens,
          ...fields,
          ...equipment,
          ...farmUsers,
        ],
      };

      const result = await aiService.analyzeMessage(
        inputText,
        selectedImages,
        context,
        locale // Pass user's language preference
      );

      const assistantMessage: ChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: result.response,
        timestamp: new Date(),
        metadata: {
          suggestedActions: result.suggestedActions,
          analysisData: result.analysisData,
          entityReferences: result.entityReferences,
          entityListings: result.entityListings,
          showEntityData: result.showEntityData,
        },
      };

      setMessages(prev => [...prev, assistantMessage]);

      // Show action confirmation if there are suggested actions
      if (result.suggestedActions.length > 0) {
        setPendingActions(result.suggestedActions);
        setShowActionModal(true);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: ChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: 'Sorry, I encountered an error processing your request. Please try again.',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleImagePicker = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsMultipleSelection: true,
      quality: 0.8,
      base64: false,
    });

    if (!result.canceled) {
      const newImages = result.assets.map(asset => asset.uri);
      setSelectedImages(prev => [...prev, ...newImages]);
    }
  };

  const handleCamera = async () => {
    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      quality: 0.8,
      base64: false,
    });

    if (!result.canceled) {
      setSelectedImages(prev => [...prev, result.assets[0].uri]);
    }
  };

  const removeImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
  };

  const renderMessage = ({ item }: { item: ChatMessage }) => (
    <View style={[
      styles.messageContainer,
      item.role === 'user' ? styles.userMessage : styles.assistantMessage
    ]}>
      <Text style={[
        styles.messageText,
        item.role === 'user' ? styles.userMessageText : styles.assistantMessageText
      ]}>
        {item.content}
      </Text>
      
      {item.images && item.images.length > 0 && (
        <View style={styles.messageImages}>
          {item.images.map((uri, index) => (
            <TouchableOpacity
              key={index}
              onPress={() => {
                setSelectedImageUrl(uri);
                setShowImageModal(true);
              }}
            >
              <Image
                source={{ uri }}
                style={styles.messageImage}
                resizeMode="cover"
              />
            </TouchableOpacity>
          ))}
        </View>
      )}

      {/* Entity References */}
      {item.metadata?.entityReferences && item.metadata.entityReferences.length > 0 && (
        <View style={styles.entityReferencesContainer}>
          {item.metadata.entityReferences.map((entity, index) => (
            <EntityReferenceCard
              key={entity.id}
              entity={entity}
              style={styles.entityReference}
            />
          ))}
        </View>
      )}

      {/* Entity Listings */}
      {item.metadata?.entityListings && item.metadata.entityListings.length > 0 && (
        <View style={styles.entityListingsContainer}>
          {item.metadata.entityListings.map((listing, index) => (
            <EntityListingSection
              key={`${listing.type}-${index}`}
              listing={listing}
              style={styles.entityListing}
            />
          ))}
        </View>
      )}

      {/* Special Action Handler for AI Assistant */}
      {item.role === 'assistant' && (
        <SpecialActionHandler
          message={item.content}
          onActionTaken={(action) => {
            console.log('Special action taken:', action);
          }}
        />
      )}

      {item.metadata?.suggestedActions && item.metadata.suggestedActions.length > 0 && (
        <TouchableOpacity
          style={styles.actionsButton}
          onPress={() => {
            if (item.metadata?.suggestedActions) {
              setPendingActions(item.metadata.suggestedActions);
              setShowActionModal(true);
            }
          }}
        >
          <Text style={styles.actionsButtonText}>
            View Suggested Actions ({item.metadata.suggestedActions.length})
          </Text>
        </TouchableOpacity>
      )}

      <Text style={styles.timestamp}>
        {item.timestamp.toLocaleTimeString()}
      </Text>
    </View>
  );

  return (
    <>
      <Stack.Screen
        options={{
          title: 'AI Assistant',
          headerRight: () => (
            <TouchableOpacity onPress={() => setMessages([])}>
              <Ionicons name="refresh" size={24} color={colors.primary} />
            </TouchableOpacity>
          ),
        }}
      />

      <SafeAreaView style={styles.container}>
        <View style={styles.messagesContainer}>
          <FlatList
            ref={flatListRef}
            data={messages}
            renderItem={renderMessage}
            keyExtractor={item => item.id}
            style={styles.messagesList}
            contentContainerStyle={{ paddingBottom: 20 }}
            onContentSizeChange={() => {
              // Delay scroll to ensure content is rendered
              setTimeout(() => {
                flatListRef.current?.scrollToEnd({ animated: true });
              }, 100);
            }}
            onLayout={() => flatListRef.current?.scrollToEnd({ animated: false })}
            showsVerticalScrollIndicator={false}
            removeClippedSubviews={false} // Ensure all content is rendered
          />

          {isLoading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={colors.primary} />
              <Text style={styles.loadingText}>AI is thinking...</Text>
            </View>
          )}
        </View>

        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
          style={styles.inputSection}
        >

          {selectedImages.length > 0 && (
            <View style={styles.selectedImagesContainer}>
              <FlatList
                horizontal
                data={selectedImages}
                renderItem={({ item, index }) => (
                  <View style={styles.selectedImageContainer}>
                    <Image source={{ uri: item }} style={styles.selectedImage} />
                    <TouchableOpacity
                      style={styles.removeImageButton}
                      onPress={() => removeImage(index)}
                    >
                      <Ionicons name="close" size={16} color={colors.white} />
                    </TouchableOpacity>
                  </View>
                )}
                keyExtractor={(item, index) => index.toString()}
              />
            </View>
          )}

          <View style={styles.inputContainer}>
            <TouchableOpacity style={styles.imageButton} onPress={handleCamera}>
              <Ionicons name="camera" size={24} color={colors.primary} />
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.imageButton} onPress={handleImagePicker}>
              <Ionicons name="image" size={24} color={colors.primary} />
            </TouchableOpacity>

            <TextInput
              style={styles.textInput}
              value={inputText}
              onChangeText={setInputText}
              placeholder="Ask me anything about your farm..."
              multiline
              maxLength={1000}
            />

            <TouchableOpacity
              style={[styles.sendButton, (!inputText.trim() && selectedImages.length === 0) && styles.sendButtonDisabled]}
              onPress={handleSendMessage}
              disabled={!inputText.trim() && selectedImages.length === 0}
            >
              <Ionicons name="send" size={20} color={colors.white} />
            </TouchableOpacity>
          </View>
        </KeyboardAvoidingView>

        <ActionConfirmationModal
          visible={showActionModal}
          actions={pendingActions}
          onClose={() => setShowActionModal(false)}
          onConfirm={(confirmedActions) => {
            // Handle confirmed actions
            console.log('Confirmed actions:', confirmedActions);
            setShowActionModal(false);
          }}
        />

        {/* Image Modal */}
        <Modal
          visible={showImageModal}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowImageModal(false)}
        >
          <View style={styles.imageModalContainer}>
            <TouchableOpacity
              style={styles.imageModalOverlay}
              onPress={() => setShowImageModal(false)}
            >
              <Image
                source={{ uri: selectedImageUrl }}
                style={styles.fullScreenImage}
                resizeMode="contain"
              />
              <TouchableOpacity
                style={styles.closeImageButton}
                onPress={() => setShowImageModal(false)}
              >
                <Ionicons name="close" size={24} color={colors.white} />
              </TouchableOpacity>
            </TouchableOpacity>
          </View>
        </Modal>
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  keyboardContainer: {
    flex: 1,
  },
  messagesContainer: {
    flex: 1,
  },
  inputSection: {
    backgroundColor: colors.white,
  },
  messagesList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  messageContainer: {
    marginVertical: 8,
    padding: 12,
    borderRadius: 12,
    maxWidth: '90%', // Increased width for entity data
    flexShrink: 1, // Allow shrinking if needed
  },
  userMessage: {
    alignSelf: 'flex-end',
    backgroundColor: colors.primary,
  },
  assistantMessage: {
    alignSelf: 'flex-start',
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  userMessageText: {
    color: colors.white,
  },
  assistantMessageText: {
    color: colors.gray[800],
  },
  messageImages: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 12,
  },
  messageImage: {
    width: 120,
    height: 120,
    borderRadius: 12,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  actionsButton: {
    marginTop: 8,
    padding: 8,
    backgroundColor: colors.primary,
    borderRadius: 8,
  },
  actionsButtonText: {
    color: colors.white,
    fontSize: 14,
    textAlign: 'center',
  },
  timestamp: {
    fontSize: 12,
    color: colors.gray[500],
    marginTop: 4,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  loadingText: {
    marginLeft: 8,
    color: colors.gray[600],
  },
  selectedImagesContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: colors.gray[100],
  },
  selectedImageContainer: {
    position: 'relative',
    marginRight: 8,
  },
  selectedImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: colors.danger,
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    padding: 16,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  imageButton: {
    padding: 8,
    marginRight: 8,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    maxHeight: 100,
    backgroundColor: colors.white,
  },
  sendButton: {
    backgroundColor: colors.primary,
    borderRadius: 20,
    padding: 12,
    marginLeft: 8,
  },
  sendButtonDisabled: {
    backgroundColor: colors.gray[400],
  },
  // Image Modal Styles
  imageModalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageModalOverlay: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullScreenImage: {
    width: '90%',
    height: '80%',
  },
  closeImageButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Entity Data Styles
  entityReferencesContainer: {
    marginTop: 8,
    maxHeight: 300, // Increased height to show more content
  },
  entityReference: {
    marginVertical: 2,
  },
  entityListingsContainer: {
    marginTop: 8,
    maxHeight: 250, // Increased height for better listings display
  },
  entityListing: {
    marginVertical: 4,
  },
});