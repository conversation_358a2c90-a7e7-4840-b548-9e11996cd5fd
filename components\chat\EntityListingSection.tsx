import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
} from 'react-native';
import { router } from 'expo-router';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';
import { EntityListing } from '@/services/ai-assistant';
import { EntityReferenceCard } from './EntityReferenceCard';
import { Ionicons } from '@expo/vector-icons';

interface EntityListingSectionProps {
  listing: EntityListing;
  style?: any;
}

export const EntityListingSection: React.FC<EntityListingSectionProps> = ({
  listing,
  style,
}) => {
  const { t, isRTL } = useTranslation();

  const handleShowAll = () => {
    // Navigate to the appropriate listing screen
    const routes = {
      animal: '/animal',
      plant: '/plant',
      garden: '/garden',
      field: '/field',
      equipment: '/equipment',
    };
    
    const route = routes[listing.type];
    if (route) {
      router.push(route);
    }
  };

  const renderEntityItem = ({ item }: { item: any }) => (
    <EntityReferenceCard
      entity={item}
      style={styles.entityCard}
    />
  );

  // Show all items instead of limiting to 3
  const displayItems = listing.entities;

  return (
    <View style={[styles.container, style]}>
      {/* Header */}
      <View style={[styles.header, isRTL && styles.headerRtl]}>
        <Text style={[styles.title, isRTL && styles.textRtl]}>
          {listing.title}
        </Text>
        <Text style={[styles.count, isRTL && styles.textRtl]}>
          ({listing.total_count})
        </Text>
        {listing.show_all_link && listing.entities.length < listing.total_count && (
          <TouchableOpacity
            style={[styles.showAllButton, isRTL && styles.showAllButtonRtl]}
            onPress={handleShowAll}
          >
            <Text style={styles.showAllText}>
              {t('common.showAll')}
            </Text>
            <Ionicons
              name={isRTL ? "chevron-back" : "chevron-forward"}
              size={16}
              color={colors.primary}
            />
          </TouchableOpacity>
        )}
      </View>

      {/* Entity List */}
      {displayItems.length > 0 ? (
        <View style={styles.listWrapper}>
          <FlatList
            data={displayItems}
            renderItem={renderEntityItem}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.listContainer}
            ItemSeparatorComponent={() => <View style={{ width: 8 }} />}
            style={styles.flatList}
            removeClippedSubviews={true}
            initialNumToRender={3}
            maxToRenderPerBatch={3}
          />
        </View>
      ) : (
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, isRTL && styles.textRtl]}>
            {t(`entity.${listing.type}.noItems`)}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    padding: 12,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  headerRtl: {
    flexDirection: 'row-reverse',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    flex: 1,
  },
  count: {
    fontSize: 14,
    color: colors.gray[500],
    marginLeft: 8,
  },
  showAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    backgroundColor: colors.primary + '10',
    marginLeft: 12,
  },
  showAllButtonRtl: {
    flexDirection: 'row-reverse',
    marginLeft: 0,
    marginRight: 12,
  },
  showAllText: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '500',
    marginRight: 4,
  },
  listWrapper: {
    minHeight: 120, // Increased minimum height to show content properly
    maxHeight: 150, // Allow some flexibility
  },
  flatList: {
    flexGrow: 0, // Prevent FlatList from expanding
  },
  listContainer: {
    paddingHorizontal: 4,
    alignItems: 'flex-start', // Align to top instead of center
  },
  entityCard: {
    width: 260, // Increased width for better content display
    marginHorizontal: 0,
    minHeight: 110, // Increased minimum height for better content display
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    color: colors.gray[500],
    fontStyle: 'italic',
  },
  textRtl: {
    textAlign: 'right',
  },
});

export default EntityListingSection;
