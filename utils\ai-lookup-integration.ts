import { useLookupStore } from '@/store/lookup-store';
import { convertTextToLookupId, ENTITY_LOOKUP_MAPPINGS } from './lookup-validation';

/**
 * Converts AI-generated text data to proper lookup IDs for form population
 */
export const convertAIDataToLookupIds = (entityType: keyof typeof ENTITY_LOOKUP_MAPPINGS, aiData: any) => {
  const { getLookupsByCategory } = useLookupStore.getState();
  const lookupMappings = ENTITY_LOOKUP_MAPPINGS[entityType];
  const convertedData = { ...aiData };

  // Convert each field that has a lookup mapping
  Object.entries(lookupMappings).forEach(([fieldName, lookupCategory]) => {
    const textValue = aiData[fieldName];
    if (textValue && typeof textValue === 'string') {
      const lookupId = convertTextToLookupId(lookupCategory, textValue);
      if (lookupId) {
        convertedData[fieldName] = lookupId;
      }
      // Keep original text value as fallback for species/variety fields
      else if (['species', 'variety'].includes(fieldName)) {
        convertedData[fieldName] = textValue;
      }
    }
  });

  return convertedData;
};

/**
 * Gets valid size unit lookup ID from text
 */
export const getSizeUnitLookupId = (textValue: string): string => {
  const { getLookupsByCategory } = useLookupStore.getState();
  const sizeUnits = getLookupsByCategory('areaUnit');
  
  if (!textValue || !sizeUnits.length) {
    // Default to acres if no valid unit found
    const defaultUnit = sizeUnits.find((unit: any) => 
      unit.title?.toLowerCase().includes('acre')
    );
    return defaultUnit?.id || 'ezVe7GdWq6SAxwaREyIw'; // fallback ID
  }

  const normalizedText = textValue.toLowerCase().trim();
  
  // Direct match first
  let match = sizeUnits.find((unit: any) => 
    unit.title?.toLowerCase() === normalizedText ||
    unit.value?.toLowerCase() === normalizedText
  );

  if (match) return match.id;

  // Try common variations
  const unitMappings: { [key: string]: string[] } = {
    'acres': ['acre', 'ac', 'acre_us', 'acre_pk'],
    'hectares': ['hectare', 'ha'],
    'square_meters': ['sq_m', 'sqm', 'm²', 'square meters', 'square_meters'],
    'square_feet': ['sq_ft', 'sqft', 'ft²', 'square feet', 'square_feet'],
    'marla_pk': ['marla', 'marlas'],
    'kanal_pk': ['kanal', 'kanals'],
  };

  for (const [unitKey, variations] of Object.entries(unitMappings)) {
    if (variations.some(variation => normalizedText.includes(variation))) {
      const foundUnit = sizeUnits.find((unit: any) => 
        unit.title?.toLowerCase().includes(unitKey) ||
        unit.value?.toLowerCase().includes(unitKey)
      );
      if (foundUnit) return foundUnit.id;
    }
  }

  // Default fallback
  const defaultUnit = sizeUnits.find((unit: any) => 
    unit.title?.toLowerCase().includes('acre')
  );
  return defaultUnit?.id || 'ezVe7GdWq6SAxwaREyIw';
};

/**
 * Gets valid field type lookup ID from text
 */
export const getFieldTypeLookupId = (textValue: string): string => {
  const { getLookupsByCategory } = useLookupStore.getState();
  const fieldTypes = getLookupsByCategory('fieldType');
  
  if (!textValue || !fieldTypes.length) {
    // Default to cropland
    const defaultType = fieldTypes.find((type: any) => 
      type.title?.toLowerCase().includes('crop')
    );
    return defaultType?.id || 'nSEVmRWD9OqffU6uwqPp'; // fallback ID
  }

  const normalizedText = textValue.toLowerCase().trim();
  
  // Direct match first
  let match = fieldTypes.find((type: any) => 
    type.title?.toLowerCase() === normalizedText ||
    type.value?.toLowerCase() === normalizedText
  );

  if (match) return match.id;

  // Try common variations
  const typeMappings: { [key: string]: string[] } = {
    'cropland': ['crop', 'crops', 'farming', 'agriculture'],
    'orchard': ['fruit', 'trees', 'fruit trees'],
    'livestock': ['animal', 'animals', 'grazing', 'pasture'],
    'garden': ['vegetable', 'vegetables', 'gardening'],
  };

  for (const [typeKey, variations] of Object.entries(typeMappings)) {
    if (variations.some(variation => normalizedText.includes(variation))) {
      const foundType = fieldTypes.find((type: any) => 
        type.title?.toLowerCase().includes(typeKey) ||
        type.value?.toLowerCase().includes(typeKey)
      );
      if (foundType) return foundType.id;
    }
  }

  // Default fallback
  const defaultType = fieldTypes.find((type: any) => 
    type.title?.toLowerCase().includes('crop')
  );
  return defaultType?.id || 'nSEVmRWD9OqffU6uwqPp';
};

/**
 * Gets valid status lookup ID from text
 */
export const getStatusLookupId = (textValue: string): string => {
  const { getLookupsByCategory } = useLookupStore.getState();
  const statuses = getLookupsByCategory('status');
  
  if (!textValue || !statuses.length) {
    // Default to active
    const defaultStatus = statuses.find((status: any) => 
      status.title?.toLowerCase().includes('active')
    );
    return defaultStatus?.id || 'b5LJdACC4zX5qvt328Sb'; // fallback ID
  }

  const normalizedText = textValue.toLowerCase().trim();
  
  // Direct match first
  let match = statuses.find((status: any) => 
    status.title?.toLowerCase() === normalizedText ||
    status.value?.toLowerCase() === normalizedText
  );

  if (match) return match.id;

  // Try common variations
  const statusMappings: { [key: string]: string[] } = {
    'active': ['working', 'operational', 'functioning'],
    'inactive': ['not working', 'broken', 'out of order'],
    'maintenance': ['repair', 'fixing', 'servicing'],
  };

  for (const [statusKey, variations] of Object.entries(statusMappings)) {
    if (variations.some(variation => normalizedText.includes(variation))) {
      const foundStatus = statuses.find((status: any) => 
        status.title?.toLowerCase().includes(statusKey) ||
        status.value?.toLowerCase().includes(statusKey)
      );
      if (foundStatus) return foundStatus.id;
    }
  }

  // Default fallback
  const defaultStatus = statuses.find((status: any) => 
    status.title?.toLowerCase().includes('active')
  );
  return defaultStatus?.id || 'b5LJdACC4zX5qvt328Sb';
};

/**
 * Gets equipment category lookup ID from text
 */
export const getEquipmentCategoryLookupId = (textValue: string): string => {
  const { getLookupsByCategory } = useLookupStore.getState();
  const categories = getLookupsByCategory('inventoryCategory');
  
  if (!textValue || !categories.length) {
    // Default to equipment
    const defaultCategory = categories.find((cat: any) => 
      cat.title?.toLowerCase().includes('equipment')
    );
    return defaultCategory?.id || '';
  }

  const normalizedText = textValue.toLowerCase().trim();
  
  // Direct match first
  let match = categories.find((cat: any) => 
    cat.title?.toLowerCase() === normalizedText ||
    cat.value?.toLowerCase() === normalizedText
  );

  if (match) return match.id;

  // Try common variations
  const categoryMappings: { [key: string]: string[] } = {
    'electronics': ['electronic', 'electrical', 'digital', 'tech'],
    'tools': ['tool', 'hand tools', 'manual tools', 'implements'],
    'equipment': ['equipments', 'machinery', 'machines', 'heavy equipment'],
  };

  for (const [catKey, variations] of Object.entries(categoryMappings)) {
    if (variations.some(variation => normalizedText.includes(variation))) {
      const foundCategory = categories.find((cat: any) => 
        cat.title?.toLowerCase().includes(catKey) ||
        cat.value?.toLowerCase().includes(catKey)
      );
      if (foundCategory) return foundCategory.id;
    }
  }

  // Default fallback
  const defaultCategory = categories.find((cat: any) => 
    cat.title?.toLowerCase().includes('equipment')
  );
  return defaultCategory?.id || '';
};

/**
 * Validates and converts AI field data to use proper lookup IDs
 */
export const validateAIFieldData = (aiData: any) => {
  const validatedData = { ...aiData };

  // Convert field type
  if (aiData.type) {
    validatedData.type = getFieldTypeLookupId(aiData.type);
  }

  // Convert size unit
  if (aiData.sizeUnit) {
    validatedData.sizeUnit = getSizeUnitLookupId(aiData.sizeUnit);
  }

  // Convert status
  if (aiData.status) {
    validatedData.status = getStatusLookupId(aiData.status);
  }

  return validatedData;
};
