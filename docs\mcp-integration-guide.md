# MCP Server Integration Guide

This guide explains how to integrate and use the MCP (Model Context Protocol) server with your farm management app.

## Overview

The MCP integration provides enhanced AI capabilities with a structured response format:
- **Summary**: Text summary of what the user wants
- **EntityData**: Detailed information about entities analyzed from the API
- **SuggestedActionArray**: Actions suggested by the AI for save/get operations

## Architecture

```
User Message (text/image/both) 
    ↓
MCP Server API Call
    ↓
Response: { summary, entityData, suggestedActionArray }
    ↓
Display Summary + Details + Action Buttons
    ↓
Execute Actions (Firestore operations)
```

## Setup

### 1. Configure MCP Server

Update your environment variables:

```env
EXPO_PUBLIC_MCP_SERVER_URL=http://your-mcp-server.com
EXPO_PUBLIC_MCP_API_KEY=your-api-key
```

### 2. MCP Server API Integration

Your MCP server is already implemented at: `https://api-3tfznjgouq-uc.a.run.app/chat/analyze`

**Request Format:**
```json
{
  "text": "want to add plant",
  "userId": "123456",
  "imageBase64": "/9j/4AAQSkZJRgABAQEBLAEsAAD/4QCjRXhpZgAASUkqAAgAAAAEAA4BAgBFAAA",
  "imageName": "photo.jpg",
  "imageMimeType": "image/jpeg"
}
```

**Response Format:**
```json
{
  "suggestions": {
    "summary": "User wants to add a plant, specifically apple trees.",
    "entityData": {
      "name": "Apple Tree",
      "species": "Malus domestica",
      "variety": "Unknown",
      "status": "Active",
      "healthStatus": "Healthy",
      "plantingDate": "Unknown"
    },
    "suggestedActions": [
      {
        "type": "add_plant",
        "label": "Add Apple Tree",
        "entityType": "plant"
      }
    ]
  }
}
```

## Usage

### 1. Basic Chat Integration

```tsx
import { mcpServerService } from '@/services/mcp-server';

// Send message to MCP server
const result = await mcpServerService.analyzeMessage({
  message: "I want to plant tomatoes in my garden",
  images: [imageUri], // optional
  context: {
    farmId: currentFarm.id,
    userId: user.uid,
    language: 'en',
    currentEntities: [...gardens, ...plants]
  }
});

// Display results
console.log(result.summary); // "User wants to add tomato plants..."
console.log(result.entityData); // { plantType: "tomato", ... }
console.log(result.suggestedActionArray); // [{ type: "save", ... }]
```

### 2. Action Execution

```tsx
import { MCPFirestoreOperations } from '@/services/mcp-firestore-operations';

// Execute a save action
const action = {
  id: "save_plant_001",
  type: "save",
  entity: "plant",
  data: {
    name: "Roma Tomato",
    species: "Solanum lycopersicum",
    // ... other plant data
  }
};

const result = await MCPFirestoreOperations.executeAction(action, {
  farmId: currentFarm.id,
  userId: user.uid
});
```

## Action Types

### Save Actions
Create new entities in Firestore:

```json
{
  "type": "save",
  "entity": "plant",
  "data": {
    "name": "Tomato Plant",
    "species": "Solanum lycopersicum"
  }
}
```

### Get Actions
Retrieve entities with optional queries:

```json
{
  "type": "get",
  "entity": "plant",
  "query": {
    "where": ["species", "==", "Solanum lycopersicum"],
    "orderBy": ["plantedDate", "desc"],
    "limit": 10
  }
}
```

### Update Actions
Modify existing entities:

```json
{
  "type": "update",
  "entity": "plant",
  "data": {
    "id": "plant_123",
    "status": "flowering",
    "health": "good"
  }
}
```

### Delete Actions
Remove entities:

```json
{
  "type": "delete",
  "entity": "plant",
  "data": {
    "id": "plant_123"
  }
}
```

## Supported Entities

- `animal` → `farms/{farmId}/animals`
- `plant` → `farms/{farmId}/plants`
- `garden` → `farms/{farmId}/gardens`
- `field` → `farms/{farmId}/fields`
- `equipment` → `farms/{farmId}/equipment`
- `crop` → `farms/{farmId}/crops`
- `task` → `farms/{farmId}/tasks`

## Error Handling

The system includes comprehensive error handling:

1. **Validation**: Actions are validated before execution
2. **Fallback**: If local Firestore execution fails, it falls back to MCP server
3. **User Feedback**: Clear error messages are shown to users

## Testing

Test your MCP server connection:

```tsx
const isConnected = await mcpServerService.testConnection();
console.log('MCP Server connected:', isConnected);
```

## Migration from Legacy AI Assistant

The new MCP integration works alongside the existing AI assistant. You can gradually migrate by:

1. Using the new `/mcp-chat` screen for MCP features
2. Keeping the existing `/ai-assistant` for legacy functionality
3. Eventually migrating all functionality to MCP format

## Components

- **MCPChatScreen**: Main chat interface for MCP
- **MCPActionModal**: Modal for executing suggested actions
- **MCPFirestoreOperations**: Service for Firestore operations
- **MCPServerService**: Service for MCP server communication

## How to Use

### Accessing MCP Chat
1. Go to the **Profile** screen in your app
2. Look for "MCP AI Assistant" in the Help & Support section
3. Tap to open the enhanced AI chat

### Using the Chat
1. **Text Messages**: Type your farm-related questions or requests
2. **Image Upload**: Tap camera or gallery icon to add a single image
3. **Combined**: Send text with an image for better analysis
4. **Review Response**: See AI summary and entity details
5. **Execute Actions**: Tap action buttons to save data to your farm

### Example Interactions
- "I want to add a tomato plant" → AI suggests creating a plant record
- Send plant photo → AI identifies species and suggests adding to database
- "Show me my cattle" → AI suggests querying animal records

## Best Practices

1. **Always validate** action data before execution
2. **Provide clear feedback** to users during action execution
3. **Handle errors gracefully** with fallback mechanisms
4. **Test thoroughly** with various message types and images
5. **Monitor performance** of MCP server calls
6. **Use single images** for best API compatibility
