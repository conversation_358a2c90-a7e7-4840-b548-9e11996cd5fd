/**
 * Modern ListView component for displaying MCP entity data
 * Supports different entity types with appropriate UI layouts
 */

import React from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Image,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '@/constants/colors';

interface MCPEntityListViewProps {
  entityType: string;
  data: any[];
  onItemPress?: (item: any) => void;
  showActions?: boolean;
  selectedItems?: string[];
  onItemSelect?: (item: any, isSelected: boolean) => void;
  selectionMode?: boolean;
  maxSelections?: number;
}

export default function MCPEntityListView({
  entityType,
  data,
  onItemPress,
  showActions = false,
  selectedItems = [],
  onItemSelect,
  selectionMode = false,
  maxSelections,
}: MCPEntityListViewProps) {
  const getEntityIcon = (type: string) => {
    const iconMap = {
      plant: 'leaf',
      animal: 'paw',
      garden: 'flower',
      field: 'grid',
      equipment: 'construct',
      task: 'checkmark-done',
      user: 'person',
      users: 'people',
    };
    return iconMap[type] || 'help';
  };

  const getEntityColor = (type: string) => {
    const colorMap = {
      plant: colors.success,
      animal: colors.warning,
      garden: colors.primary,
      field: colors.info,
      equipment: colors.secondary,
      task: colors.primary,
      user: colors.info,
      users: colors.info,
    };
    return colorMap[type] || colors.gray[600];
  };

  const isItemSelected = (item: any) => {
    return selectedItems.includes(item.id || item.identificationNumber || item.name);
  };

  const handleItemPress = (item: any) => {
    if (selectionMode && onItemSelect) {
      const isSelected = isItemSelected(item);

      // Check max selections limit
      if (!isSelected && maxSelections && selectedItems.length >= maxSelections) {
        return; // Don't allow more selections
      }

      onItemSelect(item, !isSelected);
    } else if (onItemPress) {
      onItemPress(item);
    }
  };

  const getStatusColor = (status: string) => {
    const statusColors = {
      active: colors.success,
      healthy: colors.success,
      good: colors.success,
      growing: colors.success,
      inactive: colors.gray[500],
      sick: colors.danger,
      poor: colors.danger,
      maintenance: colors.warning,
      pending: colors.warning,
    };
    return statusColors[status?.toLowerCase()] || colors.gray[600];
  };

  const renderPlantItem = ({ item }: { item: any }) => {
    const isSelected = isItemSelected(item);

    return (
      <TouchableOpacity
        style={[
          styles.itemContainer,
          isSelected && styles.selectedItemContainer,
          selectionMode && styles.selectableItemContainer
        ]}
        onPress={() => handleItemPress(item)}
      >
        {/* Selection Indicator */}
        {selectionMode && (
          <View style={styles.selectionIndicator}>
            <Ionicons
              name={isSelected ? "checkmark-circle" : "ellipse-outline"}
              size={24}
              color={isSelected ? colors.primary : colors.gray[400]}
            />
          </View>
        )}

        <View style={styles.itemHeader}>
        <View style={[styles.iconContainer, { backgroundColor: getEntityColor('plant') }]}>
          <Ionicons name="leaf" size={20} color={colors.white} />
        </View>
        <View style={styles.itemInfo}>
          <Text style={[styles.itemTitle, isSelected && styles.selectedItemTitle]}>
            {item.name || 'Unnamed Plant'}
          </Text>
          <Text style={styles.itemSubtitle}>
            {item.species} {item.variety ? `• ${item.variety}` : ''}
          </Text>
        </View>
        <View style={styles.statusContainer}>
          <View style={[styles.statusDot, { backgroundColor: getStatusColor(item.status) }]} />
          <Text style={styles.statusText}>{item.status || 'Unknown'}</Text>
        </View>
      </View>
      
      <View style={styles.itemDetails}>
        <View style={styles.detailRow}>
          <Ionicons name="calendar" size={14} color={colors.gray[600]} />
          <Text style={styles.detailText}>
            Planted: {item.plantedDate ? new Date(item.plantedDate).toLocaleDateString() : 'N/A'}
          </Text>
        </View>
        {item.health && (
          <View style={styles.detailRow}>
            <Ionicons name="fitness" size={14} color={getStatusColor(item.health)} />
            <Text style={styles.detailText}>Health: {item.health}</Text>
          </View>
        )}
        {item.gardenId && (
          <View style={styles.detailRow}>
            <Ionicons name="location" size={14} color={colors.gray[600]} />
            <Text style={styles.detailText}>Garden ID: {item.gardenId}</Text>
          </View>
        )}
      </View>
      
      {item.notes && (
        <Text style={styles.notesText} numberOfLines={2}>
          {item.notes}
        </Text>
      )}
    </TouchableOpacity>
    );
  };

  const renderAnimalItem = ({ item }: { item: any }) => {
    const isSelected = isItemSelected(item);

    return (
      <TouchableOpacity
        style={[
          styles.itemContainer,
          isSelected && styles.selectedItemContainer,
          selectionMode && styles.selectableItemContainer
        ]}
        onPress={() => handleItemPress(item)}
      >
        {/* Selection Indicator */}
        {selectionMode && (
          <View style={styles.selectionIndicator}>
            <Ionicons
              name={isSelected ? "checkmark-circle" : "ellipse-outline"}
              size={24}
              color={isSelected ? colors.primary : colors.gray[400]}
            />
          </View>
        )}

        <View style={styles.itemHeader}>
        <View style={[styles.iconContainer, { backgroundColor: getEntityColor('animal') }]}>
          <Ionicons name="paw" size={20} color={colors.white} />
        </View>
        <View style={styles.itemInfo}>
          <Text style={[styles.itemTitle, isSelected && styles.selectedItemTitle]}>
            {item.name || `${item.species} #${item.identificationNumber}`}
          </Text>
          <Text style={styles.itemSubtitle}>
            {item.species} {item.breed ? `• ${item.breed}` : ''}
          </Text>
        </View>
        <View style={styles.statusContainer}>
          <View style={[styles.statusDot, { backgroundColor: getStatusColor(item.status) }]} />
          <Text style={styles.statusText}>{item.status || 'Unknown'}</Text>
        </View>
      </View>
      
      <View style={styles.itemDetails}>
        {item.gender && (
          <View style={styles.detailRow}>
            <Ionicons name="male-female" size={14} color={colors.gray[600]} />
            <Text style={styles.detailText}>Gender: {item.gender}</Text>
          </View>
        )}
        {item.weight && (
          <View style={styles.detailRow}>
            <Ionicons name="scale" size={14} color={colors.gray[600]} />
            <Text style={styles.detailText}>Weight: {item.weight} {item.weightUnit || 'kg'}</Text>
          </View>
        )}
        {item.purpose && (
          <View style={styles.detailRow}>
            <Ionicons name="flag" size={14} color={colors.gray[600]} />
            <Text style={styles.detailText}>Purpose: {item.purpose}</Text>
          </View>
        )}
      </View>
      
      {item.notes && (
        <Text style={styles.notesText} numberOfLines={2}>
          {item.notes}
        </Text>
      )}
    </TouchableOpacity>
    );
  };

  const renderGardenItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.itemContainer}
      onPress={() => onItemPress?.(item)}
    >
      <View style={styles.itemHeader}>
        <View style={[styles.iconContainer, { backgroundColor: getEntityColor('garden') }]}>
          <Ionicons name="flower" size={20} color={colors.white} />
        </View>
        <View style={styles.itemInfo}>
          <Text style={styles.itemTitle}>{item.name || 'Unnamed Garden'}</Text>
          <Text style={styles.itemSubtitle}>{item.type}</Text>
        </View>
        <View style={styles.statusContainer}>
          <View style={[styles.statusDot, { backgroundColor: getStatusColor(item.status) }]} />
          <Text style={styles.statusText}>{item.status || 'Unknown'}</Text>
        </View>
      </View>
      
      <View style={styles.itemDetails}>
        {item.size && (
          <View style={styles.detailRow}>
            <Ionicons name="resize" size={14} color={colors.gray[600]} />
            <Text style={styles.detailText}>Size: {item.size} {item.sizeUnit || 'sq ft'}</Text>
          </View>
        )}
        {item.soilType && (
          <View style={styles.detailRow}>
            <Ionicons name="earth" size={14} color={colors.gray[600]} />
            <Text style={styles.detailText}>Soil: {item.soilType}</Text>
          </View>
        )}
        {item.irrigationSystem && (
          <View style={styles.detailRow}>
            <Ionicons name="water" size={14} color={colors.gray[600]} />
            <Text style={styles.detailText}>Irrigation: {item.irrigationSystem}</Text>
          </View>
        )}
      </View>
      
      {item.notes && (
        <Text style={styles.notesText} numberOfLines={2}>
          {item.notes}
        </Text>
      )}
    </TouchableOpacity>
  );

  const renderFieldItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.itemContainer}
      onPress={() => onItemPress?.(item)}
    >
      <View style={styles.itemHeader}>
        <View style={[styles.iconContainer, { backgroundColor: getEntityColor('field') }]}>
          <Ionicons name="grid" size={20} color={colors.white} />
        </View>
        <View style={styles.itemInfo}>
          <Text style={styles.itemTitle}>{item.name || 'Unnamed Field'}</Text>
          <Text style={styles.itemSubtitle}>{item.type} {item.cropType ? `• ${item.cropType}` : ''}</Text>
        </View>
        <View style={styles.statusContainer}>
          <View style={[styles.statusDot, { backgroundColor: getStatusColor(item.status) }]} />
          <Text style={styles.statusText}>{item.status || 'Unknown'}</Text>
        </View>
      </View>
      
      <View style={styles.itemDetails}>
        {item.size && (
          <View style={styles.detailRow}>
            <Ionicons name="resize" size={14} color={colors.gray[600]} />
            <Text style={styles.detailText}>Size: {item.size} {item.sizeUnit || 'acres'}</Text>
          </View>
        )}
        {item.plantedDate && (
          <View style={styles.detailRow}>
            <Ionicons name="calendar" size={14} color={colors.gray[600]} />
            <Text style={styles.detailText}>
              Planted: {new Date(item.plantedDate).toLocaleDateString()}
            </Text>
          </View>
        )}
        {item.harvestDate && (
          <View style={styles.detailRow}>
            <Ionicons name="calendar-outline" size={14} color={colors.gray[600]} />
            <Text style={styles.detailText}>
              Harvest: {new Date(item.harvestDate).toLocaleDateString()}
            </Text>
          </View>
        )}
      </View>
      
      {item.notes && (
        <Text style={styles.notesText} numberOfLines={2}>
          {item.notes}
        </Text>
      )}
    </TouchableOpacity>
  );

  const renderEquipmentItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.itemContainer}
      onPress={() => onItemPress?.(item)}
    >
      <View style={styles.itemHeader}>
        <View style={[styles.iconContainer, { backgroundColor: getEntityColor('equipment') }]}>
          <Ionicons name="construct" size={20} color={colors.white} />
        </View>
        <View style={styles.itemInfo}>
          <Text style={styles.itemTitle}>{item.name || 'Unnamed Equipment'}</Text>
          <Text style={styles.itemSubtitle}>
            {item.category} {item.type ? `• ${item.type}` : ''}
          </Text>
        </View>
        <View style={styles.statusContainer}>
          <View style={[styles.statusDot, { backgroundColor: getStatusColor(item.status) }]} />
          <Text style={styles.statusText}>{item.status || 'Unknown'}</Text>
        </View>
      </View>
      
      <View style={styles.itemDetails}>
        {item.manufacturer && (
          <View style={styles.detailRow}>
            <Ionicons name="business" size={14} color={colors.gray[600]} />
            <Text style={styles.detailText}>
              {item.manufacturer} {item.model ? `${item.model}` : ''}
            </Text>
          </View>
        )}
        {item.purchaseDate && (
          <View style={styles.detailRow}>
            <Ionicons name="calendar" size={14} color={colors.gray[600]} />
            <Text style={styles.detailText}>
              Purchased: {new Date(item.purchaseDate).toLocaleDateString()}
            </Text>
          </View>
        )}
        {item.lastMaintenanceDate && (
          <View style={styles.detailRow}>
            <Ionicons name="build" size={14} color={colors.gray[600]} />
            <Text style={styles.detailText}>
              Last Maintenance: {new Date(item.lastMaintenanceDate).toLocaleDateString()}
            </Text>
          </View>
        )}
      </View>
      
      {item.notes && (
        <Text style={styles.notesText} numberOfLines={2}>
          {item.notes}
        </Text>
      )}
    </TouchableOpacity>
  );

  const renderUserItem = ({ item }: { item: any }) => {
    const isSelected = isItemSelected(item);

    const getRoleColor = (role: string) => {
      const roleColors: { [key: string]: string } = {
        owner: colors.primary,
        manager: colors.warning,
        worker: colors.success,
        viewer: colors.info,
        specialist: colors.secondary,
        veterinarian: colors.info,
      };
      return roleColors[role?.toLowerCase()] || colors.gray[600];
    };

    return (
      <TouchableOpacity
        style={[
          styles.itemContainer,
          isSelected && styles.selectedItemContainer,
          selectionMode && styles.selectableItemContainer
        ]}
        onPress={() => handleItemPress(item)}
      >
        {/* Selection Indicator */}
        {selectionMode && (
          <View style={styles.selectionIndicator}>
            <Ionicons
              name={isSelected ? "checkmark-circle" : "ellipse-outline"}
              size={24}
              color={isSelected ? colors.primary : colors.gray[400]}
            />
          </View>
        )}

        <View style={styles.itemHeader}>
          {/* User Avatar */}
          <View style={styles.userAvatarContainer}>
            {item.avatar ? (
              <Image
                source={{ uri: item.avatar }}
                style={styles.userAvatarImage}
              />
            ) : (
              <View style={[styles.userAvatarPlaceholder, { backgroundColor: getRoleColor(item.role) }]}>
                <Text style={styles.userAvatarText}>
                  {(item.name || item.displayName || 'U').charAt(0).toUpperCase()}
                </Text>
              </View>
            )}
          </View>

          <View style={styles.itemInfo}>
            <Text style={[styles.itemTitle, isSelected && styles.selectedItemTitle]}>
              {item.name || item.displayName || 'Unknown User'}
            </Text>
            <Text style={styles.itemSubtitle}>
              {item.role?.charAt(0).toUpperCase() + item.role?.slice(1) || 'User'}
            </Text>
          </View>

          <View style={styles.statusContainer}>
            <View style={[styles.roleBadge, { backgroundColor: getRoleColor(item.role) }]}>
              <Text style={styles.roleText}>
                {item.role?.toUpperCase() || 'USER'}
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.itemDetails}>
          <View style={styles.detailRow}>
            <Ionicons name="mail" size={14} color={colors.gray[600]} />
            <Text style={styles.detailText}>
              {item.email || 'No email'}
            </Text>
          </View>
          {item.phoneNumber && (
            <View style={styles.detailRow}>
              <Ionicons name="call" size={14} color={colors.gray[600]} />
              <Text style={styles.detailText}>{item.phoneNumber}</Text>
            </View>
          )}
          {item.joinedDate && (
            <View style={styles.detailRow}>
              <Ionicons name="calendar" size={14} color={colors.gray[600]} />
              <Text style={styles.detailText}>
                Joined: {new Date(item.joinedDate).toLocaleDateString()}
              </Text>
            </View>
          )}
        </View>

        {item.bio && (
          <Text style={styles.notesText} numberOfLines={2}>
            {item.bio}
          </Text>
        )}
      </TouchableOpacity>
    );
  };

  const renderTaskItem = ({ item }: { item: any }) => {
    const isSelected = isItemSelected(item);

    const getPriorityColor = (priority: string) => {
      const priorityColors: { [key: string]: string } = {
        low: colors.success,
        medium: colors.warning,
        high: colors.danger,
        urgent: colors.danger,
        critical: colors.danger,
      };
      return priorityColors[priority?.toLowerCase()] || colors.gray[600];
    };

    const getStatusColor = (status: string) => {
      console.log(status)
      const statusColors: { [key: string]: string } = {
        pending: colors.warning,
        'in progress': colors.info,
        completed: colors.success,
        cancelled: colors.gray[500],
        overdue: colors.danger,
      };
      return statusColors[status?.toLowerCase()] || colors.gray[600];
    };

    return (
      <TouchableOpacity
        style={[
          styles.itemContainer,
          isSelected && styles.selectedItemContainer,
          selectionMode && styles.selectableItemContainer
        ]}
        onPress={() => handleItemPress(item)}
      >
        {/* Selection Indicator */}
        {selectionMode && (
          <View style={styles.selectionIndicator}>
            <Ionicons
              name={isSelected ? "checkmark-circle" : "ellipse-outline"}
              size={24}
              color={isSelected ? colors.primary : colors.gray[400]}
            />
          </View>
        )}

        <View style={styles.itemHeader}>
          <View style={[styles.iconContainer, { backgroundColor: getEntityColor('task') }]}>
            <Ionicons name="checkmark-done" size={20} color={colors.white} />
          </View>
          <View style={styles.itemInfo}>
            <Text style={[styles.itemTitle, isSelected && styles.selectedItemTitle]}>
              {item.title || item.name || 'Untitled Task'}
            </Text>
            <Text style={styles.itemSubtitle}>
              {item.category || 'General Task'}
            </Text>
          </View>
          <View style={styles.statusContainer}>
            <View style={[styles.statusDot, { backgroundColor: getPriorityColor(item.priority) }]} />
            <Text style={styles.statusText}>{item.priority || 'Medium'}</Text>
          </View>
        </View>

        <View style={styles.itemDetails}>
          <View style={styles.detailRow}>
            <Ionicons name="flag" size={14} color={getStatusColor(item.status)} />
            <Text style={styles.detailText}>
              Status: {item.status || 'Pending'}
            </Text>
          </View>
          {item.dueDate && (
            <View style={styles.detailRow}>
              <Ionicons name="calendar" size={14} color={colors.gray[600]} />
              <Text style={styles.detailText}>
                Due: {new Date(item.dueDate).toLocaleDateString()}
              </Text>
            </View>
          )}
          {item.assignedTo && (
            <View style={styles.detailRow}>
              <Ionicons name="person" size={14} color={colors.gray[600]} />
              <Text style={styles.detailText}>
                Assigned: {item.assignedTo.name || item.assignedTo}
              </Text>
            </View>
          )}
        </View>

        {item.description && (
          <Text style={styles.notesText} numberOfLines={2}>
            {item.description}
          </Text>
        )}
      </TouchableOpacity>
    );
  };

  const renderItem = ({ item }: { item: any }) => {
    // console.log(item)
    switch (entityType.toLowerCase()) {
      case 'plant':
        return renderPlantItem({ item });
      case 'animal':
        return renderAnimalItem({ item });
      case 'garden':
        return renderGardenItem({ item });
      case 'field':
        return renderFieldItem({ item });
      case 'equipment':
        return renderEquipmentItem({ item });
      case 'user':
      case 'users':
        return renderUserItem({ item });
      case 'task':
      case 'tasks':
        return renderTaskItem({ item });
      default:
        return (
          <View style={styles.itemContainer}>
            <Text style={styles.itemTitle}>
              {item.name || item.title || JSON.stringify(item)}
            </Text>
          </View>
        );
    }
  };

  if (!data || data.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Ionicons 
          name={getEntityIcon(entityType)} 
          size={48} 
          color={colors.gray[400]} 
        />
        <Text style={styles.emptyText}>No {entityType}s found</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Ionicons 
          name={getEntityIcon(entityType)} 
          size={20} 
          color={getEntityColor(entityType)} 
        />
        <Text style={styles.headerText}>
          {entityType.charAt(0).toUpperCase() + entityType.slice(1)}s ({data.length})
        </Text>
      </View>
      
      <FlatList
        data={data}
        renderItem={renderItem}
        horizontal
        keyExtractor={(item, index) => item.id || `${entityType}-${index}`}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: colors.gray[50],
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  headerText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginLeft: 8,
  },
  listContent: {
    padding: 16,
  },
  itemContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    margin: 2,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  selectedItemContainer: {
    borderColor: colors.primary,
    borderWidth: 1,
    // backgroundColor: colors.primary+20 ,//+ '10',
  },
  selectableItemContainer: {
    borderWidth: 1,
    // borderColor: colors.gray[300],
  },
  selectionIndicator: {
    position: 'absolute',
    top: 8,
    right: 8,
    zIndex: 1,
  },
  selectedItemTitle: {
    color: colors.primary,
    fontWeight: '600',
  },
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  itemInfo: {
    flex: 1,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 2,
  },
  itemSubtitle: {
    fontSize: 14,
    color: colors.gray[600],
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    color: colors.gray[600],
    textTransform: 'capitalize',
  },
  itemDetails: {
    marginBottom: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  detailText: {
    fontSize: 13,
    color: colors.gray[600],
    marginLeft: 6,
  },
  notesText: {
    fontSize: 13,
    color: colors.gray[500],
    fontStyle: 'italic',
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: colors.gray[100],
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    color: colors.gray[500],
    marginTop: 12,
  },
  userAvatarContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  userAvatarImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  userAvatarPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  userAvatarText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.white,
  },
  roleBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  roleText: {
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
    color: colors.white,
  },
});
