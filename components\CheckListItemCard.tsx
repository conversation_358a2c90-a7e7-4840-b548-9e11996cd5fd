// import { Ionicons } from '@expo/vector-icons';
// import React from 'react';
// import { View, Text, TouchableOpacity, StyleSheet, Switch } from 'react-native';
// import Input from './Input';

// export interface ChecklistItem {
//     id: string;
//     title: string;
//     category: string;
// }

// interface RenderChecklistItemProps {
//     item: ChecklistItem;
//     onPress: (item: ChecklistItem) => void;
// }

// export const CheckListItemCard = ({ item, onPress }: RenderChecklistItemProps) => {
//     return (
//         <TouchableOpacity style={styles.itemContainer} onPress={() => onPress(item)}>
//             <View style={styles.textContainer}>
//                 <Text style={styles.title}>{item.title}</Text>
//                 <Text style={styles.category}>{item.category}</Text>
//             </View>
//             <View style={styles.separator} />
//             {(item.items || []).map((field, index) => (
//                 <View key={index} style={styles.fieldItem}>
//                     <Text style={styles.fieldLabel}>{field.label}</Text>
//                     {renderFieldByType(field?.type)}
//                     {/* <Text style={styles.fieldValue}>{String(field.value)}</Text> */}
//                 </View>
//             ))}
//         </TouchableOpacity>
//     );
// };
// const renderFieldByType = (type: any) => {
//     switch (type) {
//         case 'text':
//         // case 'number':
//             return <Input style={styles.input} placeholder="..." editable={false} />;
//         case 'textarea':
//             return <Input style={[styles.input, styles.textarea]} multiline numberOfLines={4} editable={false} />;
//         case 'boolean':
//             return <Switch disabled value={false} />;
//         case 'select':
//             return (
//                 <View style={styles.selectBox}>
//                     <Text style={styles.selectText}>Select option</Text>
//                     <Ionicons name="chevron-down" size={16} color="#888" />
//                 </View>
//             );
//         case 'date':
//             return (
//                 <TouchableOpacity style={styles.dateField} disabled>
//                     <Ionicons name="calendar" size={16} color="#888" />
//                     <Text style={styles.dateText}>Select date</Text>
//                 </TouchableOpacity>
//             );
//         default:
//             return null;
//     }
// };
// const styles = StyleSheet.create({
//     itemContainer: {
//         paddingVertical: 12,
//         paddingHorizontal: 16,
//         backgroundColor: '#fff',
//     },
//     textContainer: {
//         marginBottom: 4,
//     },
//     title: {
//         fontSize: 16,
//         fontWeight: '600',
//         color: '#222',
//     },
//     category: {
//         fontSize: 14,
//         color: '#666',
//     },
//     separator: {
//         height: 1,
//         backgroundColor: '#eee',
//         marginTop: 8,
//     },
//     card: {
//         backgroundColor: '#fff',
//         padding: 16,
//         borderRadius: 12,
//         elevation: 2,
//         marginBottom: 12,
//     },
//     title: {
//         fontSize: 18,
//         fontWeight: '600',
//         marginBottom: 12,
//         color: '#222',
//     },
//     fieldContainer: {
//         marginBottom: 12,
//     },
//     label: {
//         fontSize: 14,
//         fontWeight: '500',
//         color: '#555',
//         marginBottom: 4,
//     },
//     input: {
//         backgroundColor: '#f1f1f1',
//         borderRadius: 6,
//         padding: 10,
//         color: '#333',
//     },
//     textarea: {
//         height: 80,
//         textAlignVertical: 'top',
//     },
//     selectBox: {
//         backgroundColor: '#f1f1f1',
//         borderRadius: 6,
//         padding: 10,
//         flexDirection: 'row',
//         justifyContent: 'space-between',
//         alignItems: 'center',
//     },
//     selectText: {
//         color: '#888',
//     },
//     dateField: {
//         backgroundColor: '#f1f1f1',
//         borderRadius: 6,
//         padding: 10,
//         flexDirection: 'row',
//         alignItems: 'center',
//         gap: 6,
//     },
//     dateText: {
//         color: '#888',
//     },
// });
// // });


import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Switch } from 'react-native';
import Input from './Input';

export interface ChecklistField {
    label: string;
    type: 'text' | 'textarea' | 'boolean' | 'select' | 'date';
}

export interface ChecklistItem {
    id: string;
    title: string;
    category: string;
    items?: ChecklistField[];
}

interface RenderChecklistItemProps {
    item: ChecklistItem;
    onPress: (item: ChecklistItem) => void;
}

export const CheckListItemCard = ({ item, onPress }: RenderChecklistItemProps) => {
    return (
        <TouchableOpacity style={styles.listContainer} onPress={() => onPress(item)}>
            {/* {checklists.map((checklist, idx) => ( */}
            <View key={item?.id} style={styles.checklistBlock}>
                <Text style={styles.checklistTitle}>📋 {item?.title}</Text>
                <Text style={styles.checklistCategory}>🌱 {item?.category}</Text>

                {(item?.items||[])?.map((field, i) => (
                    <View key={i} style={styles.fieldRow}>
                        <Text style={styles.fieldLabel}>• {field.label}</Text>
                        <View style={styles.fieldPreview}>
                            {renderFieldByType(field.type)}
                        </View>
                    </View>
                ))}
            </View>
            {/* ))} */}
        </TouchableOpacity>
        // <TouchableOpacity style={styles.card} onPress={() => onPress(item)}>
        //     {/* Header Section */}
        //     <View style={styles.header}>
        //         <Text style={styles.title}>{item.title}</Text>
        //         <Text style={styles.category}>{item.category}</Text>
        //     </View>

        //     {/* Divider */}
        //     <View style={styles.separator} />

        //     {/* Fields Preview */}
        //     <View style={styles.fieldsGroup}>
        //         {(item.items || []).map((field, index) => (
        //             <View key={index} style={styles.fieldRow}>
        //                 <Text style={styles.fieldLabel}>• {field.label}</Text>
        //                 <View style={styles.fieldPreview}>
        //                     {renderFieldByType(field.type)}
        //                 </View>
        //             </View>
        //         ))}
        //     </View>
        // </TouchableOpacity>
    );
};

export const renderFieldByType = (type: string) => {
    switch (type) {
        case 'text':
        case 'number':
            return <Input style={styles.input} placeholder="..." editable={false} />;
        case 'textarea':
            return <Input style={[styles.input, styles.textarea]} multiline numberOfLines={4} editable={false} />;
        case 'boolean':
            return <Switch disabled value={false} />;
        case 'select':
            return (
                <View style={styles.selectBox}>
                    <Text style={styles.selectText}>Select option</Text>
                    <Ionicons name="chevron-down" size={16} color="#888" />
                </View>
            );
        case 'date':
            return (
                <TouchableOpacity style={styles.dateField} disabled>
                    <Ionicons name="calendar" size={16} color="#888" />
                    <Text style={styles.dateText}>Select date</Text>
                </TouchableOpacity>
            );
        default:
            return null;
    }
};

const styles = StyleSheet.create({
    card: {
        backgroundColor: '#fff',
        padding: 16,
        borderRadius: 12,
        marginBottom: 12,
        elevation: 1,
    },
    header: {
        marginBottom: 8,
    },
    title: {
        fontSize: 16,
        fontWeight: '600',
        color: '#222',
    },
    category: {
        fontSize: 14,
        color: '#666',
        marginTop: 2,
    },
    separator: {
        height: 1,
        backgroundColor: '#eee',
        marginVertical: 12,
    },
    fieldsGroup: {
        gap: 12,
    },
    fieldItem: {
        marginBottom: 6,
    },
    fieldLabel: {
        fontSize: 14,
        fontWeight: '500',
        color: '#555',
        marginBottom: 4,
    },
    input: {
        backgroundColor: '#f1f1f1',
        borderRadius: 6,
        padding: 10,
        color: '#333',
    },
    textarea: {
        height: 80,
        textAlignVertical: 'top',
    },
    selectBox: {
        backgroundColor: '#f1f1f1',
        borderRadius: 6,
        padding: 10,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    selectText: {
        color: '#888',
    },
    dateField: {
        backgroundColor: '#f1f1f1',
        borderRadius: 6,
        padding: 10,
        flexDirection: 'row',
        alignItems: 'center',
        gap: 6,
    },
    dateText: {
        color: '#888',
    },
    listContainer: {
        padding: 16,
        backgroundColor: '#fff',
    },

    checklistBlock: {
        marginBottom: 24,
        paddingBottom: 12,
        borderBottomWidth: 1,
        borderColor: '#eee',
    },

    checklistTitle: {
        fontSize: 16,
        fontWeight: '700',
        color: '#222',
    },

    checklistCategory: {
        fontSize: 13,
        color: '#666',
        marginBottom: 12,
    },

    fieldRow: {
        marginBottom: 10,
    },

    fieldLabel: {
        fontSize: 14,
        fontWeight: '500',
        color: '#444',
        marginBottom: 4,
    },

    fieldPreview: {
        backgroundColor: '#f6f6f6',
        borderRadius: 6,
        padding: 10,
    },
});
