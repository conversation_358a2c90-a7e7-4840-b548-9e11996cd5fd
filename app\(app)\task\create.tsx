import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Modal,
  FlatList,
  ActivityIndicator,
  Switch,
  Dimensions,
  TouchableWithoutFeedback,
} from 'react-native';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import Button from '@/components/Button';
import Input from '@/components/Input';
import DatePicker from '@/components/DatePicker';
import EnhancedDropdown from '@/components/EnhancedDropdown'; // Not used in this snippet, but kept
import {
  ChevronDown,
  Check,
  Leaf, // Used for generic entity icon now
  User,
  Home,
  Repeat,
  ClipboardList,
  Image as ImageIcon,
} from 'lucide-react-native';
import { Field, Farm, ChecklistItem, Garden, Plant, Animal, Equipment, User as UserType } from '@/types';
import DropdownPicker from '@/components/DropdownPicker';
import { useTranslation } from '@/i18n/useTranslation';
import { useLookupStore } from '@/store/lookup-store';
import { CheckListItemCard } from '@/components/CheckListItemCard';
import ImagePicker from '@/components/ImagePicker';
import { Camera, FileText, ChevronUp } from 'lucide-react-native';
import Toast from 'react-native-toast-message';
import { AntDesign } from '@expo/vector-icons';
import { uploadImageAsync } from '@/utils/firebase-storage';

// Mock checklist templates (kept for existing logic, but `fetchChecklists` is also present)
const checklistTemplates = {
  animal: [
    { id: '1', title: 'Check for signs of illness', description: 'Look for unusual behavior, discharge, or wounds', completed: false, required: true },
    { id: '2', title: 'Verify food and water', description: 'Ensure adequate food and clean water are available', completed: false, required: true },
    { id: '3', title: 'Clean living area', description: 'Remove waste and replace bedding if necessary', completed: false, required: true },
  ],
  plant: [
    { id: '1', title: 'Check soil moisture', description: 'Ensure soil is appropriately moist but not waterlogged', completed: false, required: true },
    { id: '2', title: 'Inspect for pests', description: 'Look for insects, eggs, or damage on leaves and stems', completed: false, required: true },
    { id: '3', title: 'Check for disease', description: 'Look for discoloration, spots, or unusual growth', completed: false, required: true },
  ],
  field: [
    { id: '1', title: 'Check irrigation system', description: 'Ensure all sprinklers and drip lines are functioning properly', completed: false, required: true },
    { id: '2', title: 'Inspect for weeds', description: 'Identify areas requiring weed control', completed: false, required: true },
    { id: '3', title: 'Check crop health', description: 'Look for signs of disease or nutrient deficiency', completed: false, required: true },
  ],
  garden: [
    { id: '1', title: 'Check irrigation system', description: 'Ensure all sprinklers and drip lines are functioning properly', completed: false, required: true },
    { id: '2', title: 'Inspect for weeds', description: 'Identify areas requiring weed control', completed: false, required: true },
    { id: '3', title: 'Check plant health', description: 'Look for signs of disease or nutrient deficiency', completed: false, required: true },
  ],
  equipment: [
    { id: '1', title: 'Check for damage', description: 'Inspect for any visible damage or wear', completed: false, required: true },
    { id: '2', title: 'Verify functionality', description: 'Test that the equipment operates as expected', completed: false, required: true },
    { id: '3', title: 'Clean equipment', description: 'Remove dirt, debris, or residue', completed: false, required: true },
  ],
};


const SCREEN_HEIGHT = Dimensions.get('window').height;

export default function CreateTaskScreen() {
  const { id, entity } = useLocalSearchParams(); // `id` if passing entity ID directly, `entity` for type string
  const { user } = useAuthStore();
  const {
    addTask,
    fields,
    gardens,
    plants,
    animals,
    equipment,
    fetchFields,
    fetchGardens,
    fetchPlants,
    fetchAnimals,
    fetchEquipment,
    currentFarm,
    farms,
    getField, // Not directly used in new dropdown logic but kept for reference
    getEquipment,
    getGarden,
    getPlant,
    getAnimal,
    fetchChecklists,
    fetchFarms, // Not directly used in this snippet's scope
    setCurrentFarm
  } = useFarmStore();
  const { t, isRTL } = useTranslation()
  const { getLookupsByCategory, getLookupsByCategoryParssedData } = useLookupStore();
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState<'low' | 'medium' | 'high' | 'urgent'>('medium');
  const [dueDate, setDueDate] = useState<Date | null>(new Date(Date.now() + 86400000)); // Tomorrow
  const [assignedTo, setAssignedTo] = useState('');
  const [assigneeName, setAssigneeName] = useState(''); // Not directly used in updated assignee dropdown, but can be kept
  const [selectedEntityId, setSelectedEntityId] = useState(''); // Renamed from entityId for clarity
  const [selectedEntityType, setSelectedEntityType] = useState<string | null>(null); // Renamed from entityType for clarity
  const [category, setCategory] = useState<'general' | 'irrigation' | 'fertilization' | 'harvest' | 'maintenance' | 'feeding' | 'health_check' | 'cleaning'>('general');
  const [isLoading, setIsLoading] = useState(false);
  const [isRecurring, setIsRecurring] = useState(false);
  const [frequency, setFrequency] = useState<'once' | 'daily' | 'weekly' | 'monthly' | 'custom'>('once');
  const [repeatUntil, setRepeatUntil] = useState<Date | null>(null);
  const [checklist, setChecklist] = useState<ChecklistItem[]>([]);
  const [evidenceRequired, setEvidenceRequired] = useState(true);
  const [taskImage, setTaskImage] = useState('');
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);

  // Field validation errors
  const [fieldErrors, setFieldErrors] = useState({
    title: '',
    assignedTo: '',
    dueDate: ''
  });

  // Farm users state
  const [farmUsers, setFarmUsers] = useState<UserType[]>([]);
  const [loadingFarmUsers, setLoadingFarmUsers] = useState(false);

  // Modals (kept for other purposes, entity selection modal removed)
  const [showFarmModal, setShowFarmModal] = useState(false);
  const [isShowCheckListDynamically, setIsShowCheckListDyanmically] = useState(false);

  // Memoized entity type options to avoid re-renders
  const entityTypeOptions = useMemo(() => {
    // Assuming 'entityType' lookup gives values like 'field', 'garden', etc.
    return getLookupsByCategoryParssedData('entityType', 'entity_types.');
  }, [getLookupsByCategoryParssedData]);

  // Fetch farm-related data on currentFarm change
  useEffect(() => {
    if (currentFarm?.id) {
      fetchFields(currentFarm.id);
      fetchGardens(currentFarm.id);
      fetchPlants(currentFarm.id);
      fetchAnimals(currentFarm.id);
      fetchEquipment(currentFarm.id);
      loadFarmUsers(currentFarm.id);
    }
  }, [currentFarm]);

  // // Initialize entity type and ID if passed via route params (e.g., from an entity's screen)
  // useEffect(() => {
  //   if (entity && id) { // `entity` as type string (e.g., 'field'), `id` as entity ID
  //     setSelectedEntityType(entity as string);
  //     setSelectedEntityId(id as string);
  //     // Also load checklists relevant to this initial entity type
  //     loadCheckListData(entity as string);
  //   }
  // }, [entity, id]);

  // Initialize entity type and ID if passed via route params
  // IMPORTANT: This useEffect should run after `entityTypeOptions` is ready.
  // We make `entityTypeOptions` a dependency to ensure it has data.
  useEffect(() => {
    if (entity && id && entityTypeOptions.length > 0) { // Ensure entityTypeOptions is loaded
      // Find the matching entity type value from the lookup array
      const matchedEntityType = entityTypeOptions.find(
        (item: { label: string; value: string }) => item.label.toLowerCase() === (entity as string).toLowerCase()
      );

      if (matchedEntityType) {
        setSelectedEntityType(matchedEntityType.value); // Set the internal value (e.g., 'field')
        setSelectedEntityId(id as string);
        loadCheckListData(matchedEntityType.value);
      } else {
        console.warn(`Entity type "${entity}" not found in lookup.`);
      }
    }
  }, [entity, id, entityTypeOptions]); // Add entityTypeOptions as a dependency

  // // Update checklist based on selected entity type
  // useEffect(() => {
  //   // Only use mock templates if fetchChecklists isn't providing data, or for initial display
  //   // Otherwise, `loadCheckListData` (called on entityType change) will handle `setChecklist`
  //   if (selectedEntityType && checklistTemplates[selectedEntityType] && checkListArray.length === 0) {
  //       // If no dynamic checklist data, fallback to mock
  //       // This part might need adjustment based on how `fetchChecklists` works and when it loads.
  //       // For now, it sets mock only if `checkListArray` is empty.
  //       // Consider removing this if `fetchChecklists` is always reliable.
  //       setChecklist(checklistTemplates[selectedEntityType]);
  //   } else if (!selectedEntityType) {
  //       setChecklist([]);
  //   }
  // }, [selectedEntityType, checkListArray]);


  const loadFarmUsers = async (farmId: string) => {
    setLoadingFarmUsers(true);
    try {
      const { getUsersByFarm } = useAuthStore.getState();
      if (!getUsersByFarm) {
        console.error("getUsersByFarm function is not available");
        setLoadingFarmUsers(false);
        return;
      }
      const users = await getUsersByFarm(farmId);

      let filteredUsers = users;
      if (user?.role === 'admin') {
        filteredUsers = users.filter(u => u.role === 'admin' || u.role === 'caretaker' && u.status !== 'inactive');
      } else if (user?.role === 'owner') {
        filteredUsers = users.filter(u => (u.role === 'admin' || u.role === 'caretaker') && u.status !== 'inactive');
      }
      setFarmUsers(filteredUsers);
    } catch (error) {
      console.error('Error loading farm users:', error);
      Alert.alert('Error', 'Failed to load farm users. Please try again.');
    } finally {
      setLoadingFarmUsers(false);
    }
  };

  // const handleCreateTask = async () => {
  //   if (!title) {
  //     Alert.alert('Error', 'Please enter a task title');
  //     return;
  //   }
  //   if (!dueDate) {
  //     Alert.alert('Error', 'Please select a due date');
  //     return;
  //   }
  //   if (!currentFarm) {
  //     Alert.alert('Error', 'Please select a farm first');
  //     return;
  //   }
  //   if (isRecurring && frequency !== 'once' && !repeatUntil) {
  //     Alert.alert('Error', 'Please select an end date for recurring tasks');
  //     return;
  //   }

  //   try {
  //     setIsLoading(true);

  //     const taskData: any = {
  //       title,
  //       description,
  //       status: 'pending',
  //       frequency,
  //       isRecurring,
  //       priority,
  //       dueDate: dueDate.toISOString(),
  //       category,
  //       farmId: currentFarm.id,
  //       assignedBy: user?.id,
  //       createdAt: new Date().toISOString(),
  //       updatedAt: new Date().toISOString(),
  //       AssignedCheckListId: selectedCheckListItem,
  //     };

  //     // Add selected entity type and ID
  //     if (selectedEntityType && selectedEntityId) {
  //       taskData[`${selectedEntityType}Id`] = selectedEntityId;
  //       // Optionally store the entityType string as well if needed in DB
  //       taskData.relatedEntityType = selectedEntityType;
  //     }

  //     if (isRecurring && frequency !== 'once') {
  //       taskData.frequency = frequency;
  //       if (repeatUntil) {
  //         taskData.repeatUntil = repeatUntil.toISOString();
  //       }
  //     } else {
  //       taskData.frequency = 'once';
  //     }

  //     // If you're using `selectedCheckListItem` for checklist reference, remove `checklist` array direct save
  //     // If `checklist` state is the actual items, use that. Assuming `selectedCheckListItem` stores ID of a template.
  //     if (checklist.length > 0) { // If `checklist` holds the actual items
  //       // For now, sticking to the existing `checklist` state logic for items
  //       // If `AssignedCheckListId` is meant to *reference* a template, `checklist` should be generated from that template later.
  //       taskData.checklistDetails = {
  //         title: "Task Checklist", // Or derive from selectedCheckListItem
  //         instructions: "Follow these steps to complete the task.",
  //         items: checklist // This should be populated based on selectedCheckListItem
  //       };
  //     }


  //     taskData.evidence = {
  //       required: evidenceRequired,
  //       images: [], // Images are added on task completion, not creation
  //     };

  //     if (assignedTo) taskData.assignedTo = assignedTo;

  //     await addTask(taskData);

  //     Alert.alert('Success', 'Task created successfully', [
  //       { text: 'OK', onPress: () => router.back() }
  //     ]);
  //   } catch (error: any) {
  //     console.error('Error creating task:', error);
  //     Alert.alert('Error', error.message || 'Failed to create task. Please try again.');
  //   } finally {
  //     setIsLoading(false);
  //   }
  // };
  const validateFields = () => {
    const errors = {
      title: '',
      assignedTo: '',
      dueDate: ''
    };

    let hasErrors = false;

    if (!title.trim()) {
      errors.title = t('task.titleRequired');
      hasErrors = true;
    }

    if (!assignedTo) {
      errors.assignedTo = t('task.assigneeRequired');
      hasErrors = true;
    }

    if (!dueDate) {
      errors.dueDate = t('task.dueDateRequired');
      hasErrors = true;
    }

    setFieldErrors(errors);
    return !hasErrors;
  };

  const handleCreateTask = async () => {
    if (!validateFields()) {
      Toast.show({
        type: 'overlay',
        text1: t('common.error'),
        text2: t('common.fillAllFields'),
      });
      return;
    }

    try {
      setIsLoading(true);

      const taskData: any = {
        title,
        description,
        status: 'pending',
        frequency,
        isRecurring,
        priority,
        dueDate: dueDate.toISOString(),
        category,
        farmId: currentFarm.id,
        assignedBy: user?.id,
        assignedTo,
        evidenceRequired,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        AssignedCheckListId: selectedCheckListItem,
      };

      // Add task image if provided
      if (taskImage) {
        const imageUrl = await uploadImageAsync(taskImage, 'animals');

        taskData.imageUrl = imageUrl;
      }

      // Add selected entity type and ID
      if (selectedEntityType && selectedEntityId) {
        // taskData[`${selectedEntityType}Id`] = selectedEntityId;
        taskData.entityId=selectedEntityId
        taskData.relatedEntityType = selectedEntityType;
      }

      // Add recurring task data
      if (isRecurring && frequency !== 'once' && repeatUntil) {
        taskData.repeatUntil = repeatUntil.toISOString();
      }

      await addTask(taskData);

      Toast.show({
        type: 'success',
        text1: t('common.success'),
        text2: t('task.createdSuccessfully'),
      });

      router.back();
    } catch (error: any) {
      console.error('Create task error:', error);
      Toast.show({
        type: 'error',
        text1: t('common.error'),
        text2: error.message || t('task.createError'),
      });
    } finally {
      setIsLoading(false);
    }
  };
  // Helper function to get entities based on type string (e.g., 'field', 'garden')
  const getEntitiesByType = (type: string | null) => {
    switch (type) {
      case 'lxRKXdJgjL9qURotrMo8'://'field':
        return fields;
      case 'waUnV31wc9JXr1debFN4'://'garden':
        return gardens;
      case 'EIkF12Ewx9idmbF69wCJ'://'plant':
        return plants;
      case '3h9A6NeNdJNX2tm6vSY0':// 'animal':
        return animals;
      case 'vldIZre7gYu64Y42UyvT'://'equipment':
        return equipment;
      default:
        return [];
    }
  };

  // Helper function to get entity icon based on type string (e.g., 'field', 'garden')
  const getEntityIcon = (type: string | null, color: string) => {
    switch (type) {
      case 'field':
      case 'garden':
      case 'plant':
        return <Leaf size={20} color={color} style={styles.dropdownIcon} />; // Using Leaf for all plant/field types
      case 'animal':
        return <User size={20} color={color} style={styles.dropdownIcon} />; // Placeholder, consider a paw/animal icon
      case 'equipment':
        return <ClipboardList size={20} color={color} style={styles.dropdownIcon} />; // Placeholder, consider a wrench/gear icon
      default:
        return <Leaf size={20} color={color} style={styles.dropdownIcon} />;
    }
  };

  const toggleChecklistItem = (id: string) => {
    setChecklist(checklist.map(item =>
      item.id === id ? { ...item, completed: !item.completed } : item
    ));
  };

  const priorityLookupArray = useMemo(() => getLookupsByCategoryParssedData('taskPriority', 'task.priority.'), [getLookupsByCategoryParssedData]);
  const categoryLookupArray = useMemo(() => getLookupsByCategoryParssedData('taskCategory', 'task.category.'), [getLookupsByCategoryParssedData]);
  const frequencyArray = useMemo(() => getLookupsByCategoryParssedData('taskFrequency', 'task.frequencies.'), [getLookupsByCategoryParssedData]);

  const [checkListArray, setCheckListArray] = useState([]);
  const [selectedCheckListItem, setSelectCheckListItem] = useState(null); // This holds the ID of the selected checklist template

  const loadCheckListData = async (entityType: string) => {
    // This will fetch checklists based on the selected entity type
    // You might need to adjust `fetchChecklists` to accept entityType
    let entityType2 = ''
    switch (entityType) {
      case 'lxRKXdJgjL9qURotrMo8'://'field':
        entityType2 = 'field';
        break;
      case 'waUnV31wc9JXr1debFN4'://'garden':
        entityType2 = 'garden';
        break;
      case 'EIkF12Ewx9idmbF69wCJ'://'plant':
        entityType2 = 'plant';
        break;
      case '3h9A6NeNdJNX2tm6vSY0':// 'animal':
        entityType2 = 'animal';
        break;
      case 'vldIZre7gYu64Y42UyvT'://'equipment':
        entityType2 = 'equipment';
        break;
      default:
        entityType2 = '';
        break;
    }

    const res = await fetchChecklists(entityType2);
    setCheckListArray(res);
    // When a checklist type is selected from the modal, you'd set the actual items
    // For now, this just loads templates.
  };

  useEffect(() => {
    if (selectedEntityType) { // Load checklist data only when an entity type is selected
      loadCheckListData(selectedEntityType);
    }
  }, [selectedEntityType]); // Re-run when selectedEntityType changes


  return (
    <>
      <Stack.Screen
        options={{
          title: 'Create New Task',
          headerShown: true,
        }}
      />

      <SafeAreaView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          {/* Farm Selection */}
          {farms.length > 0 && (
            <View style={styles.farmSelectionContainer}>
              <Text style={[styles.farmSelectionLabel, isRTL && styles.rtlTextAlign]}>{t('farm.selectFarm')}</Text>
              <TouchableOpacity
                style={[styles.farmSelectionButton, isRTL && styles.rtlDirection]}
                onPress={() => setShowFarmModal(true)}
              >
                <Home size={20} color={colors.gray[500]} style={styles.farmIcon} />
                <Text style={[styles.farmSelectionText, isRTL && styles.rtlTextAlign]}>
                  {currentFarm?.name || t('farm.selectAFarm')}
                </Text>
                <ChevronDown size={20} color={colors.gray[500]} />
              </TouchableOpacity>
            </View>
          )}

          <View style={styles.formContainer}>
            <Input
              label={t('task.title')}
              placeholder={t('task.titlePlaceholder')}
              value={title}
              onChangeText={(text) => {
                setTitle(text);
                if (fieldErrors.title) {
                  setFieldErrors(prev => ({ ...prev, title: '' }));
                }
              }}
              containerStyle={styles.inputContainer}
              required={true}
              error={fieldErrors.title}
            />

            <Input
              label={t('task.description')}
              placeholder={t('task.descriptionPlaceholder')}
              value={description}
              onChangeText={setDescription}
              multiline
              numberOfLines={4}
              containerStyle={styles.inputContainer}
              inputStyle={styles.textArea}
            />

            <DropdownPicker
              label={t('tasks.priority')}
              placeholder={t('task.priorityPlaceholder', 'Select priority...')}
              options={priorityLookupArray}
              selectedValue={priority}
              onSelect={(val) => setPriority(val as any)}
              lookupCollection="lookups"
              lookupCategory="taskPriorities"
              isRTL={isRTL}
              zIndex={3000}
              zIndexInverse={1000}
            />

            <DatePicker
              label={t('task.dueDate')}
              value={dueDate}
              onChange={(date) => {
                setDueDate(date);
                if (fieldErrors.dueDate) {
                  setFieldErrors(prev => ({ ...prev, dueDate: '' }));
                }
              }}
              required={true}
              minimumDate={new Date()}
            />
            {fieldErrors.dueDate && (
              <Text style={styles.errorText}>{fieldErrors.dueDate}</Text>
            )}

            <View style={[styles.switchContainer, isRTL && styles.rtlDirection]}>
              <Text style={styles.switchLabel}>{t('task.recurring')}</Text>
              <Switch
                value={isRecurring}
                onValueChange={setIsRecurring}
                trackColor={{ false: colors.gray[300], true: colors.primaryLight }}
                thumbColor={isRecurring ? colors.primary : colors.gray[100]}
              />
            </View>

            {isRecurring && (
              <>
                <DropdownPicker
                  label={t('task.frequency')}
                  options={frequencyArray}
                  onSelect={(val) => setFrequency(val as any)}
                  selectedValue={frequency}
                  isMultiple={false}
                />

                {frequency !== 'once' && (
                  <DatePicker
                    label={t('task.repeatUntil')}
                    value={repeatUntil}
                    onChange={setRepeatUntil}
                    required={true}
                    minimumDate={dueDate || new Date()}
                  />
                )}
              </>
            )}

            <DropdownPicker
              label={t('task.assignTo')}
              options={farmUsers.map(user => ({ label: user.name || user.displayName || '', value: user.id }))}
              onSelect={(val) => {
                setAssignedTo(val as any);
                if (fieldErrors.assignedTo) {
                  setFieldErrors(prev => ({ ...prev, assignedTo: '' }));
                }
              }}
              selectedValue={assignedTo}
              isMultiple={false}
              placeholder={t('task.assignToPlaceholder')}
              required={true}
              error={fieldErrors.assignedTo}
            />

            {/* Related Entity Selection - First Dropdown: Entity Type */}
            <DropdownPicker
              label={t('task.relatedTo')}
              placeholder={t('task.selectEntityType', 'Select entity type...')}
              options={entityTypeOptions}
              selectedValue={selectedEntityType}
              onSelect={(val) => {
                setSelectedEntityType(val as string);
                setSelectedEntityId(null); // Reset entity ID when type changes
              }}
              isMultiple={false}
              zIndex={2000}
              zIndexInverse={2000}
            />

            {/* Related Entity Selection - Second Dropdown: Specific Entity */}
            {selectedEntityType && (
              <View style={{ marginTop: 16 }}>
                <DropdownPicker
                  label={t('task.selectEntity', { entity: t(`entity_types.${selectedEntityType}`) })}
                  placeholder={t('task.selectSpecificEntity', { entity: t(`entity_types.${selectedEntityType}`) })}
                  options={getEntitiesByType(selectedEntityType).map(item => ({ label: item.name, value: item.id }))}
                  selectedValue={selectedEntityId}
                  onSelect={(val) => setSelectedEntityId(val as string)}
                  isMultiple={false}
                  zIndex={1000}
                  zIndexInverse={3000}
                  disabled={getEntitiesByType(selectedEntityType).length === 0}
                />
                {getEntitiesByType(selectedEntityType).length === 0 && (
                  <Text style={styles.noEntitiesText}>
                    {t('task.noEntitiesAvailable', { entity: t(`entity_types.${selectedEntityType}`) })}
                    {" "}
                    <Text style={styles.createEntityLink} onPress={() => router.push(`/${selectedEntityType}/create`)}>
                      {t('task.createOne', { entity: t(`entity_types.${selectedEntityType}`) })}
                    </Text>
                  </Text>
                )}
              </View>
            )}

            <View style={styles.templateSelectorContainer}>
              <Text style={[styles.sectionTitle, isRTL && styles.rtlTextAlign]}>
                {t('task.checklistTemplate')}
              </Text>

              <TouchableOpacity
                style={[styles.templateSelectorButton, isRTL && styles.rtlDirection]}
                onPress={() => {
                  if (!selectedEntityType) {
                    Toast.show({
                      type: 'info',
                      text1: t('task.selectEntityFirst'),
                      text2: t('task.selectEntityFirstDesc'),
                    });
                    return;
                  }
                  loadCheckListData(selectedEntityType);
                  setShowTemplateSelector(true);
                }}
              >
                <View style={[styles.templateSelectorContent, isRTL && styles.rtlDirection]}>
                  <FileText size={20} color={colors.primary} />
                  <View style={styles.templateSelectorTextContainer}>
                    <Text style={[styles.templateSelectorLabel, isRTL && styles.rtlTextAlign]}>
                      {selectedTemplate ? selectedTemplate.title : t('task.selectTemplate')}
                    </Text>
                    {selectedTemplate && (
                      <Text style={[styles.templateSelectorSubtext, isRTL && styles.rtlTextAlign]}>
                        {selectedTemplate.items?.length || 0} {t('task.items')}
                      </Text>
                    )}
                  </View>
                </View>
                <ChevronDown size={20} color={colors.gray[500]} />
              </TouchableOpacity>
            </View>



            {/* Task Image Section */}
            <View style={styles.imageSection}>
              <Text style={[styles.sectionTitle, isRTL && styles.rtlTextAlign]}>
                {t('task.attachImage')} {t('common.optional')}
              </Text>
              <ImagePicker
                image={taskImage}
                onImageSelected={setTaskImage}
                placeholder={t('task.addTaskImage')}
                size={120}
                iconComponent={<Camera size={24} color={colors.gray[500]} />}
                style={styles.taskImagePicker}
              />
              {taskImage && (
                <View style={styles.imagePreviewContainer}>
                  <Text style={[styles.imagePreviewLabel, isRTL && styles.rtlTextAlign]}>
                    {t('task.attachedImage')}
                  </Text>
                </View>
              )}
            </View>

            <View style={styles.evidenceContainer}>
              <View style={[styles.evidenceHeader, isRTL && styles.rtlDirection]}>
                <Text style={[styles.sectionTitle, isRTL && styles.rtlTextAlign]}>{t('task.evidenceRequired')}</Text>
                <Switch
                  value={evidenceRequired}
                  onValueChange={setEvidenceRequired}
                  trackColor={{ false: colors.gray[300], true: colors.primaryLight }}
                  thumbColor={evidenceRequired ? colors.primary : colors.gray[100]}
                />
              </View>

              {evidenceRequired && (
                <View style={[styles.evidenceInfo, isRTL && styles.rtlDirection]}>
                  <ImageIcon size={20} color={colors.gray[600]} />
                  <Text style={[styles.evidenceText, isRTL && styles.rtlTextAlign]}>{t('task.evidenceNote')}</Text>
                </View>
              )}
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <Button
              title={t('common.cancel', 'Cancel')}
              variant="outline"
              onPress={() => router.back()}
              style={styles.cancelButton}
              disabled={isLoading}
            />
            <Button
              title={
                isLoading
                  ? t('task.creating', 'Creating...')
                  : t('task.create', 'Create Task')
              }
              onPress={handleCreateTask}
              style={styles.createButton}
              disabled={isLoading}
              leftIcon={
                isLoading ? (
                  <ActivityIndicator size="small" color={colors.white} />
                ) : undefined
              }
            />
          </View>
        </ScrollView>

        {/* Modal for checkList Listing (unchanged) */}
        {/* <Modal
          visible={isShowCheckListDynamically}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setIsShowCheckListDyanmically(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>
                  Select Checklist
                </Text>
                <TouchableOpacity onPress={() => setIsShowCheckListDyanmically(false)}>
                  <Text style={styles.modalCloseText}>Close</Text>
                </TouchableOpacity>
              </View>

              <FlatList
                data={checkListArray}
                renderItem={({ item }) => <CheckListItemCard item={item} onPress={(val: any) => {
                  setSelectCheckListItem(val?.id);
                  // When a checklist is selected from this modal, you might want to load its actual items
                  // For now, this just sets the ID. You'll need logic to load checklist items into `checklist` state
                  // based on `selectedCheckListItem` if they are to be displayed/used.
                  setIsShowCheckListDyanmically(false); // Close modal on selection
                }} />}
                keyExtractor={item => item.id}
                style={styles.modalList}
                ListEmptyComponent={
                  <View style={styles.emptyListContainer}>
                    <Text style={styles.emptyListText}>
                      No checklists available for {selectedEntityType ? t(`entity_types.${selectedEntityType}`) : 'this type'}.
                    </Text>
                  </View>
                }
              />
            </View>
          </View>
        </Modal> */}


        {/* Template Selection Modal */}
        <Modal
          visible={showTemplateSelector}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowTemplateSelector(false)}
        >
          <TouchableWithoutFeedback onPress={() => setShowTemplateSelector(false)}>
            <View style={styles.modalOverlay} />
          </TouchableWithoutFeedback>

          <View style={styles.bottomSheet}>
            <View style={[styles.modalHeader, isRTL && styles.rtlDirection]}>
              <Text style={styles.modalTitle}>{t('task.selectChecklistTemplate')}</Text>
              <TouchableOpacity onPress={() => setShowTemplateSelector(false)}>
                <Text style={styles.closeText}>{t('common.close')}</Text>
              </TouchableOpacity>
            </View>

            {checkListArray.length > 0 ? (
              <FlatList
                data={checkListArray}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[
                      styles.templateModalOption,
                      selectedCheckListItem === item.id && styles.templateModalOptionSelected,
                      isRTL && styles.rtlDirection
                    ]}
                    onPress={() => {
                      setSelectCheckListItem(item.id);
                      setSelectedTemplate(item);
                      setShowTemplateSelector(false);
                    }}
                  >
                    <View style={[styles.templateModalContent, isRTL && styles.rtlDirection]}>
                      <View style={[
                        styles.templateModalIcon,
                        selectedCheckListItem === item.id && styles.templateModalIconSelected
                      ]}>
                        <FileText size={18} color={
                          selectedCheckListItem === item.id ? colors.white : colors.primary
                        } />
                      </View>

                      <View style={styles.templateOptionTextContainer}>
                        <Text style={[
                          styles.templateModalTitle,
                          selectedCheckListItem === item.id && styles.templateModalTitleSelected,
                          isRTL && styles.rtlTextAlign
                        ]}>
                          {item.title}
                        </Text>
                        <Text style={[styles.templateModalSubtext, isRTL && styles.rtlTextAlign]}>
                          {item.items?.length || 0} {t('task.items')}
                        </Text>
                        {item.description && (
                          <Text style={[styles.templateModalDescription, isRTL && styles.rtlTextAlign]} numberOfLines={2}>
                            {item.description}
                          </Text>
                        )}
                      </View>
                    </View>

                    {selectedCheckListItem === item.id && (
                      <AntDesign name="checkcircleo" size={20} color={colors.primary} />
                    )}
                  </TouchableOpacity>
                )}
                contentContainerStyle={{ paddingBottom: 30 }}
                showsVerticalScrollIndicator={false}
              />
            ) : (
              <View style={styles.noTemplatesContainer}>
                <FileText size={48} color={colors.gray[300]} />
                <Text style={[styles.templateModalTitle, isRTL && styles.rtlTextAlign]}>
                  {t('task.noTemplatesTitle')}
                </Text>
                <Text style={[styles.noTemplatesText, isRTL && styles.rtlTextAlign]}>
                  {t('task.noTemplatesAvailable', {
                    entity: selectedEntityType ? t(`entity_types.${selectedEntityType}`) : ''
                  })}
                </Text>
              </View>
            )}
          </View>
        </Modal>
        {/* Farm Selection Modal (unchanged) */}
        <Modal
          visible={showFarmModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowFarmModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Select Farm</Text>
                <TouchableOpacity onPress={() => setShowFarmModal(false)}>
                  <Text style={styles.modalCloseText}>Close</Text>
                </TouchableOpacity>
              </View>
              {farms.length > 0 ? (
                <FlatList
                  data={farms}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      style={styles.modalItem}
                      onPress={() => {
                        setCurrentFarm(item.id);
                        setShowFarmModal(false);
                        // Reset entity and assignee when farm changes
                        setSelectedEntityId('');
                        setSelectedEntityType(null);
                        setAssignedTo('');
                        setAssigneeName('');
                      }}
                    >
                      <View style={styles.modalItemContent}>
                        <Home size={20} color={colors.gray[600]} style={styles.modalItemIcon} />
                        <Text style={styles.modalItemText}>{item.name}</Text>
                      </View>
                      {currentFarm?.id === item.id && (
                        <Check size={20} color={colors.primary} />
                      )}
                    </TouchableOpacity>
                  )}
                  keyExtractor={item => item.id}
                  style={styles.modalList}
                />
              ) : (
                <View style={styles.emptyListContainer}>
                  <Text style={styles.emptyListText}>No farms available. Please create a farm first.</Text>
                </View>
              )}
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  templateSelectorContainer: {
    marginTop: 16,
    marginBottom: 16,

  },
  templateModalContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  templateModalIcon: {
    width: 32,
    height: 32,
    borderRadius: 8,
    backgroundColor: colors.primaryLight,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  templateModalIconSelected: {
    backgroundColor: colors.primary,
  },
  rtlDirection: {
    flexDirection: 'row-reverse',
  },
  rtlTextAlign: {
    textAlign: 'right',
    marginRight: 8
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  formContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  inputContainer: {
    marginBottom: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
    paddingTop: 12,
  },
  label: { // This style is mostly superseded by DropdownPicker's internal label
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
  },
  dropdownContainer: { // This style is mostly superseded by DropdownPicker's internal container
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    marginBottom: 16,
    position: 'relative',
  },
  dropdownHeader: { // This style is mostly superseded by DropdownPicker's internal header
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  dropdownIcon: {
    marginRight: 8,
  },
  dropdownText: { // This style is mostly superseded by DropdownPicker's internal text
    fontSize: 14,
    color: colors.gray[800],
    flex: 1,
  },
  dropdownOptions: { // This style is mostly superseded by DropdownPicker's internal options container
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    backgroundColor: colors.white,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    elevation: 2,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    maxHeight: 200,
  },
  dropdownOption: { // This style is mostly superseded by DropdownPicker's internal option item
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  dropdownOptionText: { // This style is mostly superseded by DropdownPicker's internal option text
    fontSize: 14,
    color: colors.gray[800],
    marginLeft: 8,
  },
  fieldButton: { // This style is replaced by the second DropdownPicker
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    marginBottom: 16,
  },
  fieldIcon: { // Replaced by dropdownIcon for consistency
    marginRight: 8,
  },
  fieldText: { // Replaced by dropdownText for consistency
    fontSize: 14,
    color: colors.gray[800],
    flex: 1,
  },
  noEntitiesText: {
    fontSize: 14,
    color: colors.gray[600],
    marginTop: 8,
    textAlign: 'center',
  },
  createEntityLink: {
    color: colors.primary,
    fontWeight: '600',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  createButton: {
    flex: 1,
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  modalCloseText: {
    fontSize: 16,
    color: colors.primary,
  },
  modalList: {
    maxHeight: '80%',
  },
  modalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalItemIcon: {
    marginRight: 12,
  },
  modalItemText: {
    fontSize: 16,
    color: colors.gray[800],
  },
  modalItemSubtext: {
    fontSize: 14,
    color: colors.gray[500],
  },
  emptyListContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyListText: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
  },
  farmSelectionContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  farmSelectionLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
  },
  farmSelectionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  farmIcon: {
    marginRight: 8,
  },
  farmSelectionText: {
    fontSize: 14,
    color: colors.gray[800],
    flex: 1,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  switchLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
  },
  checklistContainer: { // Not directly used in the final render of checklist items
    marginTop: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  checklistItem: { // Not directly used in the final render of checklist items
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  checkbox: { // Not directly used in the final render of checklist items
    width: 24,
    height: 24,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: colors.gray[400],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    marginTop: 2,
  },
  checkboxChecked: { // Not directly used in the final render of checklist items
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  checklistItemContent: { // Not directly used in the final render of checklist items
    flex: 1,
  },
  checklistItemTitle: { // Not directly used in the final render of checklist items
    fontSize: 16,
    color: colors.gray[800],
    marginBottom: 4,
  },
  checklistItemDescription: { // Not directly used in the final render of checklist items
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 4,
  },
  requiredBadge: { // Not directly used in the final render of checklist items
    backgroundColor: colors.danger + '20',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  requiredText: { // Not directly used in the final render of checklist items
    fontSize: 12,
    color: colors.danger,
  },
  evidenceContainer: {
    marginTop: 16,
  },
  evidenceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  // evidenceInfo: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   backgroundColor: colors.gray[100],
  //   marginBottom: 20,
  // },
  evidenceInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
    padding: 12,
    borderRadius: 8,
  },
  evidenceIcon: {
    marginRight: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  templateSelectorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 16,
    backgroundColor: colors.white,
    minHeight: 56,
  },
  templateSelectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  templateSelectorTextContainer: {
    flex: 1,
    marginLeft: 12,
  },
  templateSelectorLabel: {
    fontSize: 16,
    fontWeight: '400',
    color: colors.text,
  },
  templateSelectorSubtext: {
    fontSize: 14,
    color: colors.gray[600],
    marginTop: 2,
  },

  // Modal styles matching DropdownPicker
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  bottomSheet: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: SCREEN_HEIGHT * 0.7,
    paddingTop: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  closeText: {
    fontSize: 16,
    color: colors.primary,
    fontWeight: '500',
  },

  // Template option styles
  templateModalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  templateModalOptionSelected: {
    backgroundColor: colors.primaryLight,
  },
  templateOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  templateOptionIcon: {
    width: 32,
    height: 32,
    borderRadius: 8,
    backgroundColor: colors.primaryLight,
    alignItems: 'center',
    justifyContent: 'center',
  },
  templateOptionIconSelected: {
    backgroundColor: colors.primary,
  },
  templateOptionTextContainer: {
    flex: 1,
    marginLeft: 12,
  },
  templateOptionTitle: {
    fontSize: 15,
    fontWeight: '500',
    color: colors.text,
  },
  templateOptionTitleSelected: {
    color: colors.primary,
  },
  templateOptionSubtext: {
    fontSize: 13,
    color: colors.gray[600],
    marginTop: 2,
  },
  noTemplatesContainer: {
    padding: 20,
    alignItems: 'center',
  },
  noTemplatesText: {
    fontSize: 14,
    color: colors.gray[600],
    textAlign: 'center',
  },
  imageSection: {
    marginBottom: 20,
  },
  taskImagePicker: {
    alignSelf: 'flex-start',
  },
  imagePreviewContainer: {
    marginTop: 8,
  },
  imagePreviewLabel: {
    fontSize: 14,
    color: colors.gray[600],
  },
  evidenceText: {
    fontSize: 14,
    color: colors.gray[700],
    marginLeft: 8,
  },
  templateModalTitle: {
    fontSize: 15,
    fontWeight: '500',
    color: colors.text,
  },
  templateModalTitleSelected: {
    color: colors.primary,
  },
  templateModalSubtext: {
    fontSize: 13,
    color: colors.gray[600],
    marginTop: 2,
  },
  templateModalDescription: {
    fontSize: 13,
    color: colors.gray[500],
    marginTop: 4,
  },
  errorText: {
    color: colors.danger,
    fontSize: 12,
    marginTop: 4,
    marginBottom: 8,
  },
});
