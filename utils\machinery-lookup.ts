import { useLookupStore } from '@/store/lookup-store';

/**
 * Machinery database with common farm equipment and their specifications
 */
export const MACHINERY_DATABASE = {
  tractors: [
    {
      name: '<PERSON> 5075E',
      manufacturer: '<PERSON>',
      type: 'Utility Tractor',
      category: 'Equipment',
      horsepower: '75 HP',
      fuelType: 'Diesel',
      transmission: 'Manual',
      estimatedPrice: 3500000,
      description: 'Versatile utility tractor for medium-scale farming operations'
    },
    {
      name: 'Massey Ferguson 240',
      manufacturer: 'Massey Ferguson',
      type: 'Compact Tractor',
      category: 'Equipment',
      horsepower: '50 HP',
      fuelType: 'Diesel',
      transmission: 'Manual',
      estimatedPrice: 2800000,
      description: 'Reliable compact tractor for small to medium farms'
    },
    {
      name: 'New Holland 3630',
      manufacturer: 'New Holland',
      type: 'Utility Tractor',
      category: 'Equipment',
      horsepower: '55 HP',
      fuelType: 'Diesel',
      transmission: 'Manual',
      estimatedPrice: 3200000,
      description: 'Efficient utility tractor with excellent fuel economy'
    }
  ],
  harvesters: [
    {
      name: '<PERSON> S660',
      manufacturer: '<PERSON>',
      type: 'Combine Harvester',
      category: 'Equipment',
      cuttingWidth: '25 ft',
      grainTankCapacity: '300 bu',
      estimatedPrice: 15000000,
      description: 'High-capacity combine harvester for large-scale operations'
    },
    {
      name: 'Case IH Axial-Flow 250',
      manufacturer: 'Case IH',
      type: 'Combine Harvester',
      category: 'Equipment',
      cuttingWidth: '20 ft',
      grainTankCapacity: '250 bu',
      estimatedPrice: 12000000,
      description: 'Efficient combine harvester with advanced threshing technology'
    }
  ],
  implements: [
    {
      name: 'Disc Harrow',
      manufacturer: 'Various',
      type: 'Tillage Equipment',
      category: 'Equipment',
      workingWidth: '8-12 ft',
      estimatedPrice: 800000,
      description: 'Primary tillage implement for soil preparation'
    },
    {
      name: 'Seed Drill',
      manufacturer: 'Various',
      type: 'Planting Equipment',
      category: 'Equipment',
      workingWidth: '10-15 ft',
      estimatedPrice: 1200000,
      description: 'Precision seeding equipment for crop planting'
    },
    {
      name: 'Rotary Tiller',
      manufacturer: 'Various',
      type: 'Tillage Equipment',
      category: 'Equipment',
      workingWidth: '6-8 ft',
      estimatedPrice: 600000,
      description: 'Secondary tillage implement for seedbed preparation'
    }
  ],
  tools: [
    {
      name: 'Hand Hoe',
      manufacturer: 'Various',
      type: 'Hand Tool',
      category: 'Tools',
      material: 'Steel',
      estimatedPrice: 1500,
      description: 'Essential hand tool for weeding and cultivation'
    },
    {
      name: 'Pruning Shears',
      manufacturer: 'Various',
      type: 'Hand Tool',
      category: 'Tools',
      material: 'Steel',
      estimatedPrice: 2500,
      description: 'Precision cutting tool for plant maintenance'
    },
    {
      name: 'Irrigation Sprinkler',
      manufacturer: 'Various',
      type: 'Irrigation Tool',
      category: 'Tools',
      coverage: '20-30 ft radius',
      estimatedPrice: 5000,
      description: 'Water distribution tool for crop irrigation'
    }
  ],
  electronics: [
    {
      name: 'GPS Guidance System',
      manufacturer: 'Trimble',
      type: 'Navigation System',
      category: 'Electronics',
      accuracy: '2-5 cm',
      estimatedPrice: 500000,
      description: 'Precision agriculture GPS system for accurate field operations'
    },
    {
      name: 'Soil Moisture Sensor',
      manufacturer: 'Various',
      type: 'Monitoring Device',
      category: 'Electronics',
      range: '0-100% moisture',
      estimatedPrice: 25000,
      description: 'Electronic sensor for monitoring soil moisture levels'
    },
    {
      name: 'Weather Station',
      manufacturer: 'Davis Instruments',
      type: 'Monitoring Device',
      category: 'Electronics',
      features: 'Temperature, Humidity, Wind, Rain',
      estimatedPrice: 150000,
      description: 'Comprehensive weather monitoring system for farm management'
    }
  ]
};

/**
 * Searches machinery database based on user input
 */
export const searchMachinery = (searchTerm: string): any[] => {
  if (!searchTerm || searchTerm.length < 2) return [];

  const normalizedSearch = searchTerm.toLowerCase().trim();
  const results: any[] = [];

  // Search through all categories
  Object.values(MACHINERY_DATABASE).forEach(category => {
    category.forEach(item => {
      const searchableText = `${item.name} ${item.manufacturer} ${item.type} ${item.description}`.toLowerCase();
      
      if (searchableText.includes(normalizedSearch)) {
        results.push({
          ...item,
          matchScore: calculateMatchScore(searchableText, normalizedSearch)
        });
      }
    });
  });

  // Sort by match score (higher is better)
  return results.sort((a, b) => b.matchScore - a.matchScore);
};

/**
 * Calculates match score for search results
 */
const calculateMatchScore = (text: string, searchTerm: string): number => {
  let score = 0;
  
  // Exact match in name gets highest score
  if (text.includes(searchTerm)) {
    score += 10;
  }
  
  // Word matches
  const searchWords = searchTerm.split(' ');
  searchWords.forEach(word => {
    if (text.includes(word)) {
      score += 5;
    }
  });
  
  return score;
};

/**
 * Gets machinery suggestions based on category
 */
export const getMachineryByCategory = (category: string): any[] => {
  const normalizedCategory = category.toLowerCase();
  
  switch (normalizedCategory) {
    case 'tractor':
    case 'tractors':
      return MACHINERY_DATABASE.tractors;
    case 'harvester':
    case 'harvesters':
    case 'combine':
      return MACHINERY_DATABASE.harvesters;
    case 'implement':
    case 'implements':
    case 'tillage':
      return MACHINERY_DATABASE.implements;
    case 'tool':
    case 'tools':
    case 'hand tools':
      return MACHINERY_DATABASE.tools;
    case 'electronic':
    case 'electronics':
    case 'gps':
    case 'sensor':
      return MACHINERY_DATABASE.electronics;
    default:
      return [];
  }
};

/**
 * Populates equipment form with machinery data
 */
export const populateEquipmentFromMachinery = (machineryItem: any) => {
  const { getLookupsByCategory } = useLookupStore.getState();
  
  // Get appropriate category lookup ID
  let categoryId = '';
  const categories = getLookupsByCategory('inventoryCategory');
  
  if (categories.length > 0) {
    const categoryMatch = categories.find((cat: any) => 
      cat.title?.toLowerCase().includes(machineryItem.category.toLowerCase())
    );
    categoryId = categoryMatch?.id || '';
  }

  // Get appropriate type lookup ID
  let typeId = '';
  const types = getLookupsByCategory('equipmentType');
  
  if (types.length > 0) {
    const typeMatch = types.find((type: any) => 
      type.title?.toLowerCase().includes(machineryItem.type.toLowerCase()) ||
      type.title?.toLowerCase().includes('tractor') && machineryItem.type.toLowerCase().includes('tractor')
    );
    typeId = typeMatch?.id || '';
  }

  // Get operational status ID
  let statusId = '';
  const statuses = getLookupsByCategory('equipmentStatus');
  
  if (statuses.length > 0) {
    const statusMatch = statuses.find((status: any) => 
      status.title?.toLowerCase().includes('operational')
    );
    statusId = statusMatch?.id || '';
  }

  return {
    name: machineryItem.name,
    category: categoryId,
    type: typeId,
    manufacturer: machineryItem.manufacturer,
    status: statusId,
    price: machineryItem.estimatedPrice?.toString() || '',
    description: machineryItem.description,
    // Additional fields based on machinery type
    ...(machineryItem.horsepower && { notes: `Horsepower: ${machineryItem.horsepower}` }),
    ...(machineryItem.workingWidth && { notes: `Working Width: ${machineryItem.workingWidth}` }),
    ...(machineryItem.features && { notes: `Features: ${machineryItem.features}` })
  };
};

/**
 * Gets popular machinery suggestions for quick selection
 */
export const getPopularMachinery = (): any[] => {
  return [
    MACHINERY_DATABASE.tractors[0], // John Deere 5075E
    MACHINERY_DATABASE.harvesters[0], // John Deere S660
    MACHINERY_DATABASE.implements[0], // Disc Harrow
    MACHINERY_DATABASE.implements[1], // Seed Drill
    MACHINERY_DATABASE.tools[0], // Hand Hoe
    MACHINERY_DATABASE.electronics[0] // GPS Guidance System
  ];
};
