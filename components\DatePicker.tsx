import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Modal,
  FlatList,
  Platform
} from 'react-native';
import { colors } from '@/constants/colors';
import { Calendar, ChevronLeft, ChevronRight } from 'lucide-react-native';
import { useTranslation } from '@/i18n/useTranslation';

interface DatePickerProps {
  label?: string;
  value: Date | null;
  onChange: (date: Date) => void;
  placeholder?: string;
  required?: boolean;
  startOffset?: number; // months in the past (negative) or future (positive)
  endOffset?: number; // months in the future
  maximumDate?: Date;
  minimumDate?: Date;
}

const DatePicker: React.FC<DatePickerProps> = ({
  label,
  value,
  onChange,
  placeholder = 'Select a date',
  required = false,
  startOffset = -12, // Default to 1 year in the past
  endOffset = 12, // Default to 1 year in the future
  maximumDate,
  minimumDate,
}) => {
  const [showModal, setShowModal] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(value || new Date());
  const {isRTL}=useTranslation()
  // Generate days for the current month
  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  };
  
  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay();
  };
  
  const generateDays = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const daysInMonth = getDaysInMonth(year, month);
    const firstDay = getFirstDayOfMonth(year, month);
    
    const days = [];
    
    // Add empty spaces for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push({ day: '', empty: true });
    }
    
    // Add days of the month
    for (let i = 1; i <= daysInMonth; i++) {
      const date = new Date(year, month, i);
      const isDisabled = 
        (minimumDate && date < minimumDate) || 
        (maximumDate && date > maximumDate);
      
      days.push({ 
        day: i, 
        date, 
        disabled: isDisabled,
        isToday: isToday(date),
        isSelected: value && isSameDay(date, value),
      });
    }
    
    return days;
  };
  
  const isToday = (date: Date) => {
    const today = new Date();
    return isSameDay(date, today);
  };
  
  const isSameDay = (date1: Date, date2: Date) => {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    );
  };
  
  const handleDayPress = (day: any) => {
    if (day.disabled) return;
    
    onChange(day.date);
    setShowModal(false);
  };
  
  const goToPreviousMonth = () => {
    const newDate = new Date(currentMonth);
    newDate.setMonth(newDate.getMonth() - 1);
    setCurrentMonth(newDate);
  };
  
  const goToNextMonth = () => {
    const newDate = new Date(currentMonth);
    newDate.setMonth(newDate.getMonth() + 1);
    setCurrentMonth(newDate);
  };
  
  const formatDate = (date: Date | null) => {
    if (!date) return placeholder;
    
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };
  
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  
  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  
  // Check if the current month is at the limit
  const isPreviousMonthDisabled = () => {
    if (!minimumDate) {
      if (startOffset >= 0) return false;
      
      const minDate = new Date();
      minDate.setMonth(minDate.getMonth() + startOffset);
      return (
        currentMonth.getFullYear() < minDate.getFullYear() ||
        (currentMonth.getFullYear() === minDate.getFullYear() && 
         currentMonth.getMonth() <= minDate.getMonth())
      );
    }
    
    return (
      currentMonth.getFullYear() < minimumDate.getFullYear() ||
      (currentMonth.getFullYear() === minimumDate.getFullYear() && 
       currentMonth.getMonth() <= minimumDate.getMonth())
    );
  };
  
  const isNextMonthDisabled = () => {
    if (!maximumDate) {
      if (endOffset <= 0) return false;
      
      const maxDate = new Date();
      maxDate.setMonth(maxDate.getMonth() + endOffset);
      return (
        currentMonth.getFullYear() > maxDate.getFullYear() ||
        (currentMonth.getFullYear() === maxDate.getFullYear() && 
         currentMonth.getMonth() >= maxDate.getMonth())
      );
    }
    
    return (
      currentMonth.getFullYear() > maximumDate.getFullYear() ||
      (currentMonth.getFullYear() === maximumDate.getFullYear() && 
       currentMonth.getMonth() >= maximumDate.getMonth())
    );
  };
  
  return (
    <View style={styles.container}>
      {label && (
        <View style={[styles.labelContainer,isRTL && styles.rtlDirection]}>
          <Text style={styles.label}>{label}</Text>
          {required && <Text style={styles.required}>*</Text>}
        </View>
      )}
      
      <TouchableOpacity
        style={[styles.input,isRTL && styles.rtlDirection]}
        onPress={() => setShowModal(true)}
      >
        <Calendar size={20} color={colors.gray[500]} style={styles.icon} />
        <Text style={[
          styles.inputText,
          !value && styles.placeholderText
        ]}>
          {formatDate(value)}
        </Text>
      </TouchableOpacity>
      
      <Modal
        visible={showModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Date</Text>
              <TouchableOpacity onPress={() => setShowModal(false)}>
                <Text style={styles.closeButton}>Close</Text>
              </TouchableOpacity>
            </View>
            
            <View style={styles.calendarHeader}>
              <TouchableOpacity 
                onPress={goToPreviousMonth}
                disabled={isPreviousMonthDisabled()}
                style={[
                  styles.navigationButton,
                  isPreviousMonthDisabled() && styles.disabledButton
                ]}
              >
                <ChevronLeft 
                  size={24} 
                  color={isPreviousMonthDisabled() ? colors.gray[300] : colors.gray[700]} 
                />
              </TouchableOpacity>
              
              <Text style={styles.monthYearText}>
                {monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
              </Text>
              
              <TouchableOpacity 
                onPress={goToNextMonth}
                disabled={isNextMonthDisabled()}
                style={[
                  styles.navigationButton,
                  isNextMonthDisabled() && styles.disabledButton
                ]}
              >
                <ChevronRight 
                  size={24} 
                  color={isNextMonthDisabled() ? colors.gray[300] : colors.gray[700]} 
                />
              </TouchableOpacity>
            </View>
            
            <View style={styles.daysOfWeek}>
              {dayNames.map((day, index) => (
                <Text key={index} style={styles.dayOfWeekText}>{day}</Text>
              ))}
            </View>
            
            <View style={styles.calendarGrid}>
              {generateDays().map((day, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.dayCell,
                    day.empty && styles.emptyCell,
                    day.isToday && styles.todayCell,
                    day.isSelected && styles.selectedCell,
                    day.disabled && styles.disabledCell,
                  ]}
                  onPress={() => !day.empty && !day.disabled && handleDayPress(day)}
                  disabled={day.empty || day.disabled}
                >
                  <Text style={[
                    styles.dayText,
                    day.isToday && styles.todayText,
                    day.isSelected && styles.selectedText,
                    day.disabled && styles.disabledText,
                  ]}>
                    {day.day}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
            
            <View style={styles.modalFooter}>
              <TouchableOpacity 
                style={styles.todayButton}
                onPress={() => {
                  const today = new Date();
                  if (
                    (!minimumDate || today >= minimumDate) && 
                    (!maximumDate || today <= maximumDate)
                  ) {
                    onChange(today);
                    setShowModal(false);
                  }
                }}
                disabled={
                  (minimumDate && new Date() < minimumDate) || 
                  (maximumDate && new Date() > maximumDate)
                }
              >
                <Text style={[
                  styles.todayButtonText,
                  ((minimumDate && new Date() < minimumDate) || 
                   (maximumDate && new Date() > maximumDate)) && styles.disabledText
                ]}>
                  Today
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.clearButton}
                onPress={() => {
                  onChange(new Date());
                  setShowModal(false);
                }}
              >
                <Text style={styles.clearButtonText}>Select</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
    rtlDirection:{
    flexDirection: 'row-reverse',
  },
  rtlTextAlign:{
    textAlign: 'right',
  },
  labelContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
  },
  required: {
    color: colors.danger,
    marginLeft: 4,
  },
  input: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  icon: {
    marginRight: 8,
  },
  inputText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  placeholderText: {
    color: colors.gray[500],
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 20,
    width: '90%',
    maxWidth: 400,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  closeButton: {
    fontSize: 16,
    color: colors.primary,
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  navigationButton: {
    padding: 8,
  },
  disabledButton: {
    opacity: 0.5,
  },
  monthYearText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
  },
  daysOfWeek: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  dayOfWeekText: {
    width: '14.28%',
    textAlign: 'center',
    fontSize: 12,
    fontWeight: '500',
    color: colors.gray[600],
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  dayCell: {
    width: '14.28%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  emptyCell: {
    // Empty cell style
  },
  todayCell: {
    backgroundColor: colors.gray[100],
    borderRadius: 20,
  },
  selectedCell: {
    backgroundColor: colors.primary,
    borderRadius: 20,
  },
  disabledCell: {
    opacity: 0.3,
  },
  dayText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  todayText: {
    fontWeight: '600',
    color: colors.gray[800],
  },
  selectedText: {
    color: colors.white,
    fontWeight: '600',
  },
  disabledText: {
    color: colors.gray[400],
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  todayButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  todayButtonText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
  },
  clearButton: {
    backgroundColor: colors.primary,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  clearButtonText: {
    fontSize: 14,
    color: colors.white,
    fontWeight: '500',
  },
});

export default DatePicker;