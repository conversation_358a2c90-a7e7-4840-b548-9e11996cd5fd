import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { auth, firestore } from '@/firebase/config';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut as firebaseSignOut,
  sendPasswordResetEmail,
  updateProfile,
  User as FirebaseUser,
  sendEmailVerification
} from 'firebase/auth';
import {
  doc,
  setDoc,
  getDoc,
  collection,
  query,
  where,
  getDocs,
  updateDoc,
  serverTimestamp,
  Timestamp,
  arrayUnion
} from 'firebase/firestore';

import { useFarmStore } from './farm-store'; // Import useFarmStore
import { logEvent } from '@/store/logging';
export interface User {
  id: string;
  email: string;
  name?: string;
  displayName?: string;
  photoURL?: string;
  role: 'owner' | 'admin' | 'caretaker' | 'admin';
  phone?: string;
  status?: 'active' | 'inactive' | 'pending';
  farms?: string[];
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
  lastLogin?: Timestamp;
  language?: string;
  emailVerified?: boolean;
}

interface RegisterData {
  email: string;
  password: string;
  name?: string;
  photoURL?: string;
  phone?: string;
  role: 'owner' | 'admin' | 'caretaker' | 'admin';
}

interface AuthState {
  user: User | null;
  isLoading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  language: string;

  // Auth actions
  signIn: (email: string, password: string) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updateUserProfile: (data: Partial<User>) => Promise<void>;
  clearError: () => void; // Added clearError function
  setLanguage: (language: string) => Promise<void>;

  // User management
  inviteUser: (data: Partial<User> & { farmId: string }) => Promise<void>;
  getUsersByFarm: (farmId: string) => Promise<User[]>;
  getUserById: (userId: string) => Promise<User | null>;
  updateUserStatus: (userId: string, status: 'active' | 'inactive' | 'pending') => Promise<void>;
  removeUserFromFarm: (userId: string, farmId: string) => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isLoading: false,
      error: null,
      isAuthenticated: false,
      language: 'en',

      signIn: async (email, password) => {
        set({ isLoading: true, error: null });
        try {
          const userCredential = await signInWithEmailAndPassword(auth, email, password);

          // Reload user to get fresh emailVerified status
          await userCredential.user.reload();

          // Check if email is verified
          if (!userCredential.user.emailVerified) {
            // Sign out the user
            await firebaseSignOut(auth);
            throw new Error('Please verify your email before logging in');
          }

          const userDoc = await getDoc(doc(firestore, 'users', userCredential.user.uid));

          if (userDoc.exists()) {
            const userData = userDoc.data() as User;
            // console.log({ userData })
            // Update last login and emailVerified status
            await updateDoc(doc(firestore, 'users', userCredential.user.uid), {
              lastLogin: serverTimestamp(),
              updatedAt: serverTimestamp(),
              emailVerified: true
            });

            set({
              user: {
                id: userCredential.user.uid,
                ...userData,
                emailVerified: true
              },
              isLoading: false,
              isAuthenticated: true,
              error: null
            });
            logEvent('login', {
              userId: userCredential.user.uid,
              email: userData.email,
            });

          } else {
            throw new Error('User data not found');
          }
        } catch (error: any) {
          // Add specific error for email verification
          // console.log("error", { error })
          let errorMessage = 'Failed to sign in';

          if (error.message === 'Please verify your email before logging in') {
            errorMessage = error.message;
          } else if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password') {
            errorMessage = 'Invalid email or password';
          } else if (error.code === 'auth/too-many-requests') {
            errorMessage = 'Too many failed login attempts. Please try again later.';
          } else if (error.code === 'auth/user-disabled') {
            errorMessage = 'This account has been disabled. Please contact support.';
          }

          set({ isLoading: false, error: errorMessage });
          throw new Error(errorMessage);
        }
      },

      register: async (data) => {
        set({ isLoading: true, error: null });
        try {
          // Create user in Firebase Auth
          const userCredential = await createUserWithEmailAndPassword(auth, data.email, data.password);

          // Send verification email
          await sendEmailVerification(userCredential.user);

          // Update profile if name or photo provided
          if (data.name || data.photoURL) {
            await updateProfile(userCredential.user, {
              displayName: data.name,
              photoURL: data.photoURL
            });
          }

          // Create user document in Firestore
          const userData: User = {
            id: userCredential.user.uid,
            email: data.email,
            name: data.name || '',
            photoURL: data.photoURL || '',
            role: data.role,
            phone: data.phone || '',
            status: 'active',
            farms: [],
            createdAt: Timestamp.now(),
            updatedAt: Timestamp.now(),
            lastLogin: Timestamp.now(),
            language: get().language,
            emailVerified: false
          };

          await setDoc(doc(firestore, 'users', userCredential.user.uid), userData);

          // Sign out the user to force email verification
          await firebaseSignOut(auth);

          set({
            isLoading: false,
            error: null
          });

          // Return success message instead of logging in
          return "Verification email sent. Please check your inbox and verify before logging in.";
        } catch (error: any) {
          console.error('Registration error:', error);
          let errorMessage = 'Failed to register';

          if (error.code === 'auth/email-already-in-use') {
            errorMessage = 'Email is already in use';
          } else if (error.code === 'auth/invalid-email') {
            errorMessage = 'Invalid email address';
          } else if (error.code === 'auth/weak-password') {
            errorMessage = 'Password is too weak';
          }

          set({ isLoading: false, error: errorMessage });
          throw new Error(errorMessage);
        }
      },

      signOut: async () => {
        set({ isLoading: true });
        try {
          logEvent('logout', { userId: get().user?.id });
          await firebaseSignOut(auth);
          set({
            user: null,
            isLoading: false,
            isAuthenticated: false,
            error: null
          });
          // Clear farm store state
          useFarmStore.getState().resetState();
        } catch (error: any) {
          console.error('Sign out error:', error);
          set({
            isLoading: false,
            error: 'Failed to sign out'
          });
          throw error;
        }
      },

      resetPassword: async (email) => {
        set({ isLoading: true, error: null });
        try {
          await sendPasswordResetEmail(auth, email);
          set({ isLoading: false });
        } catch (error: any) {
          console.error('Reset password error:', error);
          let errorMessage = 'Failed to send password reset email';

          if (error.code === 'auth/user-not-found') {
            errorMessage = 'No user found with this email address';
          } else if (error.code === 'auth/invalid-email') {
            errorMessage = 'Invalid email address';
          }

          set({ isLoading: false, error: errorMessage });
          throw new Error(errorMessage);
        }
      },

      updateUserProfile: async (data) => {
        const { user } = get();
        if (!user) throw new Error('User not authenticated');

        set({ isLoading: true, error: null });
        try {
          // Update Firestore document
          await updateDoc(doc(firestore, 'users', user.id), {
            ...data,
            updatedAt: serverTimestamp()
          });

          // Update local state
          set({
            user: { ...user, ...data },
            isLoading: false
          });
          logEvent('update_profile', { userId: user.id, updatedFields: Object.keys(data) });
        } catch (error: any) {
          console.error('Update profile error:', error);
          set({
            isLoading: false,
            error: 'Failed to update profile'
          });
          throw error;
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setLanguage: async (language) => {
        const { user } = get();
        set({ language });

        // If user is authenticated, update their language preference
        if (user) {
          try {
            await updateDoc(doc(firestore, 'users', user.id), {
              language,
              updatedAt: serverTimestamp()
            });
          } catch (error) {
            console.error('Error updating language preference:', error);
          }
        }
      },

      inviteUser: async (data) => {
        const { user } = get();
        if (!user) throw new Error('User not authenticated');
        if (user.role !== 'owner' && user.role !== 'admin' && user.role !== 'admin') {
          throw new Error('Insufficient permissions');
        }

        set({ isLoading: true, error: null });
        try {
          // Check if user with this email already exists
          const usersRef = collection(firestore, 'users');
          const q = query(usersRef, where('email', '==', data.email));
          const querySnapshot = await getDocs(q);

          let userId;

          if (!querySnapshot.empty) {
            // User exists, add farm to their farms array
            const existingUser = querySnapshot.docs[0];
            userId = existingUser.id;

            const existingFarms = existingUser.data().farms || [];
            if (!existingFarms.includes(data.farmId)) {
              await updateDoc(doc(firestore, 'users', userId), {
                farms: [...existingFarms, data.farmId],
                updatedAt: serverTimestamp()
              });
            }
          } else {
            // Create new user with pending status
            userId = `pending_${Date.now()}`;
            const userData: Partial<User> = {
              id: userId,
              email: data.email,
              name: data.name || '',
              role: data.role,
              status: 'pending',
              farms: [data.farmId],
              createdAt: Timestamp.now(),
              updatedAt: Timestamp.now()
            };

            await setDoc(doc(firestore, 'users', userId), userData);
          }

          set({ isLoading: false });
        } catch (error: any) {
          console.error('Invite user error:', error);
          set({
            isLoading: false,
            error: 'Failed to invite user'
          });
          throw error;
        }
      },

      getUsersByFarm: async (farmId) => {
        try {
          const usersRef = collection(firestore, 'users');
          const q = query(usersRef, where('assignedFarmIds', 'array-contains', farmId));
          const querySnapshot = await getDocs(q);

          const users: User[] = [];
          querySnapshot.forEach((doc) => {
            users.push({ id: doc.id, ...doc.data() } as User);
          });

          return users;
        } catch (error) {
          console.error('Get users by farm error:', error);
          throw error;
        }
      },

      getUserById: async (userId) => {
        try {
          if (!userId) return null;

          const userDoc = await getDoc(doc(firestore, 'users', userId));
          if (userDoc.exists()) {
            return { id: userDoc.id, ...userDoc.data() } as User;
          }
          return null;
        } catch (error) {
          console.error('Get user by ID error:', error);
          return null;
        }
      },

      updateUserStatus: async (userId, status) => {
        const { user } = get();
        if (!user) throw new Error('User not authenticated');
        if (user.role !== 'owner' && user.role !== 'admin') {
          throw new Error('Insufficient permissions');
        }

        try {
          await updateDoc(doc(firestore, 'users', userId), {
            status,
            updatedAt: serverTimestamp()
          });
        } catch (error) {
          console.error('Update user status error:', error);
          throw error;
        }
      },

      removeUserFromFarm: async (userId, farmId) => {
        const { user } = get();
        if (!user) throw new Error('User not authenticated');
        if (user.role !== 'owner' && user.role !== 'admin') {
          throw new Error('Insufficient permissions');
        }

        try {
          const userDoc = await getDoc(doc(firestore, 'users', userId));
          if (userDoc.exists()) {
            const userData = userDoc.data();
            const farms = userData.farms || [];
            const updatedFarms = farms.filter((id: string) => id !== farmId);

            await updateDoc(doc(firestore, 'users', userId), {
              farms: updatedFarms,
              updatedAt: serverTimestamp()
            });
          }
        } catch (error) {
          console.error('Remove user from farm error:', error);
          throw error;
        }
      },
      assignFarmToUser: async (userId: string, farmId: string, role: string) => {
        try {
          const userRef = doc(firestore, "users", userId);

          // Update assignedFarmIds and optionally roles
          await updateDoc(userRef, {
            assignedFarmIds: arrayUnion(farmId),
            // roles: {
            //   [farmId]: role, // e.g., { "farm123": "worker" }
            // }
          });

          // console.log(`Successfully assigned farm ${farmId} to user ${userId} with role ${role}`);
        } catch (error) {
          console.error("Error assigning farm to user:", error);
          throw error;
        }
      },
      // // Add this method to the auth store
      // getUserById: async (userId) => {
      //   try {
      //     const userDoc = await getDoc(doc(firestore, 'users', userId));
      //     if (userDoc.exists()) {
      //       return { id: userDoc.id, ...userDoc.data() };
      //     }
      //     return null;
      //   } catch (error) {
      //     console.error('Get user by ID error:', error);
      //     throw error;
      //   }
      // },

      // Update the removeFarmFromUser method to handle multiple farms
      removeFarmFromUser: async (userId: string, farmId: string) => {
        const { user } = get();
        if (!user) throw new Error('User not authenticated');
        if (user.role !== 'owner' && user.role !== 'admin') {
          throw new Error('Insufficient permissions');
        }

        try {
          const userDoc = await getDoc(doc(firestore, 'users', userId));
          if (userDoc.exists()) {
            const userData = userDoc.data();
            const assignedFarmIds = userData.assignedFarmIds || [];
            const updatedFarmIds = assignedFarmIds.filter((id) => id !== farmId);

            await updateDoc(doc(firestore, 'users', userId), {
              assignedFarmIds: updatedFarmIds,
              updatedAt: serverTimestamp()
            });
          }
        } catch (error) {
          console.error('Remove farm from user error:', error);
          throw error;
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        language: state.language
      }),
    }
  )
);
