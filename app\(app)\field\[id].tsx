import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Alert,
  RefreshControl,
} from 'react-native';
import { useLocalSearchParams, router, Stack } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import Button from '@/components/Button';
import {
  Calendar,
  Ruler,
  History,
  Leaf,
  BarChart3,
  MapPin,
  MoreVertical,
  Edit,
  Trash,
  Plus,
  Archive,
  Wheat,
  Trash2,
} from 'lucide-react-native';
import Toast from 'react-native-toast-message';
import InactiveStatusModal from '@/components/InactiveStatusModal';
import { Crop } from '@/types';
import { useFocusEffect } from '@react-navigation/native';
import { useLanguage } from '@/i18n/translations/LanguageProvider';
import { useTranslation } from '@/i18n/useTranslation';
import { MaterialIcons } from '@expo/vector-icons';
import { normalizeDate } from '@/utils/dateUtils';
import { ImageSlider } from '@/components/ImageSlider';

export default function FieldDetailScreen() {
  const { id } = useLocalSearchParams();
  const {
    getField,
    updateField,
    deleteField,
    currentFarm,
    getFieldCrops,

    isLoading,
    markFieldInactive,
    fields, // Add this to the destructuring
    tasks, // Add this if you're using tasks
    crops, // Add this if you're using crops
  } = useFarmStore();
  const { t, isRTL } = useTranslation();

  const [showOptions, setShowOptions] = useState(false);
  const [activeTab, setActiveTab] = useState('details');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showInactiveModal, setShowInactiveModal] = useState(false);
  const [activeCrop, setActiveCrop] = useState<Crop | null>(null);
  const [cropHistory, setCropHistory] = useState<Crop[]>([]);
  const [field, setField] = useState<any>(null);
  const [fieldTasks, setTask] = useState<any>([])
  const [refreshing, setRefreshing] = useState(false);
  // Find the field with the matching ID
  // const field = 
  // const fieldTasks = ;

  const loadFieldDetails = async () => {
    try {
      setRefreshing(true);
      const fieldData = await getField(id as string);
      setField(fieldData);

      if (fieldData) {
        // Fetch field crops to get the most up-to-date crop data
        await fetchFieldCrops(fieldData.id);
      }
    } catch (error) {
      console.error('Error loading field details:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const fetchFieldCrops = async (fieldId: string) => {
    try {
      const { activeCrop, crops } = await getFieldCrops(fieldId);
      console.log('Fetched crops for field:', fieldId, { activeCrop, crops });

      // Set active crop
      setActiveCrop(activeCrop);

      // Set crop history (all crops except the active one)
      const historycrops = crops.filter(crop => crop.id !== activeCrop?.id);
      setCropHistory(historycrops);

      console.log('Active crop set:', activeCrop);
      console.log('Crop history set:', historycrops);
    } catch (error) {
      console.error('Error fetching field crops:', error);
      // Reset states on error
      setActiveCrop(null);
      setCropHistory([]);
    }
  };

  const onRefresh = async () => {
    await loadFieldDetails();
  };

  useEffect(() => {
    if (id) {
      loadFieldDetails();
      // Filter tasks for this field
      setTask(tasks ? tasks.filter(t => t.entityId === id) : []);
    }
  }, [id])

  // useFocusEffect(
  //   React.useCallback(() => {
  //     loadFieldDetails();
  //     return () => {};
  //   }, [id])
  // );

  if (!field) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.notFoundContainer}>
          <Text style={styles.notFoundText}>Field not found</Text>
          <Button
            title="Go Back"
            onPress={() => router.back()}
            style={styles.backButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  const handleDelete = () => {
    Alert.alert(
      'Delete Field',
      'Are you sure you want to delete this field? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteField(id as string);
              Toast.show({
                type: 'success',
                text1: 'Success',
                text2: 'Field deleted successfully',
              });
              router.back();
            } catch (error) {
              console.error('Error deleting field:', error);
              Toast.show({
                type: 'error',
                text1: 'Error',
                text2: 'Failed to delete field',
              });
            }
          }
        }
      ]
    );
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not set';

    const date = new Date(dateString);
    return date.toLocaleDateString([], {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return colors.success;
      case 'inactive':
        return colors.gray[400];
      case 'irrigated':
        return colors.info;
      default:
        return colors.gray[500];
    }
  };

  const handleMarkInactive = async (data: {
    reason: string;
    notes?: string;
    image?: string;
    cascadeToPlants?: boolean;
  }) => {
    try {
      await markFieldInactive(id as string, data);
      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'Field marked as inactive',
      });
    } catch (error) {
      console.error('Error marking field as inactive:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to mark field as inactive',
      });
    }
  };

  const isInactive = field.isInactive === true;


  const renderCurrentCropTab = () => {
    // Calculate crop statistics
    const cropStats = {
      daysPlanted: activeCrop ? Math.floor((new Date().getTime() - new Date(activeCrop.plantedDate).getTime()) / (1000 * 60 * 60 * 24)) : 0,
      daysToHarvest: activeCrop && activeCrop.expectedHarvestDate ? Math.floor((new Date(activeCrop.expectedHarvestDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) : 0,
      totalCrops: cropHistory.length + (activeCrop ? 1 : 0),
      fieldTasks: fieldTasks.length,
    };

    return (
      <View style={styles.tabContent}>
        {/* Crop Statistics */}
        {activeCrop && (
          <View style={styles.statsContainer}>
            <View style={[styles.statCard, isRTL && { alignItems: 'flex-end' }]}>
              <Text style={[styles.statNumber, { color: colors.primary }]}>{cropStats.daysPlanted}</Text>
              <Text style={[styles.statLabel, isRTL && { textAlign: 'right' }]}>{t('field.daysPlanted')}</Text>
            </View>
            <View style={[styles.statCard, isRTL && { alignItems: 'flex-end' }]}>
              <Text style={[styles.statNumber, { color: cropStats.daysToHarvest > 0 ? colors.success : colors.warning }]}>
                {cropStats.daysToHarvest > 0 ? cropStats.daysToHarvest : 'Ready'}
              </Text>
              <Text style={[styles.statLabel, isRTL && { textAlign: 'right' }]}>{t('field.daysToHarvest')}</Text>
            </View>
            <View style={[styles.statCard, isRTL && { alignItems: 'flex-end' }]}>
              <Text style={[styles.statNumber, { color: colors.info }]}>{cropStats.totalCrops}</Text>
              <Text style={[styles.statLabel, isRTL && { textAlign: 'right' }]}>{t('field.totalCrops')}</Text>
            </View>
            <View style={[styles.statCard, isRTL && { alignItems: 'flex-end' }]}>
              <Text style={[styles.statNumber, { color: colors.secondary }]}>{cropStats.fieldTasks}</Text>
              <Text style={[styles.statLabel, isRTL && { textAlign: 'right' }]}>{t('field.activeTasks')}</Text>
            </View>
          </View>
        )}

        {activeCrop ? (
        <View style={styles.cropDetailsContainer}>
          <View style={[styles.cropHeader, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
            <View style={styles.cropTitleContainer}>
              <View style={styles.cropIconContainer}>
                <Wheat size={24} color={colors.primary} />
              </View>
              <Text style={styles.cropTitle}>{activeCrop.cropType}</Text>

            </View>

            {/* <Button
              title={t("field.details")}
              onPress={() => router.push(`/field/crop/${activeCrop.id}`)}
            /> */}
          </View>

          {activeCrop.images && activeCrop.images.length > 0 && (
            <Image
              source={{ uri: activeCrop.images[0] }}
              style={styles.cropImage}
              resizeMode="cover"
            />
          )}

          <View style={styles.cropInfoContainer}>
            <View style={[styles.cropInfoRow, { flexDirection: isRTL ? "row-reverse" : "row" }]}>
              <View style={styles.cropInfoItem}>
                <Text style={[styles.cropInfoLabel, { textAlign: isRTL ? "right" : "left" }]}>{t(`entity.plant.plantedDate`)}</Text>
                <Text style={[styles.cropInfoValue, { textAlign: isRTL ? "right" : "left" }]}>
                  {new Date(activeCrop.plantedDate).toLocaleDateString()}
                </Text>
              </View>

              {activeCrop.expectedHarvestDate && (
                <View style={styles.cropInfoItem}>
                  <Text style={[styles.cropInfoLabel, { textAlign: isRTL ? "right" : "left" }]}>{t('entity.plant.expectedHarvestDate')}</Text>
                  <Text style={[styles.cropInfoValue, { textAlign: isRTL ? "right" : "left" }]}>
                    {new Date(activeCrop.expectedHarvestDate).toLocaleDateString()}
                  </Text>
                </View>
              )}
            </View>

            {activeCrop.soilType && (
              <View style={styles.cropInfoRow}>
                <View style={styles.cropInfoItem}>
                  <Text style={[styles.cropInfoLabel, { textAlign: isRTL ? "right" : "left" }]}>{t('entity.garden.soilType')}</Text>
                  <Text style={[styles.cropInfoValue, { textAlign: isRTL ? "right" : "left" }]}>{activeCrop.soilType}</Text>
                </View>
              </View>
            )}

            {activeCrop.notes && (
              <View style={styles.notesContainer}>
                <Text style={[styles.notesLabel, { textAlign: isRTL ? "right" : "left" }]}>{t('entity.plant.notes')}</Text>
                <Text style={[styles.notesText, { textAlign: isRTL ? "right" : "left" }]}>{activeCrop.notes}</Text>
              </View>
            )}
          </View>

          <View style={styles.cropActionsContainer}>
            <Button
              title={t('field.markAsHarvested')}   //"Mark as Harvested"
              onPress={() => router.push(`/field/harvest?id=${field.id}&cropId=${activeCrop.id}`)}
              // onPress={() => router.push(`/field/harvest/${activeCrop.id}`)}
              style={styles.harvestButton}
            />

            <Button
              title={t('add.task')}
              onPress={() => router.push(`/task/create?fieldId=${field.id}&cropId=${activeCrop.id}`)}
              style={styles.harvestTaskButton}
            // type="secondary"
            />
          </View>
        </View>
      ) : (
        <View style={[styles.emptyCropContainer, isRTL && { textAlign: 'right' }]}>
          <MaterialIcons name="agriculture" size={48} color={colors.gray[400]} />
          <Text style={[styles.emptyCropText, isRTL && { textAlign: 'right' }]}>
            {t('field.no_active_crop', 'No active crop for this field')}
          </Text>
          <Button
            title={t('field.add_crop', 'Add Crop')}
            onPress={() => router.push(`/field/add-crop?id=${field.id}`)}
            style={styles.addCropButton}
          />
        </View>
      )}
    </View>
    );
  };

  const renderCropHistoryTab = () => (
    <View style={styles.tabContent}>
      <View style={[styles.historyHeader]}>
        <Text style={[styles.historyTitle, { textAlign: isRTL ? "right" : "left" }]}>{t('field.cropHistory')}</Text>
        <Text style={[styles.historySubtitle, { textAlign: isRTL ? "right" : "left" }]}>
          {cropHistory.length} {t('field.previous')} {cropHistory.length === 1 ? t('field.crop') : t('field.crops')}
        </Text>
      </View>

      {cropHistory.length > 0 ? (
        <View style={[styles.timelineContainer, { flexDirection: isRTL ? 'column-reverse' : 'column' }]}>
          {cropHistory.map((crop, index) => (
            <View style={styles.card}>
              <View style={[styles.headers, { flexDirection: isRTL ? "row-reverse" : "row" ,justifyContent:'space-between'}]}>
                <View style={{ flexDirection: isRTL ? "row-reverse" : "row" }}>
                  <MaterialIcons name="grass" size={24} color={colors.primary} />
                  <Text style={[styles.cropName, isRTL && styles.textRTL]}>{crop.cropType}</Text>
                </View>
                <View style={{ flexDirection: isRTL ? "row-reverse" : "row" }}>
                  <Text style={[styles.text, isRTL && styles.textRTL]}>
                    {t('crop.Yielded')}: {crop?.harvestQuantity || 0} {crop?.harvestUnit || 'kg'}
                  </Text>
                </View>
              </View>

              <View style={styles.row}>

                <MaterialIcons name="event" size={20} color={colors.gray[600]} />
                <Text style={[styles.text, isRTL && styles.textRTL]}>
                  {t('crop.planted')}: {normalizeDate(crop.plantedDate)}
                </Text>
                {/* </View> */}

              </View>

              <View style={styles.row}>
                <MaterialIcons name="event-available" size={20} color={colors.gray[600]} />
                <Text style={[styles.text, isRTL && styles.textRTL]}>
                  {t('crop.harvested')}: {normalizeDate(crop?.actualHarvestDate)}
                </Text>
              </View>

              {crop.yield && (
                <View style={styles.row}>
                  <MaterialIcons name="bar-chart" size={20} color={colors.gray[600]} />
                  <Text style={[styles.text, isRTL && styles.textRTL]}>
                    {t('crop.yield')}: {crop.yield} {crop.unit || 'kg'}
                  </Text>
                </View>
              )}

              {crop.variety && (
                <View style={styles.row}>
                  <MaterialIcons name="category" size={20} color={colors.gray[600]} />
                  <Text style={[styles.text, isRTL && styles.textRTL]}>
                    {t('crop.variety')}: {crop.variety}
                  </Text>
                </View>
              )}

              {crop.area && (
                <View style={styles.row}>
                  <MaterialIcons name="square-foot" size={20} color={colors.gray[600]} />
                  <Text style={[styles.text, isRTL && styles.textRTL]}>
                    {t('crop.area')}: {crop.area} {crop.areaUnit || 'acre'}
                  </Text>
                </View>
              )}
            </View>
          ))}
        </View>
      ) : (
        <View style={styles.emptyHistoryContainer}>
          <History size={48} color={colors.gray[400]} />
          <Text style={styles.emptyHistoryText}>No crop history for this field</Text>
        </View>
      )}
    </View>
  );

  const renderCropTasks = () => {
    return (
      <View style={styles.tasksContainer}>
        {fieldTasks.length > 0 ? (
          fieldTasks.map((task, index) => (
            <TouchableOpacity
              key={index}
              style={styles.taskItem}
              onPress={() => router.push(`/task/${task.id}`)}
            >
              <View style={[
                styles.taskPriorityIndicator,
                {
                  backgroundColor: task.priority === 'high'
                    ? colors.danger
                    : task.priority === 'medium'
                      ? colors.warning
                      : colors.success
                }
              ]} />
              <View style={styles.taskContent}>
                <Text style={styles.taskTitle}>{task.title}</Text>
                <Text style={styles.taskDate}>
                  Due {new Date(task.dueDate).toLocaleDateString([], {
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                  })}
                </Text>
              </View>
              <View style={[
                styles.taskStatusBadge,
                {
                  backgroundColor: task.status === 'completed'
                    ? colors.success + '20'
                    : task.status === 'overdue'
                      ? colors.danger + '20'
                      : colors.primary + '20'
                }
              ]}>
                <Text style={[
                  styles.taskStatusText,
                  {
                    color: task.status === 'completed'
                      ? colors.success
                      : task.status === 'overdue'
                        ? colors.danger
                        : colors.primary
                  }
                ]}>
                  {task.status.charAt(0).toUpperCase() + task.status.slice(1)}
                </Text>
              </View>
            </TouchableOpacity>
          ))
        ) : (
          <View style={styles.emptyContainer}>
            <MaterialIcons name="assignment-late" size={48} color={colors.gray[400]} />
            <Text style={[styles.emptyText, isRTL && { textAlign: 'right' }]}>
              {t('task.emptyFieldTasks', 'No tasks for this field')}
            </Text>
          </View>
        )}

        <TouchableOpacity
          style={[styles.addTaskButton, isRTL && { flexDirection: 'row-reverse' }]}
          onPress={() => router.push('/task/create')}
        >
          <Plus size={20} color={colors.white} />
          <Text style={styles.addTaskText}>{t('task.add')}</Text>
        </TouchableOpacity>
      </View>
    )
  }

  return (
    <>
      <Stack.Screen
        options={{
          title: field?.name || t('field.details'),
          // headerRight: () => (
          //   <TouchableOpacity
          //     onPress={() => setShowOptions(true)}
          //     style={styles.headerButton}
          //   >
          //  </TouchableOpacity>   <MoreVertical size={24} color={colors.gray[700]} />
          //   </TouchableOpacity>
          // ),
        }}
      />

      <SafeAreaView style={styles.container}>
        {/* {showOptions && (
          <View style={styles.options</View>Menu}>
            <TouchableOpacity
              style={styles.optionItem}
              onPress={() => {
                setShowOptions(false);

                router.push({
                  pathname: '/field/create',
                  params: { editMode: true, fieldId: field.id }
                });
              }}
            >
              <Edit size={20} color={colors.gray[700]} />
              <Text style={styles.optionText}>Edit Field</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.optionItem}
              onPress={() => {
                setShowOptions(false);
                setShowInactiveModal(true);
              }}
            >
              <Archive size={20} color={colors.gray[700]} />
              <Text style={styles.optionText}>Mark as Inactive</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.optionItem}
              onPress={() => {
                setShowOptions(false);
                setShowDeleteConfirm(true);
              }}
            >
              <Trash2 size={20} color={colors.danger} />
              <Text style={[styles.optionText, { color: colors.danger }]}>Delete Field</Text>
            </TouchableOpacity>
          </View>
        )} */}

        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
        >
          {/* Display field images with slider */}
          {field.images && field.images.length > 0 ? (
            <View style={styles.imageSliderContainer}>
              <ImageSlider
                images={field.images}
                height={200}
                showIndicators={true}
              />
            </View>
          ) : field.image ? (
            <Image
              source={{ uri: field.image }}
              style={styles.headerImage}
              resizeMode="cover"
            />
          ) : null}

          {isInactive && (
            <View style={styles.inactiveBanner}>
              <Archive size={16} color={colors.white} />
              <Text style={styles.inactiveBannerText}>
                This field is inactive
                {field.inactiveReason ? `: ${field.inactiveReason}` : ''}
                {field.inactiveDate ? ` (${new Date(field.inactiveDate).toLocaleDateString()})` : ''}
              </Text>
            </View>
          )}

          <View style={styles.header}>
            <View style={[styles.statusContainer, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
              <View style={[
                styles.statusBadge,
                { backgroundColor: isInactive ? colors.gray[500] : getStatusColor(field.status) }
              ]}>
                <Text style={styles.statusText}>
                  {isInactive ? 'Inactive' : (field.status || 'Active')}
                </Text>
              </View>
            </View>

            <Text style={[styles.title, { textAlign: isRTL ? "right" : "left" }]}>{field.name}</Text>

            <View style={[styles.infoRow, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
              <View style={[styles.infoItem]}>
                <Text style={[styles.infoLabel, { textAlign: isRTL ? "right" : "left" }]}>{t('field.size')}:</Text>
                <Text style={[styles.infoValue, , { textAlign: isRTL ? "right" : "left", marginRight: isRTL ? 4 : 0 }]}>{field.size?.toFixed(2)} {t(`field.${field.sizeUnit}`)}</Text>
              </View>

              {field.cropType && (
                <View style={styles.infoItem}>
                  <Text style={[styles.infoLabel, { textAlign: isRTL ? "right" : "left" }]}>{t('field.crop')}</Text>
                  <Text style={[styles.infoValue, { textAlign: isRTL ? "right" : "left", marginRight: isRTL ? 4 : 0 }]}>{field?.cropType || "N/A"}</Text>
                </View>
              )}
            </View>
          </View>

          <View style={[styles.tabsContainer, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
            <TouchableOpacity
              style={[
                styles.tab,
                activeTab === 'details' && styles.activeTab
              ]}
              onPress={() => setActiveTab('details')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'details' && styles.activeTabText
              ]}>
                {t('field.details')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.tab,
                activeTab === 'crop' && styles.activeTab
              ]}
              onPress={() => setActiveTab('crop')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'crop' && styles.activeTabText
              ]}>
                {t('field.currentCrop')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.tab,
                activeTab === 'history' && styles.activeTab
              ]}
              onPress={() => setActiveTab('history')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'history' && styles.activeTabText
              ]}>
                {t('field.fieldHistory')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.tab,
                activeTab === 'tasks' && styles.activeTab
              ]}
              onPress={() => setActiveTab('tasks')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'tasks' && styles.activeTabText
              ]}>
                {t('field.fieldtasks')}
              </Text>
            </TouchableOpacity>
          </View>

          {activeTab === 'details' && (
            <>
              <View style={styles.detailsContainer}>
                <View style={[styles.detailItem, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                  <View style={styles.detailIcon}>
                    <Leaf size={20} color={colors.primary} />
                  </View>
                  <View style={[styles.detailContent]}>
                    <Text style={[styles.detailLabel, { textAlign: isRTL ? 'right' : 'left', marginRight: isRTL ? 8 : 0 }]}>{t('field.cropType')}</Text>
                    <Text style={[styles.detailValue, { textAlign: isRTL ? 'right' : 'left', marginRight: isRTL ? 8 : 0 }]}>{field.cropType || '--'}</Text>
                  </View>
                </View>

                <View style={[styles.detailItem, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                  <View style={styles.detailIcon}>
                    <Ruler size={20} color={colors.primary} />
                  </View>
                  <View style={styles.detailContent}>
                    <Text style={[styles.detailLabel, { textAlign: isRTL ? 'right' : 'left', marginRight: isRTL ? 8 : 0 }]}>{t('field.size')}</Text>
                    <Text style={[styles.detailValue, { textAlign: isRTL ? 'right' : 'left', marginRight: isRTL ? 8 : 0 }]}>{field.size?.toFixed(2)} {t(`common.areaUnit.${field.sizeUnit}`)}</Text>
                  </View>
                </View>

                <View style={[styles.detailItem, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                  <View style={styles.detailIcon}>
                    <MapPin size={20} color={colors.primary} />
                  </View>
                  <View style={styles.detailContent}>
                    <Text style={[styles.detailLabel, { textAlign: isRTL ? 'right' : 'left', marginRight: isRTL ? 8 : 0 }]}>{t('field.location')}</Text>
                    <Text style={[styles.detailValue, { textAlign: isRTL ? 'right' : 'left', marginRight: isRTL ? 8 : 0 }]}>Field Location</Text>
                  </View>
                </View>

                {field.health !== undefined && (
                  <View style={styles.healthContainer}>
                    <Text style={styles.healthTitle}>Field Health</Text>
                    <View style={styles.healthBarContainer}>
                      <View
                        style={[
                          styles.healthBar,
                          {
                            width: `${field.health}%`,
                            backgroundColor: field.health > 70
                              ? colors.success
                              : field.health > 40
                                ? colors.warning
                                : colors.danger
                          }
                        ]}
                      />
                    </View>
                    <Text style={styles.healthPercentage}>{field.health}%</Text>
                  </View>
                )}
              </View>

              {isInactive && (
                <View style={styles.inactiveDetailsContainer}>
                  <Text style={styles.inactiveDetailsTitle}>Inactive Details</Text>

                  <View style={styles.inactiveDetailsRow}>
                    <Text style={styles.inactiveDetailsLabel}>Reason:</Text>
                    <Text style={styles.inactiveDetailsValue}>{field.inactiveReason || 'Not specified'}</Text>
                  </View>

                  {field.inactiveDate && (
                    <View style={styles.inactiveDetailsRow}>
                      <Text style={styles.inactiveDetailsLabel}>Date:</Text>
                      <Text style={styles.inactiveDetailsValue}>
                        {new Date(field.inactiveDate).toLocaleDateString()}
                      </Text>
                    </View>
                  )}

                  {field.inactiveNotes && (
                    <View style={styles.inactiveDetailsRow}>
                      <Text style={styles.inactiveDetailsLabel}>Notes:</Text>
                      <Text style={styles.inactiveDetailsValue}>{field.inactiveNotes}</Text>
                    </View>
                  )}

                  {field.inactiveImage && (
                    <View style={styles.inactiveImageContainer}>
                      <Text style={styles.inactiveDetailsLabel}>Image:</Text>
                      <Image
                        source={{ uri: field.inactiveImage }}
                        style={styles.inactiveImage}
                        resizeMode="cover"
                      />
                    </View>
                  )}
                </View>
              )}

              <View style={[styles.quickActionsContainer,]}>
                <Text style={[styles.sectionTitle, { textAlign: isRTL ? 'right' : 'left', marginRight: isRTL ? 8 : 0 }]}>{t('common.quickActions')}</Text>
                <View style={styles.quickActions}>
                  <TouchableOpacity
                    style={[
                      styles.quickActionButton,
                      isInactive && styles.disabledActionButton,
                      { flexDirection: isRTL ? 'row-reverse' : 'row', }

                    ]}
                    onPress={() => router.push(`/task/create?id=${field.id}&entity=field`)}
                    disabled={isInactive}
                  >
                    <View style={[
                      styles.quickActionIcon,
                      { backgroundColor: isInactive ? colors.gray[400] : colors.warning }
                    ]}>
                      <Calendar size={20} color={colors.white} />
                    </View>
                    <Text style={[
                      styles.quickActionText,
                      isInactive && styles.disabledActionText,
                      { marginRight: isRTL ? 8 : 0 }

                    ]}>{t("tasks.createTask")}</Text>
                  </TouchableOpacity>
                  {field?.activeCropId ? (
                    <TouchableOpacity
                      style={[
                        styles.quickActionButton,
                        isInactive && styles.disabledActionButton,
                        { flexDirection: isRTL ? 'row-reverse' : 'row', }
                      ]}
                      onPress={() => router.push(`/field/harvest?id=${field.id}`)}
                      disabled={isInactive}
                    >
                      <View style={[
                        styles.quickActionIcon,
                        { backgroundColor: isInactive ? colors.gray[400] : colors.success }
                      ]}>
                        <Wheat size={20} color={colors.white} />
                      </View>
                      <Text style={[
                        styles.quickActionText,
                        isInactive && styles.disabledActionText,
                        { marginRight: isRTL ? 8 : 0 }
                      ]}>{t('crop.harvest')}</Text>
                    </TouchableOpacity>

                  ) : (
                    <TouchableOpacity
                      style={[
                        styles.quickActionButton,
                        isInactive && styles.disabledActionButton,
                        { flexDirection: isRTL ? 'row-reverse' : 'row', }
                      ]}
                      onPress={() => router.push(`/field/add-crop?id=${field.id}`)}
                      disabled={isInactive}
                    >
                      <View style={[
                        styles.quickActionIcon,
                        { backgroundColor: isInactive ? colors.gray[400] : colors.success }
                      ]}>
                        <Wheat size={20} color={colors.white} />
                      </View>
                      <Text style={[
                        styles.quickActionText,
                        isInactive && styles.disabledActionText,
                        { marginRight: isRTL ? 8 : 0 }
                      ]}>{t('crop.add')}</Text>
                    </TouchableOpacity>
                  )
                  }




                  <TouchableOpacity
                    style={[styles.quickActionButton, { flexDirection: isRTL ? 'row-reverse' : 'row', }]}
                    onPress={() => router.push({
                      pathname: '/field/create',
                      params: { editMode: true, id: field.id }
                    })}
                  >
                    <View style={[styles.quickActionIcon, { backgroundColor: colors.primary }]}>
                      <Edit size={20} color={colors.white} />
                    </View>
                    <Text style={styles.quickActionText}>{t('field.editField')}</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.quickActionButton, { flexDirection: isRTL ? 'row-reverse' : 'row', }]}
                    onPress={() => router.push(`/finance/create?id=${id}&entity=field`)}

                  >
                    <View style={[styles.quickActionIcon, { backgroundColor: colors.primary }]}>
                      <Edit size={20} color={colors.white} />
                    </View>
                    <Text style={[styles.quickActionText, { marginRight: isRTL ? 8 : 0 }]}>{t('finance.add')}</Text>
                  </TouchableOpacity>



                  {!isInactive && (
                    <TouchableOpacity
                      style={[styles.quickActionButton, { flexDirection: isRTL ? 'row-reverse' : 'row', }]}
                      onPress={() => setShowInactiveModal(true)}
                    >
                      <View style={[styles.quickActionIcon, { backgroundColor: colors.gray[500] }]}>
                        <Archive size={20} color={colors.white} />
                      </View>
                      <Text style={[styles.quickActionText, { marginRight: isRTL ? 8 : 0 }]}>{t('field.markInactive')}</Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            </>
          )}

          {activeTab === 'crop' && renderCurrentCropTab()}

          {activeTab === 'history' && renderCropHistoryTab()}

          {activeTab === 'tasks' && renderCropTasks()}

          {activeTab === 'reports' && (
            <View style={styles.reportsContainer}>
              <View style={styles.reportCard}>
                <View style={styles.reportHeader}>
                  <Text style={styles.reportTitle}>Crop Yield</Text>
                  <BarChart3 size={24} color={colors.primary} />
                </View>

                <View style={styles.chartPlaceholder}>
                  <View style={styles.chartBars}>
                    <View style={[styles.chartBar, { height: 60 }]} />
                    <View style={[styles.chartBar, { height: 100 }]} />
                    <View style={[styles.chartBar, { height: 80 }]} />
                    <View style={[styles.chartBar, { height: 120 }]} />
                  </View>
                </View>
              </View>

              <View style={styles.reportCard}>
                <View style={styles.reportHeader}>
                  <Text style={styles.reportTitle}>Growth Progress</Text>
                  <Text style={styles.reportValue}>75%</Text>
                </View>

                <View style={styles.progressBarContainer}>
                  <View style={[styles.progressBar, { width: '75%' }]} />
                </View>
              </View>

              <View style={styles.relatedItemsContainer}>
                <Text style={styles.relatedItemsTitle}>Related Fields</Text>

                <View style={styles.relatedItemsGrid}>
                  <TouchableOpacity style={styles.relatedItem}>
                    <Image
                      source={{ uri: 'https://images.unsplash.com/photo-1471194402529-8e0f5a675de6?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80' }}
                      style={styles.relatedItemImage}
                      resizeMode="cover"
                    />
                    <Text style={styles.relatedItemTitle}>Corn Field B</Text>
                    <Text style={styles.relatedItemSubtitle}>Adjacent</Text>
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.relatedItem}>
                    <Image
                      source={{ uri: 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80' }}
                      style={styles.relatedItemImage}
                      resizeMode="cover"
                    />
                    <Text style={styles.relatedItemTitle}>Wheat Field C</Text>
                    <Text style={styles.relatedItemSubtitle}>Similar crop</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          )}
        </ScrollView>
      </SafeAreaView>
      {/* Inactive Status Modal */}
      <InactiveStatusModal
        visible={showInactiveModal}
        onClose={() => setShowInactiveModal(false)}
        onSubmit={handleMarkInactive}
        entityType="field"
        showCascadeOption={true}
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  headerButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerButtons: {
    flexDirection: 'row',
  },
  optionsMenu: {
    position: 'absolute',
    top: 0,
    right: 16,
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 8,
    zIndex: 10,
    shadowColor: colors.gray[800],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  optionText: {
    fontSize: 14,
    color: colors.gray[800],
    marginLeft: 8,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  notFoundContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  notFoundText: {
    fontSize: 18,
    color: colors.gray[600],
    marginBottom: 20,
  },
  backButton: {
    width: 120,
  },
  headerImage: {
    width: '100%',
    height: 200,
  },
  header: {
    padding: 16,
  },
  statusContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.gray[800],
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
  },
  infoItem: {
    marginRight: 24,
  },
  infoLabel: {
    fontSize: 12,
    color: colors.gray[500],
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
  },
  tabsContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
    marginBottom: 16,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  tabText: {
    fontSize: 14,
    color: colors.gray[600],
  },
  activeTabText: {
    color: colors.primary,
    fontWeight: '600',
  },
  detailsContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 24,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  detailIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  detailContent: {
    flex: 1,
  },
  quickActionsContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionButton: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  quickActionIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
  },
  detailLabel: {
    fontSize: 12,
    color: colors.gray[500],
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
  },
  healthContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  healthTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  healthBarContainer: {
    height: 8,
    backgroundColor: colors.gray[200],
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8,
  },
  healthBar: {
    height: '100%',
    borderRadius: 4,
  },
  healthPercentage: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
    textAlign: 'right',
  },
  tasksContainer: {
    paddingHorizontal: 16,
  },
  taskItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  taskPriorityIndicator: {
    width: 4,
    height: '80%',
    borderRadius: 2,
    marginRight: 12,
  },
  taskContent: {
    flex: 1,
  },
  taskTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
    marginBottom: 4,
  },
  taskDate: {
    fontSize: 12,
    color: colors.gray[500],
  },
  taskStatusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  taskStatusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
    backgroundColor: colors.white,
    borderRadius: 12,
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 14,
    color: colors.gray[500],
  },
  addTaskButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.secondary,
    // color: colors.secondary,
    borderRadius: 8,
    paddingVertical: 12,
    marginBottom: 24,
    marginTop: 12,
  },
  addTaskText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.white,
    marginLeft: 8,
  },
  reportsContainer: {
    paddingHorizontal: 16,
  },
  reportCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  reportHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  reportTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
  },
  reportValue: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.primary,
  },
  chartPlaceholder: {
    height: 150,
    justifyContent: 'flex-end',
  },
  chartBars: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'flex-end',
  },
  chartBar: {
    width: 30,
    backgroundColor: colors.primary,
    borderRadius: 4,
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: colors.gray[200],
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 4,
  },
  relatedItemsContainer: {
    marginTop: 8,
    marginBottom: 24,
  },
  relatedItemsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 16,
  },
  relatedItemsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -8,
  },
  relatedItem: {
    width: '50%',
    paddingHorizontal: 8,
    marginBottom: 16,
  },
  relatedItemImage: {
    width: '100%',
    height: 100,
    borderRadius: 8,
    marginBottom: 8,
  },
  relatedItemTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
    marginBottom: 2,
  },
  relatedItemSubtitle: {
    fontSize: 12,
    color: colors.gray[500],
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 1,
  },
  harvestButton: {
    backgroundColor: colors.success,
    marginRight: 8,
  },
  harvestTaskButton: {
    backgroundColor: colors.secondary,
    marginRight: 8,
    marginTop: 8
  },
  editButton: {
    backgroundColor: colors.primary,
    marginLeft: 8,
  },
  actionButtonIcon: {
    marginRight: 8,
  },
  actionButtonText: {
    color: colors.white,
    fontWeight: '500',
  },
  inactiveBanner: {
    backgroundColor: colors.gray[700],
    padding: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  inactiveBannerText: {
    color: colors.white,
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  inactiveDetailsContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.gray[500],
  },
  inactiveDetailsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  inactiveDetailsRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  inactiveDetailsLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    width: 80,
  },
  inactiveDetailsValue: {
    fontSize: 14,
    color: colors.gray[800],
    flex: 1,
  },
  inactiveImageContainer: {
    marginTop: 8,
  },
  inactiveImage: {
    width: '100%',
    height: 150,
    borderRadius: 8,
    marginTop: 8,
  },
  disabledActionButton: {
    opacity: 0.6,
  },
  disabledActionText: {
    color: colors.gray[500],
  },
  tabContent: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  cropDetailsContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cropHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  cropTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cropIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  cropTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  cropImage: {
    width: '100%',
    height: 200,
  },
  cropInfoContainer: {
    padding: 16,
  },
  cropInfoRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  cropInfoItem: {
    flex: 1,
  },
  cropInfoLabel: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 4,
  },
  cropInfoValue: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
  },
  notesContainer: {
    marginTop: 8,
  },
  notesLabel: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 4,
  },
  notesText: {
    fontSize: 16,
    color: colors.text,
    lineHeight: 22,
  },
  cropActionsContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  // harvestButton: {
  //   marginBottom: 12,
  // },
  // addTaskButton: {
  //   backgroundColor: colors.white,
  //   borderWidth: 1,
  //   borderColor: colors.primary,
  // },
  emptyCropContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyCropText: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
    marginVertical: 16,
  },
  addCropButton: {
    marginTop: 16,
  },
  historyHeader: {
    marginBottom: 16,
  },
  historyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
  },
  historySubtitle: {
    fontSize: 14,
    color: colors.gray[600],
  },
  timelineContainer: {
    paddingLeft: 8,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  timelineLeft: {
    width: 24,
    alignItems: 'center',
  },
  timelineDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: colors.primary,
    marginTop: 0,
  },
  timelineLine: {
    width: 2,
    flex: 1,
    backgroundColor: colors.gray[300],
    marginTop: 2,
  },
  timelineCard: {
    flex: 1,
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginLeft: 12,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  timelineCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  timelineCropType: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  timelineStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  timelineStatusText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.white,
  },
  timelineDates: {
    marginBottom: 12,
  },
  timelineDateItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  timelineDateText: {
    fontSize: 14,
    color: colors.gray[700],
    marginLeft: 8,
  },
  timelineImage: {
    width: '100%',
    height: 120,
    borderRadius: 8,
    marginTop: 8,
  },
  emptyHistoryContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyHistoryText: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
    marginTop: 16,
  },
  card: {
    backgroundColor: colors.white,
    padding: 12,
    borderRadius: 12,
    marginVertical: 8,
    shadowColor: colors.black,
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  headers: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  cropName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
    color: colors.text,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 2,
  },
  text: {
    marginLeft: 6,
    color: colors.gray[700],
    fontSize: 14,
  },
  textRTL: {
    textAlign: 'right',
  },
  imageSliderContainer: {
    marginBottom: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
    paddingHorizontal: 4,
  },
  statCard: {
    flex: 1,
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 4,
    alignItems: 'center',
    shadowColor: colors.gray[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.gray[600],
    textAlign: 'center',
  },
});









