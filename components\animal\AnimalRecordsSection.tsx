import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';
import { useFarmStore } from '@/store/farm-store';
import { AnimalRecord } from '@/types';
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { normalizeDate } from '@/utils/dateUtils';

interface AnimalRecordsSectionProps {
  animalId: string;
  isEditable?: boolean;
}

export const AnimalRecordsSection: React.FC<AnimalRecordsSectionProps> = ({
  animalId,
  isEditable = true,
}) => {
  const { t, isRTL } = useTranslation();
  const { fetchAnimalRecords, animalRecords } = useFarmStore();
  const [loading, setLoading] = useState(false);
  const [records, setRecords] = useState<AnimalRecord[]>([]);

  useEffect(() => {
    loadRecords();
  }, [animalId]);

  const loadRecords = async () => {
    try {
      setLoading(true);
      const recordData = await fetchAnimalRecords(animalId);
      setRecords(recordData);
    } catch (error) {
      console.error('Error loading records:', error);
      Alert.alert(t('common.error'), t('animal.records.loadError'));
    } finally {
      setLoading(false);
    }
  };

  const getRecordIcon = (type: string) => {
    switch (type) {
      case 'feeding':
        return 'restaurant';
      case 'grooming':
        return 'brush';
      case 'exercise':
        return 'walk';
      case 'behavior':
        return 'eye';
      case 'maintenance':
        return 'build';
      default:
        return 'document-text';
    }
  };

  const getRecordColor = (type: string) => {
    switch (type) {
      case 'feeding':
        return colors.success;
      case 'grooming':
        return colors.primary;
      case 'exercise':
        return colors.warning;
      case 'behavior':
        return colors.secondary;
      case 'maintenance':
        return colors.gray[600];
      default:
        return colors.gray[500];
    }
  };

  const renderRecordItem = ({ item }: { item: AnimalRecord }) => (
    <View style={[styles.recordCard, isRTL && styles.recordCardRTL]}>
      <View style={[styles.recordHeader, isRTL && styles.recordHeaderRTL]}>
        <View style={[styles.recordIconContainer, isRTL && styles.recordIconContainerRTL]}>
          <Ionicons
            name={getRecordIcon(item.type) as any}
            size={20}
            color={getRecordColor(item.type)}
          />
          <Text style={[styles.recordTitle, isRTL && styles.textRTL]}>
            {item.title}
          </Text>
        </View>
        <Text style={[styles.recordDate, isRTL && styles.textRTL]}>
          {normalizeDate(new Date(item.date))}
        </Text>
      </View>

      <View style={[styles.recordTypeContainer, isRTL && styles.recordTypeContainerRTL]}>
        <View style={[styles.typeBadge, { backgroundColor: getRecordColor(item.type) + '20' }]}>
          <Text style={[styles.typeText, { color: getRecordColor(item.type) }, isRTL && styles.textRTL]}>
            {t(`animal.records.type.${item.type}`)}
          </Text>
        </View>
      </View>

      {item.description && (
        <Text style={[styles.recordDescription, isRTL && styles.textRTL]}>
          {item.description}
        </Text>
      )}

      <View style={[styles.recordDetails, isRTL && styles.recordDetailsRTL]}>
        {item.duration && (
          <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
            <Ionicons name="time" size={16} color={colors.gray[600]} />
            <Text style={[styles.detailText, isRTL && styles.textRTL]}>
              {item.duration} {t('common.minutes')}
            </Text>
          </View>
        )}

        {item.quantity && (
          <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
            <Ionicons name="scale" size={16} color={colors.gray[600]} />
            <Text style={[styles.detailText, isRTL && styles.textRTL]}>
              {item.quantity} {item.unit || ''}
            </Text>
          </View>
        )}

        {item.performedBy && (
          <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
            <Ionicons name="person" size={16} color={colors.gray[600]} />
            <Text style={[styles.detailText, isRTL && styles.textRTL]}>
              {item.performedBy}
            </Text>
          </View>
        )}

        {item.cost && (
          <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
            <Ionicons name="cash" size={16} color={colors.gray[600]} />
            <Text style={[styles.detailText, isRTL && styles.textRTL]}>
              ${item.cost}
            </Text>
          </View>
        )}
      </View>

      {item.notes && (
        <Text style={[styles.recordNotes, isRTL && styles.textRTL]}>
          {item.notes}
        </Text>
      )}
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, isRTL && styles.textRTL]}>
          {t('animal.records.loading')}
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={[styles.header, isRTL && styles.headerRTL]}>
        <Text style={[styles.sectionTitle, isRTL && styles.textRTL]}>
          {t('animal.records.title')}
        </Text>
        {isEditable && (
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => {
              // TODO: Navigate to add record screen
              Alert.alert(t('common.info'), 'Add record functionality coming soon');
            }}
          >
            <Ionicons name="add" size={20} color={colors.primary} />
          </TouchableOpacity>
        )}
      </View>

      {records.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="document-text" size={48} color={colors.gray[400]} />
          <Text style={[styles.emptyText, isRTL && styles.textRTL]}>
            {t('animal.records.noRecords')}
          </Text>
          {isEditable && (
            <TouchableOpacity
              style={styles.emptyAddButton}
              onPress={() => {
                // TODO: Navigate to add record screen
                Alert.alert(t('common.info'), 'Add record functionality coming soon');
              }}
            >
              <Text style={styles.emptyAddButtonText}>
                {t('animal.records.addFirst')}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      ) : (
        <FlatList
          data={records}
          renderItem={renderRecordItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerRTL: {
    flexDirection: 'row-reverse',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.primary,
  },
  addButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: colors.primary + '20',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.gray[600],
  },
  listContainer: {
    paddingBottom: 16,
  },
  recordCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  recordCardRTL: {
    alignItems: 'flex-end',
  },
  recordHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  recordHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  recordIconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  recordIconContainerRTL: {
    flexDirection: 'row-reverse',
  },
  recordTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.primary,
    marginLeft: 8,
  },
  recordDate: {
    fontSize: 14,
    color: colors.gray[600],
  },
  recordTypeContainer: {
    marginBottom: 8,
  },
  recordTypeContainerRTL: {
    alignItems: 'flex-end',
  },
  typeBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  typeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  recordDescription: {
    fontSize: 14,
    color: colors.secondary,
    marginBottom: 12,
    lineHeight: 20,
  },
  recordDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  recordDetailsRTL: {
    flexDirection: 'row-reverse',
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 4,
  },
  detailRowRTL: {
    flexDirection: 'row-reverse',
    marginRight: 0,
    marginLeft: 16,
  },
  detailText: {
    fontSize: 12,
    color: colors.gray[600],
    marginLeft: 4,
  },
  recordNotes: {
    fontSize: 12,
    color: colors.gray[600],
    fontStyle: 'italic',
    marginTop: 8,
    lineHeight: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  emptyAddButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyAddButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  textRTL: {
    textAlign: 'right',
  },
});
