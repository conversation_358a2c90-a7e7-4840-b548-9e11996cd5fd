import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Image, 
  ScrollView, 
  KeyboardAvoidingView, 
  Platform,
  ActivityIndicator,
  I18nManager,
} from 'react-native';
import { Stack, Link, router } from 'expo-router';
import { colors } from '@/constants/colors';
import { useAuthStore } from '@/store/auth-store';
import Input from '@/components/Input';
import Button from '@/components/Button';
import { Eye, EyeOff, Mail, Lock, Globe } from 'lucide-react-native';
import { useTranslation } from '@/i18n/useTranslation';

export default function LoginScreen() {
  const { t, locale, setLocale } = useTranslation();
  const { signIn, isLoading, error, clearError, setLanguage } = useAuthStore();
  
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  
  // Clear any errors when component mounts
  useEffect(() => {
    // Only call clearError if it exists
    if (typeof clearError === 'function') {
      clearError();
    }
  }, [clearError]);
  
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setEmailError(t('login.emailRequired'));
      return false;
    } else if (!emailRegex.test(email)) {
      setEmailError(t('login.invalidEmail'));
      return false;
    }
    setEmailError('');
    return true;
  };
  
  const validatePassword = (password: string) => {
    if (!password) {
      setPasswordError(t('login.passwordRequired'));
      return false;
    } else if (password.length < 6) {
      setPasswordError(t('login.passwordTooShort'));
      return false;
    }
    setPasswordError('');
    return true;
  };
  
  const handleLogin = async () => {
    // Only call clearError if it exists
    if (typeof clearError === 'function') {
      clearError();
    }
    
    const isEmailValid = validateEmail(email.trim());
    const isPasswordValid = validatePassword(password);
    
    if (!isEmailValid || !isPasswordValid) {
      return;
    }
    
    try {
      await signIn(email.trim(), password);
    } catch (error) {
      console.error('Login error:', error);
    }
  };
  
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  
  const handleChangeLanguage = async () => {
    try {
      const newLocale = locale === 'en' ? 'ur' : 'en';
      // Update both the i18n system and the auth store
      await setLocale(newLocale);
      await setLanguage(newLocale);
    } catch (error) {
      console.error('Language change error:', error);
    }
  };
  
  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <Stack.Screen 
        options={{ 
          headerShown: false,
        }} 
      />
      
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.header}>
          <Image
            source={require('@/assets/images/icon.png')}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={styles.appName}>Kissan Dost</Text>
          <Text style={styles.tagline}>{t('login.tagline')}</Text>
        </View>
        
        <View style={styles.formContainer}>
          <Text style={styles.title}>{t('login.welcomeBack')}</Text>
          <Text style={styles.subtitle}>{t('login.signInToContinue')}</Text>
          
          {error ? (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
            </View>
          ) : null}
          
          <Input
            label={t('login.email')}
            placeholder={t('login.enterEmail')}
            value={email}
            onChangeText={(text) => {
              setEmail(text);
              if (emailError) validateEmail(text);
            }}
            keyboardType="email-address"
            autoCapitalize="none"
            error={emailError}
            leftIcon={<Mail size={20} color={colors.gray[500]} />}
          />
          
          <Input
            label={t('login.password')}
            placeholder={t('login.enterPassword')}
            value={password}
            onChangeText={(text) => {
              setPassword(text);
              if (passwordError) validatePassword(text);
            }}
            secureTextEntry={!showPassword}
            error={passwordError}
            leftIcon={<Lock size={20} color={colors.gray[500]} />}
            rightIcon={
              <TouchableOpacity onPress={togglePasswordVisibility}>
                {showPassword ? (
                  <EyeOff size={20} color={colors.gray[500]} />
                ) : (
                  <Eye size={20} color={colors.gray[500]} />
                )}
              </TouchableOpacity>
            }
          />
          
          <Link href="/forgot-password" asChild>
            <TouchableOpacity style={styles.forgotPasswordContainer}>
              <Text style={styles.forgotPasswordText}>{t('login.forgotPassword')}</Text>
            </TouchableOpacity>
          </Link>
          
          <Button
            title={isLoading ? t('login.signingIn') : t('login.signIn')}
            onPress={handleLogin}
            disabled={isLoading}
            leftIcon={isLoading ? <ActivityIndicator size="small" color={colors.white} /> : undefined}
          />
          
          <View style={styles.registerContainer}>
            <Text style={styles.registerText}>{t('login.dontHaveAccount')}</Text>
            <Link href="/register" asChild>
              <TouchableOpacity>
                <Text style={styles.registerLink}>{t('login.signUp')}</Text>
              </TouchableOpacity>
            </Link>
          </View>
          
          <TouchableOpacity 
            style={styles.languageButton}
            onPress={handleChangeLanguage}
          >
            <Globe size={16} color={colors.primary} style={styles.languageIcon} />
            <Text style={styles.languageText}>
              {locale === 'en' ? 'اردو میں دیکھیں' : 'View in English'}
            </Text>
          </TouchableOpacity>
          
          {/* Demo Login Buttons 
          <View style={styles.demoContainer}>
            <Text style={styles.demoTitle}>{t('login.demoAccounts')}</Text>
            
            <TouchableOpacity 
              style={styles.demoButton}
              onPress={() => {
                setEmail('<EMAIL>');
                setPassword('password123');
              }}
            >
              <Text style={styles.demoButtonText}>{t('login.adminDemo')}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.demoButton}
              onPress={() => {
                setEmail('<EMAIL>');
                setPassword('password123');
              }}
            >
              <Text style={styles.demoButtonText}>{t('login.farmerDemo')}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.demoButton}
              onPress={() => {
                setEmail('<EMAIL>');
                setPassword('password123');
              }}
            >
              <Text style={styles.demoButtonText}>{t('login.workerDemo')}</Text>
            </TouchableOpacity>
          </View>*/}
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 40,
  },
  logo: {
    width: 80,
    height: 80,
    marginBottom: 16,
  },
  appName: {
    fontSize: 28,
    fontWeight: '700',
    color: colors.primary,
    marginBottom: 8,
  },
  tagline: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
    paddingHorizontal: 40,
  },
  formContainer: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    paddingHorizontal: 24,
    paddingTop: 32,
    paddingBottom: 40,
    flex: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.gray[800],
    marginBottom: 8,
    textAlign: Platform.OS === 'ios' ? (I18nManager.isRTL ? 'right' : 'left') : 'auto',
  },
  subtitle: {
    fontSize: 16,
    color: colors.gray[600],
    marginBottom: 24,
    textAlign: Platform.OS === 'ios' ? (I18nManager.isRTL ? 'right' : 'left') : 'auto',
  },
  errorContainer: {
    backgroundColor: colors.danger + '20',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: colors.danger,
  },
  errorText: {
    color: colors.danger,
    fontSize: 14,
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  forgotPasswordContainer: {
    alignSelf: I18nManager.isRTL ? 'flex-start' : 'flex-end',
    marginBottom: 24,
  },
  forgotPasswordText: {
    color: colors.primary,
    fontSize: 14,
  },
  registerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 24,
  },
  registerText: {
    color: colors.gray[600],
    fontSize: 14,
    marginRight: 4,
  },
  registerLink: {
    color: colors.primary,
    fontSize: 14,
    fontWeight: '500',
  },
  languageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 24,
    padding: 8,
  },
  languageIcon: {
    marginRight: 8,
  },
  languageText: {
    color: colors.primary,
    fontSize: 14,
    fontWeight: '500',
  },
  demoContainer: {
    marginTop: 32,
    backgroundColor: colors.gray[100],
    borderRadius: 12,
    padding: 16,
  },
  demoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[700],
    marginBottom: 12,
    textAlign: 'center',
  },
  demoButton: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  demoButtonText: {
    color: colors.gray[700],
    fontSize: 14,
    textAlign: 'center',
    fontWeight: '500',
  },
});