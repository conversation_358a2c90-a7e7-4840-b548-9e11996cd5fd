import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';
import { EntityReferenceCard } from './EntityReferenceCard';
import { EntityListingSection } from './EntityListingSection';
import { EntityReference, EntityListing } from '@/services/ai-assistant';

// Demo data to show how the enhanced AI assistant works
const demoEntityReferences: EntityReference[] = [
  {
    id: 'animal-001',
    type: 'animal',
    name: 'Holstein Cow #1',
    summary: 'Healthy dairy cow, 3 years old, excellent milk production',
    image: undefined,
    status: 'healthy',
    key_info: {
      species: 'Cattle',
      breed: 'Holstein',
      age: '3 years'
    }
  },
  {
    id: 'plant-001',
    type: 'plant',
    name: 'Tomato Plant - Roma Variety',
    summary: 'Growing well, planted 2 months ago, ready for harvest soon',
    image: undefined,
    status: 'flowering',
    key_info: {
      species: 'Tomato',
      variety: 'Roma',
      health: 'Good'
    }
  }
];

const demoEntityListing: EntityListing = {
  type: 'animal',
  title: 'Your Animals',
  entities: [
    {
      id: 'animal-001',
      type: 'animal',
      name: 'Holstein Cow #1',
      summary: 'Healthy dairy cow, excellent milk production',
      status: 'healthy',
      key_info: { species: 'Cattle', breed: 'Holstein' }
    },
    {
      id: 'animal-002',
      type: 'animal',
      name: 'Jersey Cow #2',
      summary: 'Young cow, good health, recently calved',
      status: 'healthy',
      key_info: { species: 'Cattle', breed: 'Jersey' }
    },
    {
      id: 'animal-003',
      type: 'animal',
      name: 'Goat #1',
      summary: 'Beetal goat, good for milk production',
      status: 'healthy',
      key_info: { species: 'Goat', breed: 'Beetal' }
    }
  ],
  total_count: 15,
  show_all_link: true
};

interface AIAssistantDemoProps {
  style?: any;
}

export const AIAssistantDemo: React.FC<AIAssistantDemoProps> = ({ style }) => {
  const { t, isRTL } = useTranslation();

  return (
    <ScrollView style={[styles.container, style]}>
      <View style={styles.content}>
        <Text style={[styles.title, isRTL && styles.textRtl]}>
          Enhanced AI Assistant Demo
        </Text>
        
        <Text style={[styles.description, isRTL && styles.textRtl]}>
          The AI assistant can now show interactive entity data to help uneducated users easily browse and access their farm information.
        </Text>

        {/* Demo Message Container */}
        <View style={styles.messageContainer}>
          <Text style={styles.messageText}>
            Here are your farm animals that need attention:
          </Text>

          {/* Entity References Demo */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, isRTL && styles.textRtl]}>
              Entity References (Specific Items)
            </Text>
            {demoEntityReferences.map((entity) => (
              <EntityReferenceCard
                key={entity.id}
                entity={entity}
                style={styles.entityCard}
              />
            ))}
          </View>

          {/* Entity Listing Demo */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, isRTL && styles.textRtl]}>
              Entity Listing (Browse All)
            </Text>
            <EntityListingSection
              listing={demoEntityListing}
              style={styles.entityListing}
            />
          </View>
        </View>

        <View style={styles.features}>
          <Text style={[styles.featuresTitle, isRTL && styles.textRtl]}>
            New Features:
          </Text>
          <Text style={[styles.featureItem, isRTL && styles.textRtl]}>
            • Clickable entity cards that navigate to detail screens
          </Text>
          <Text style={[styles.featureItem, isRTL && styles.textRtl]}>
            • Horizontal scrollable lists for browsing multiple items
          </Text>
          <Text style={[styles.featureItem, isRTL && styles.textRtl]}>
            • "Show All" links to navigate to full listing screens
          </Text>
          <Text style={[styles.featureItem, isRTL && styles.textRtl]}>
            • Visual status indicators and key information display
          </Text>
          <Text style={[styles.featureItem, isRTL && styles.textRtl]}>
            • Support for both English and Urdu languages
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.gray[800],
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: colors.gray[600],
    lineHeight: 22,
    marginBottom: 20,
  },
  messageContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  messageText: {
    fontSize: 16,
    color: colors.gray[800],
    marginBottom: 16,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.primary,
    marginBottom: 12,
  },
  entityCard: {
    marginVertical: 4,
  },
  entityListing: {
    marginVertical: 0,
  },
  features: {
    backgroundColor: colors.primary + '10',
    borderRadius: 12,
    padding: 16,
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.primary,
    marginBottom: 12,
  },
  featureItem: {
    fontSize: 14,
    color: colors.gray[700],
    marginBottom: 6,
    lineHeight: 20,
  },
  textRtl: {
    textAlign: 'right',
  },
});

export default AIAssistantDemo;
