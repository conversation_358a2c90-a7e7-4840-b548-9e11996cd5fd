import React, { useState } from 'react';
import { TouchableOpacity, Text, Alert, ActivityIndicator } from 'react-native';
import { mcpServerService } from '@/services/mcp-server';
import { colors } from '@/constants/colors';
import { StyleSheet } from 'react-native';

export default function MCPTestButton() {
  const [isLoading, setIsLoading] = useState(false);

  const testMCPConnection = async () => {
    setIsLoading(true);
    try {
      // Test connection first
      const isConnected = await mcpServerService.testConnection();
      
      if (!isConnected) {
        Alert.alert('Connection Failed', 'Could not connect to MCP server');
        return;
      }

      // Test analyze message
      const result = await mcpServerService.analyzeMessage({
        message: "I want to add a plant",
        context: {
          farmId: 'test-farm',
          userId: 'test-user',
          language: 'en',
        }
      });

      Alert.alert(
        'MCP Test Success',
        `Summary: ${result.summary}\nActions: ${result.suggestedActionArray.length}`,
        [{ text: 'OK' }]
      );

      console.log('MCP Test Result:', result);
    } catch (error) {
      console.error('MCP Test Error:', error);
      Alert.alert(
        'MCP Test Failed', 
        error instanceof Error ? error.message : 'Unknown error'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <TouchableOpacity 
      style={styles.testButton} 
      onPress={testMCPConnection}
      disabled={isLoading}
    >
      {isLoading ? (
        <ActivityIndicator size="small" color={colors.white} />
      ) : (
        <Text style={styles.testButtonText}>Test MCP</Text>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  testButton: {
    backgroundColor: colors.secondary || colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 80,
  },
  testButtonText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: '600',
  },
});
