import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';
import { useFarmStore } from '@/store/farm-store';
import { AnimalPregnancy } from '@/types';
import { Ionicons } from '@expo/vector-icons';
// import { format, differenceInDays } from 'date-fns';
import { differenceInDays, normalizeDate } from '@/utils/dateUtils';
// import { format, differenceInDays } from 'date-fns';

interface AnimalPregnanciesSectionProps {
  animalId: string;
  isEditable?: boolean;
}

export const AnimalPregnanciesSection: React.FC<AnimalPregnanciesSectionProps> = ({
  animalId,
  isEditable = true,
}) => {
  const { t, isRTL } = useTranslation();
  const { fetchAnimalPregnancies, animalPregnancies } = useFarmStore();
  const [loading, setLoading] = useState(false);
  const [pregnancies, setPregnancies] = useState<AnimalPregnancy[]>([]);

  useEffect(() => {
    loadPregnancies();
  }, [animalId]);

  const loadPregnancies = async () => {
    try {
      setLoading(true);
      const pregnancyData = await fetchAnimalPregnancies(animalId);
      setPregnancies(pregnancyData);
    } catch (error) {
      console.error('Error loading pregnancies:', error);
      Alert.alert(t('common.error'), t('animal.pregnancies.loadError'));
    } finally {
      setLoading(false);
    }
  };

  const getOutcomeIcon = (outcome: string) => {
    switch (outcome) {
      case 'ongoing':
        return 'time';
      case 'successful':
        return 'checkmark-circle';
      case 'miscarriage':
      case 'stillborn':
        return 'close-circle';
      default:
        return 'help-circle';
    }
  };

  const getOutcomeColor = (outcome: string) => {
    switch (outcome) {
      case 'ongoing':
        return colors.warning;
      case 'successful':
        return colors.success;
      case 'miscarriage':
      case 'stillborn':
        return colors.danger;
      default:
        return colors.gray[500];
    }
  };

  const getDaysRemaining = (expectedDueDate: string, outcome: string) => {
    if (outcome !== 'ongoing') return null;
    
    const today = new Date();
    const dueDate = new Date(expectedDueDate);
    const daysRemaining = differenceInDays(dueDate, today);
    
    return daysRemaining;
  };

  const renderPregnancyItem = ({ item }: { item: AnimalPregnancy }) => {
    const daysRemaining = getDaysRemaining(item.expectedDueDate, item.outcome);
    
    return (
      <View style={[styles.pregnancyCard, isRTL && styles.pregnancyCardRTL]}>
        <View style={[styles.pregnancyHeader, isRTL && styles.pregnancyHeaderRTL]}>
          <View style={[styles.pregnancyIconContainer, isRTL && styles.pregnancyIconContainerRTL]}>
            <Ionicons
              name={getOutcomeIcon(item.outcome) as any}
              size={20}
              color={getOutcomeColor(item.outcome)}
            />
            <Text style={[styles.pregnancyOutcome, isRTL && styles.textRTL]}>
              {t(`animal.pregnancies.outcome.${item.outcome}`)}
            </Text>
          </View>
          <Text style={[styles.pregnancyDate, isRTL && styles.textRTL]}>
            {normalizeDate(new Date(item.matingDate))}
          </Text>
        </View>

        <View style={[styles.pregnancyDetails, isRTL && styles.pregnancyDetailsRTL]}>
          <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
            <Ionicons name="calendar" size={16} color={colors.gray[600]} />
            <Text style={[styles.detailLabel, isRTL && styles.textRTL]}>
              {t('animal.pregnancies.expectedDue')}:
            </Text>
            <Text style={[styles.detailValue, isRTL && styles.textRTL]}>
              {normalizeDate(new Date(item.expectedDueDate))}
            </Text>
          </View>

          {item.outcome === 'ongoing' && daysRemaining !== null && (
            <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
              <Ionicons name="time" size={16} color={colors.warning} />
              <Text style={[styles.detailLabel, isRTL && styles.textRTL]}>
                {t('animal.pregnancies.daysRemaining')}:
              </Text>
              <Text style={[styles.detailValue, { color: daysRemaining < 0 ? colors.danger : colors.warning }, isRTL && styles.textRTL]}>
                {daysRemaining < 0 ? `${Math.abs(daysRemaining)} ${t('animal.pregnancies.overdue')}` : `${daysRemaining} ${t('common.days')}`}
              </Text>
            </View>
          )}

          {item.actualDueDate && (
            <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
              <Ionicons name="checkmark-circle" size={16} color={colors.success} />
              <Text style={[styles.detailLabel, isRTL && styles.textRTL]}>
                {t('animal.pregnancies.actualDue')}:
              </Text>
              <Text style={[styles.detailValue, isRTL && styles.textRTL]}>
                {normalizeDate(new Date(item.actualDueDate))}
              </Text>
            </View>
          )}

          {item.numberOfOffspring && (
            <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
              <Ionicons name="people" size={16} color={colors.gray[600]} />
              <Text style={[styles.detailLabel, isRTL && styles.textRTL]}>
                {t('animal.pregnancies.offspring')}:
              </Text>
              <Text style={[styles.detailValue, isRTL && styles.textRTL]}>
                {item.numberOfOffspring}
              </Text>
            </View>
          )}

          {item.veterinarian && (
            <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
              <Ionicons name="person" size={16} color={colors.gray[600]} />
              <Text style={[styles.detailLabel, isRTL && styles.textRTL]}>
                {t('animal.pregnancies.veterinarian')}:
              </Text>
              <Text style={[styles.detailValue, isRTL && styles.textRTL]}>
                {item.veterinarian}
              </Text>
            </View>
          )}

          {item.cost && (
            <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
              <Ionicons name="cash" size={16} color={colors.gray[600]} />
              <Text style={[styles.detailLabel, isRTL && styles.textRTL]}>
                {t('animal.pregnancies.cost')}:
              </Text>
              <Text style={[styles.detailValue, isRTL && styles.textRTL]}>
                ${item.cost}
              </Text>
            </View>
          )}
        </View>

        {item.complications && (
          <View style={styles.complicationsContainer}>
            <Text style={[styles.complicationsTitle, isRTL && styles.textRTL]}>
              {t('animal.pregnancies.complications')}:
            </Text>
            <Text style={[styles.complicationsText, isRTL && styles.textRTL]}>
              {item.complications}
            </Text>
          </View>
        )}

        {item.notes && (
          <Text style={[styles.pregnancyNotes, isRTL && styles.textRTL]}>
            {item.notes}
          </Text>
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, isRTL && styles.textRTL]}>
          {t('animal.pregnancies.loading')}
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={[styles.header, isRTL && styles.headerRTL]}>
        <Text style={[styles.sectionTitle, isRTL && styles.textRTL]}>
          {t('animal.pregnancies.title')}
        </Text>
        {isEditable && (
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => {
              // TODO: Navigate to add pregnancy screen
              Alert.alert(t('common.info'), 'Add pregnancy functionality coming soon');
            }}
          >
            <Ionicons name="add" size={20} color={colors.primary} />
          </TouchableOpacity>
        )}
      </View>

      {pregnancies.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="heart" size={48} color={colors.gray[400]} />
          <Text style={[styles.emptyText, isRTL && styles.textRTL]}>
            {t('animal.pregnancies.noRecords')}
          </Text>
          {isEditable && (
            <TouchableOpacity
              style={styles.emptyAddButton}
              onPress={() => {
                // TODO: Navigate to add pregnancy screen
                Alert.alert(t('common.info'), 'Add pregnancy functionality coming soon');
              }}
            >
              <Text style={styles.emptyAddButtonText}>
                {t('animal.pregnancies.addFirst')}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      ) : (
        <FlatList
          data={pregnancies}
          renderItem={renderPregnancyItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerRTL: {
    flexDirection: 'row-reverse',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  addButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: colors.primary + '20',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.gray[600],
  },
  listContainer: {
    paddingBottom: 16,
  },
  pregnancyCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  pregnancyCardRTL: {
    alignItems: 'flex-end',
  },
  pregnancyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  pregnancyHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  pregnancyIconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  pregnancyIconContainerRTL: {
    flexDirection: 'row-reverse',
  },
  pregnancyOutcome: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginLeft: 8,
  },
  pregnancyDate: {
    fontSize: 14,
    color: colors.gray[600],
  },
  pregnancyDetails: {
    marginBottom: 8,
  },
  pregnancyDetailsRTL: {
    alignItems: 'flex-end',
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  detailRowRTL: {
    flexDirection: 'row-reverse',
  },
  detailLabel: {
    fontSize: 14,
    color: colors.gray[600],
    marginLeft: 6,
    marginRight: 8,
  },
  detailValue: {
    fontSize: 14,
    color: colors.gray[800],
    fontWeight: '500',
  },
  complicationsContainer: {
    marginTop: 8,
    padding: 12,
    backgroundColor: colors.danger + '10',
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: colors.danger,
  },
  complicationsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.danger,
    marginBottom: 4,
  },
  complicationsText: {
    fontSize: 12,
    color: colors.secondary,
    lineHeight: 16,
  },
  pregnancyNotes: {
    fontSize: 12,
    color: colors.gray[600],
    fontStyle: 'italic',
    marginTop: 8,
    lineHeight: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  emptyAddButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyAddButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  textRTL: {
    textAlign: 'right',
  },
});
