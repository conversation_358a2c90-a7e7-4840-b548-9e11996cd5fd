import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Shield } from 'lucide-react-native';
import { useTranslation } from '@/i18n/useTranslation';
import colors from '@/constants/colors';
// import { t } from 'i18next'; // or your i18n hook
// import colors from '@/theme/colors'; // adjust path as needed

// const colors = {
//     white: '#fff',
//     info: '#007bff',
//     success: '#28a745',
//     primary: '#007bff',
//     gray: '#6c757d',
// };

// const styles = StyleSheet.create({
//     toggleContainer: {
//         flexDirection: 'row',
//         justifyContent: 'center',
//         alignItems: 'center',
//         marginVertical: 10,
//     },
//     toggleButton: {
//         flexDirection: 'row',
//         alignItems: 'center',
//         paddingVertical: 8,
//         paddingHorizontal: 16,
//         borderRadius: 20,
//         backgroundColor: colors.white,
//         borderWidth: 1,
//         borderColor: colors.gray,
//         marginHorizontal: 5,
//     },
//     activeButton: {
//         backgroundColor: colors.primary,
//         borderColor: colors.primary,
//     },
//     toggleText: {
//         marginLeft: 8,
//         color: colors.gray,
//         fontWeight: '500',
//     },
//     activeText: {
//         color: colors.white,
//     },
// });
const styles = StyleSheet.create({
  toggleContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 10,
    marginVertical: 12,
  },
  toggleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 14,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.gray[300],
    backgroundColor: colors.gray[100],
  },
  activeButton: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  toggleText: {
    marginLeft: 6,
    color: colors.gray[700],
    fontSize: 14,
  },
  activeText: {
    color: colors.white,
  },
});

const RoleToggle = ({ role, setRole }) => {
    const { t, isRTL } = useTranslation()
    return (
        <View style={[styles.toggleContainer, isRTL && { flexDirection: 'row-reverse' }]}>
            <TouchableOpacity
                style={[
                    styles.toggleButton,
                    role === 'admin' && styles.activeButton,
                ]}
                onPress={() => setRole('admin')}
            >
                <Shield size={16} color={role === 'admin' ? colors.white : colors.info} />
                <Text style={[
                    styles.toggleText,
                    role === 'admin' && styles.activeText
                ]}>
                    {t('user.role.admin', 'Admin')}
                </Text>
            </TouchableOpacity>

            <TouchableOpacity
                style={[
                    styles.toggleButton,
                    role === 'caretaker' && styles.activeButton,
                ]}
                onPress={() => setRole('caretaker')}
            >
                <Shield size={16} color={role === 'caretaker' ? colors.white : colors.success} />
                <Text style={[
                    styles.toggleText,
                    role === 'caretaker' && styles.activeText
                ]}>
                    {t('user.role.caretaker', 'Caretaker')}
                </Text>
            </TouchableOpacity>
        </View>
    );
};

export default RoleToggle;
