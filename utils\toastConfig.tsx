import OverlayToast from '@/components/OverlayToast';
import React from 'react';
import { BaseToast, ErrorToast } from 'react-native-toast-message';

// export const toastConfig = {
//   success: (props) => (
//     <BaseToast
//       {...props}
//       style={{ borderLeftColor: 'green' }}
//       contentContainerStyle={{ paddingHorizontal: 15 }}
//       text1Style={{
//         fontSize: 15,
//         fontWeight: 'bold'
//       }}
//     />
//   ),
//   error: (props) => (
//     <ErrorToast
//       {...props}
//       style={{ borderLeftColor: 'red' }}
//     />
//   ),
// };
export const toastConfig = {
  overlay: (props) => <OverlayToast {...props} />,
};