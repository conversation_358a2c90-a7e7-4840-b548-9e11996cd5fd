/**
 * MCP Server Service
 * 
 * Handles all communication with the MCP endpoint for conversational UI
 */

import * as FileSystem from 'expo-file-system';
import {
  MCPServerConfig,
  MCPRequest,
  MCPResponse,
  MCPMessageRequest,
  MCPActionResponse,
} from './mcp-types';

export class MCPServerService {
  private config: MCPServerConfig;

  constructor(config: MCPServerConfig) {
    this.config = {
      timeout: 30000, // 30 seconds default
      ...config,
    };
  }

  /**
   * Send a message to MCP server with conversational UI support
   */
  async sendMessage(request: MCPMessageRequest): Promise<MCPResponse> {
    try {
      const payload = await this.buildRequestPayload(request);
      console.log('MCP Request:', payload);

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout')), this.config.timeout!);
      });

      const fetchPromise = fetch(this.config.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` }),
        },
        body: JSON.stringify(payload),
      });

      const response = await Promise.race([fetchPromise, timeoutPromise]) as Response;

      if (!response.ok) {
        const errorText = await response.json();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      console.log('MCP Response:', data);
      
      return this.normalizeResponse(data);
    } catch (error) {
      console.error('MCP Server error:', error);
      throw error;
    }
  }

  /**
   * Send confirmation for an action
   */
  async confirmAction(
    action: string,
    confirmedData: any,
    farmId: string
  ): Promise<MCPResponse> {
    try {
      const payload: MCPRequest = {
        inputType: 'text',
        intent: 'confirm',
        action,
        confirmedData,
        farmId,
      };

      console.log('MCP Confirm Request:', payload);

      const response = await fetch(this.config.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` }),
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      console.log('MCP Confirm Response:', data);
      
      return this.normalizeResponse(data);
    } catch (error) {
      console.error('MCP Confirm error:', error);
      throw error;
    }
  }

  /**
   * Send selection resolution (e.g., choosing an assignee)
   */
  async resolveSelection(
    action: string,
    selection: any,
    farmId: string
  ): Promise<MCPResponse> {
    try {
      const payload: MCPRequest = {
        inputType: 'text',
        intent: 'resolve',
        action,
        selection,
        farmId,
      };

      console.log('MCP Resolve Request:', payload);

      const response = await fetch(this.config.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` }),
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      console.log('MCP Resolve Response:', data);
      
      return this.normalizeResponse(data);
    } catch (error) {
      console.error('MCP Resolve error:', error);
      throw error;
    }
  }

  /**
   * Build request payload from message request
   */
  private async buildRequestPayload(request: MCPMessageRequest): Promise<MCPRequest> {
    const hasImages = request.images && request.images.length > 0;
    
    const payload: MCPRequest = {
      inputType: hasImages ? (request.message ? 'both' : 'image') : 'text',
      query: request.message || 'Analyze this image',
      farmId: request.farmId,
    };

    // Handle image if provided
    if (hasImages && request.images) {
      try {
        const imageUri = request.images[0];
        const base64 = await FileSystem.readAsStringAsync(imageUri, {
          encoding: FileSystem.EncodingType.Base64,
        });
        payload.imageBase64 = `data:image/jpeg;base64,${base64}`;
      } catch (error) {
        console.error('Error processing image:', error);
      }
    }

    return payload;
  }

  /**
   * Normalize response from MCP server
   */
  private normalizeResponse(data: any): MCPResponse {
    // Normalize status field
    if (data.status) {
      data.status = data.status.toLowerCase().trim();
    }

    // Normalize suggested actions
    if (data.suggestedActions) {
      data.suggestedActions = data.suggestedActions.map((action: any) => ({
        ...action,
        type: action.type || 'primary',
      }));
    }

    // Normalize follow-up candidates
    if (data.followUp?.candidates) {
      data.followUp.candidates = data.followUp.candidates.map((candidate: any) => ({
        ...candidate,
        displayName: candidate.displayName || candidate.name || 'Unknown',
      }));
    }

    return data;
  }

  /**
   * Test connection to MCP server
   */
  async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      const testPayload: MCPRequest = {
        inputType: 'text',
        query: 'test connection',
        farmId: 'test',
      };

      const response = await fetch(this.config.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` }),
        },
        body: JSON.stringify(testPayload),
      });

      if (response.ok) {
        return { success: true, message: 'Connection successful' };
      } else {
        return { success: false, message: `Connection failed: ${response.status}` };
      }
    } catch (error) {
      return {
        success: false,
        message: `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }
}

// Default configuration
export const defaultMCPConfig: MCPServerConfig = {
  baseUrl: 'https://us-central1-kissandost-9570f.cloudfunctions.net/mcp/query',
  apiKey: process.env.EXPO_PUBLIC_MCP_API_KEY,
  timeout: 30000,
};

// Default service instance
export const mcpServerService = new MCPServerService(defaultMCPConfig);
