import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  ActivityIndicator,
  Modal,
  FlatList,
} from 'react-native';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import Button from '@/components/Button';
import Input from '@/components/Input';
import ImagePicker from '@/components/ImagePicker';
import MultipleImagePicker from '@/components/MultipleImagePicker';
import DatePicker from '@/components/DatePicker';
import { uploadImageAsync } from '@/utils/firebase-storage';
import {
  ChevronDown,
  Ruler,
  Home,
  Check,
  QrCode,
  Tractor,
  Factory,
  Gauge,
  DollarSign,
  Clipboard,
  Map,
  MapPin,
} from 'lucide-react-native';
import { Farm } from '@/types';
import { analyzeImageWithVision } from '@/utils/openai-vision';
import { generateSuggestedItemId, validateItemId } from '@/utils/qrcode';
import DropdownPicker from '@/components/DropdownPicker';
import { useTranslation } from '@/i18n/useTranslation';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { analyzeImageForFormPopulation } from '@/utils/ai-utils';
import { useLookupStore } from '@/store/lookup-store';
import { getEquipmentCategoryLookupId } from '@/utils/ai-lookup-integration';
// Removed import for useRouter

export default function CreateEquipmentScreen() {
  const { user } = useAuthStore();
  const {
    addInventoryItem,
    updateInventoryItem,
    currentFarm,
    farms,
    fetchFarms,
    setCurrentFarm,
    inventoryEquipment,
    fetchEquipmentFromInventory,
  } = useFarmStore();
  const { getLookupsByCategory, getLookupsByCategoryParssedData } = useLookupStore();

  const { id } = useLocalSearchParams();
  const isEditMode = id ? true : false;
  const { t, isRTL } = useTranslation()
  const [name, setName] = useState('');
  const [category, setCategory] = useState<string>('');
  const [quantity, setQuantity] = useState('1');
  const [unit, setUnit] = useState('units');
  const [price, setPrice] = useState('');
  const [supplier, setSupplier] = useState('');
  const [location, setLocation] = useState('');
  const [description, setDescription] = useState('');
  const [purchaseDate, setPurchaseDate] = useState<Date | null>(null);
  const [expiryDate, setExpiryDate] = useState<Date | null>(null);
  const [minQuantity, setMinQuantity] = useState('1');
  const [isConsumable, setIsConsumable] = useState(false);
  const [imageUri, setImageUri] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  // Item ID for QR code
  const [itemId, setItemId] = useState('');
  const [itemIdError, setItemIdError] = useState('');

  const [showTypeDropdown, setShowTypeDropdown] = useState(false);
  const [showStatusDropdown, setShowStatusDropdown] = useState(false);
  const [showFarmModal, setShowFarmModal] = useState(false);
  const [analyzingImage, setAnalyzingImage] = useState(false);

  // Lookup-based arrays
  const [equipmentTypeArray, setEquipmentTypeArray] = useState([] as any);
  const [statusArray, setStatusArray] = useState([] as any);
  const [categoryArray, setCategoryArray] = useState([] as any);

  useEffect(() => {
    setEquipmentTypeArray(getLookupsByCategoryParssedData('equipmentType', 'entity.equipment.type'));
    setStatusArray(getLookupsByCategoryParssedData('equipmentStatus', 'entity.equipment.status'));
    setCategoryArray(getLookupsByCategoryParssedData('inventoryCategory', 'entity.equipment.category'));
  }, []);

  // useEffect(() => {
  //   if (user?.id) {
  //     fetchFarms(user.id);
  //   }
  // }, [user]);

  // Generate suggested item ID when name changes
  useEffect(() => {
    if (name) {
      setItemId(generateSuggestedItemId('equipment', name));
    }
  }, [name]);

  const [equipment, setEquipment] = useState({} as any)

  useEffect(() => {
    if (isEditMode && id && inventoryEquipment) {
      const equipmentIdString = Array.isArray(id) ? id[0] : id as string;
      const equipmentData = inventoryEquipment.find(eq => eq.id === equipmentIdString);

      if (equipmentData) {
        setEquipment(equipmentData);
        setName(equipmentData.name || '');
        setCategory(equipmentData.category || 'Equipment');
        setQuantity(equipmentData.quantity?.toString() || '1');
        setUnit(equipmentData.unit || 'units');
        setPrice(equipmentData.price?.toString() || '');
        setSupplier(equipmentData.supplier || '');
        setLocation(equipmentData.location || '');
        setDescription(equipmentData.description || '');
        setPurchaseDate(equipmentData.purchaseDate ? new Date(equipmentData.purchaseDate) : null);
        setExpiryDate(equipmentData.expiryDate ? new Date(equipmentData.expiryDate) : null);
        setMinQuantity(equipmentData.minQuantity?.toString() || '1');
        setIsConsumable(equipmentData.isConsumable || false);
        setItemId(equipmentData.id || ''); // Use equipment ID as item ID

        // Set image if available
        if (equipmentData.imageUrl) {
          setImageUri(equipmentData.imageUrl);
        }
      }
    }
  }, [isEditMode, id, inventoryEquipment]);

  const validateForm = () => {
    if (!name) {
      Alert.alert('Error', 'Please enter equipment name');
      return false;
    }

    if (!category) {
      Alert.alert('Error', 'Please select a category');
      return false;
    }

    if (!quantity || isNaN(Number(quantity)) || Number(quantity) <= 0) {
      Alert.alert('Error', 'Please enter a valid quantity');
      return false;
    }

    if (!unit) {
      Alert.alert('Error', 'Please enter a unit');
      return false;
    }

    if (!currentFarm) {
      Alert.alert('Error', 'Please select a farm first');
      return false;
    }

    return true;
  };

  const handleCreateEquipment = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);

      // Upload image if selected
      let uploadedImageUrl = '';
      if (imageUri && !imageUri.startsWith('http')) {
        uploadedImageUrl = await uploadImageAsync(imageUri, 'inventory');
      } else if (imageUri) {
        uploadedImageUrl = imageUri;
      }

      // Create inventory equipment data object
      const inventoryData: any = {
        name,
        category,
        quantity: parseInt(quantity) || 1,
        unit,
        farmId: currentFarm.id,
        updatedAt: new Date(),
      };

      // Only add createdAt for new equipment
      if (!isEditMode) {
        inventoryData.createdAt = new Date();
      }

      // Only add these fields if they have values
      if (price && !isNaN(Number(price))) {
        inventoryData.price = Number(price);
      }
      if (supplier) inventoryData.supplier = supplier;
      if (location) inventoryData.location = location;
      if (description) inventoryData.description = description;
      if (purchaseDate) inventoryData.purchaseDate = purchaseDate.toISOString();
      if (expiryDate) inventoryData.expiryDate = expiryDate.toISOString();
      if (minQuantity && !isNaN(Number(minQuantity))) {
        inventoryData.minQuantity = Number(minQuantity);
      }
      if (uploadedImageUrl) inventoryData.imageUrl = uploadedImageUrl;

      inventoryData.isConsumable = isConsumable;

      // Add history entry
      const historyEntry = {
        date: new Date().toISOString(),
        type: isEditMode ? 'updated' : 'created',
        details: isEditMode ? 'Item updated' : 'Item created',
        userId: user?.uid || '',
      };
      inventoryData.history = isEditMode ? [...(equipment.history || []), historyEntry] : [historyEntry];

      if (isEditMode) {
        await updateInventoryItem(id as string, inventoryData);
        Alert.alert('Success', 'Equipment updated successfully', [
          { text: 'OK', onPress: () => router.back() }
        ]);
      } else {
        await addInventoryItem(inventoryData);
        Alert.alert('Success', 'Equipment added successfully', [
          { text: 'OK', onPress: () => router.back() }
        ]);
      }
    } catch (error) {
      console.error(`Error ${isEditMode ? 'updating' : 'creating'} equipment:`, error);
      Alert.alert('Error', `Failed to ${isEditMode ? 'update' : 'add'} equipment. Please try again.`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleImageAnalysis = async (imageUri: string) => {
    try {
      setAnalyzingImage(true);
      const result = await analyzeImageForFormPopulation(imageUri, 'equipment');

      if (result.suggestedFields) {
        const fields = result.suggestedFields;

        // Auto-populate form fields based on AI analysis
        if (fields.type && fields.type !== 'unknown') {
          setType(fields.type);
        }
        if (fields.manufacturer && !manufacturer) {
          setManufacturer(fields.manufacturer);
        }
        if (fields.model && !model) {
          setModel(fields.model);
        }
        if (fields.condition && fields.condition !== 'unknown') {
          // Map condition to status
          const conditionToStatus = {
            'excellent': 'operational',
            'good': 'operational',
            'fair': 'maintenance',
            'poor': 'repair',
            'needs_repair': 'repair'
          };
          const mappedStatus = conditionToStatus[fields.condition as keyof typeof conditionToStatus];
          if (mappedStatus) {
            setStatus(mappedStatus as any);
          }
        }

        // Add analysis to notes
        const analysisNotes = [
          result.analysis,
          fields.key_features && `Features: ${fields.key_features}`,
          fields.estimated_age && `Estimated Age: ${fields.estimated_age}`,
          fields.maintenance_needs && `Maintenance: ${fields.maintenance_needs}`,
        ].filter(Boolean).join('\n\n');

        if (analysisNotes && !notes) {
          setNotes(analysisNotes);
        }

        Alert.alert(
          'Analysis Complete',
          'Form fields have been auto-populated based on image analysis. Please review and adjust as needed.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error analyzing image:', error);
      Alert.alert('Analysis Failed', 'Could not analyze the image. Please try again.');
    } finally {
      setAnalyzingImage(false);
    }
  };

  const renderFarmItem = ({ item }: { item: Farm }) => (
    <TouchableOpacity
      style={styles.modalItem}
      onPress={() => {
        setCurrentFarm(item.id);
        setShowFarmModal(false);
      }}
    >
      <View style={styles.modalItemContent}>
        <Home size={20} color={colors.gray[600]} style={styles.modalItemIcon} />
        <Text style={styles.modalItemText}>{item.name}</Text>
      </View>
      {currentFarm?.id === item.id && (
        <Check size={20} color={colors.primary} />
      )}
    </TouchableOpacity>
  );

  // Helper function to get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'operational':
        return colors.success;
      case 'maintenance':
        return colors.warning;
      case 'repair':
        return colors.danger;
      case 'retired':
        return colors.gray[500];
      default:
        return colors.gray[500];
    }
  };

  // Handle image analysis results
  const handleAnalysisComplete = (analysis: any) => {
    if (!analysis) return;

    // Only apply results if it's equipment
    if (analysis.category === 'agri_equipment') {
      // Set equipment name if empty
      if (!name && analysis.type) {
        setName(analysis.type.charAt(0).toUpperCase() + analysis.type.slice(1));
      }

      // Set equipment type based on analysis
      if (analysis.type) {
        const equipType = analysis.type.toLowerCase();
        if (equipType.includes('tractor')) {
          setType('tractor');
        } else if (equipType.includes('harvester')) {
          setType('harvester');
        } else if (equipType.includes('plow')) {
          setType('plow');
        } else if (equipType.includes('spray')) {
          setType('sprayer');
        } else if (equipType.includes('irrigat')) {
          setType('irrigation');
        } else if (equipType.includes('tool')) {
          setType('tool');
        } else {
          setType('other');
        }
      }

      // Set manufacturer and model if available
      if (analysis.subtype) {
        const subtype = analysis.subtype;
        // Try to extract manufacturer and model
        const commonManufacturers = ['John Deere', 'New Holland', 'Case', 'Massey Ferguson', 'Kubota', 'Claas'];

        for (const manufacturer of commonManufacturers) {
          if (subtype.includes(manufacturer)) {
            setManufacturer(manufacturer);
            // Extract model (everything after manufacturer name)
            const modelPart = subtype.substring(subtype.indexOf(manufacturer) + manufacturer.length).trim();
            if (modelPart) {
              setModel(modelPart);
            }
            break;
          }
        }

        // If no manufacturer was found, just use the subtype as model
        if (!manufacturer) {
          setModel(subtype);
        }
      }

      // Set status based on details
      if (analysis.details) {
        const details = analysis.details.toLowerCase();

        if (details.includes('repair') || details.includes('broken') || details.includes('not working')) {
          setStatus('repair');
        } else if (details.includes('maintenance') || details.includes('service')) {
          setStatus('maintenance');
        } else if (details.includes('retired') || details.includes('old') || details.includes('not in use')) {
          setStatus('retired');
        } else if (details.includes('operational') || details.includes('working') || details.includes('good condition')) {
          setStatus('operational');
        }

        // Add details to notes if empty
        if (!notes) {
          setNotes(analysis.details);
        }
      }
    }
  };

  // Hardcoded arrays removed - now using lookup-based data from useEffect above

  return (
    <>
      <Stack.Screen
        options={{
          title: isEditMode ? t('entity.equipment.edit') : t('entity.equipment.add'),
          headerShown: true,
        }}
      />

      <SafeAreaView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          {/* Farm Selection */}
          {farms.length > 0 && (
            <View style={styles.farmSelectionContainer}>
              <Text style={styles.farmSelectionLabel}>Select Farm</Text>
              <TouchableOpacity
                style={styles.farmSelectionButton}
                onPress={() => setShowFarmModal(true)}
              >
                <Home size={20} color={colors.gray[500]} style={styles.farmIcon} />
                <Text style={styles.farmSelectionText}>
                  {currentFarm?.name || "Select a farm"}
                </Text>
                <ChevronDown size={20} color={colors.gray[500]} />
              </TouchableOpacity>
            </View>
          )}

          <MultipleImagePicker
            label={t('equipment.images')}
            images={images}
            onImagesChange={(newImages) => {
              setImages(newImages);
              // Keep backward compatibility with single image
              if (newImages.length > 0) {
                setImageUri(newImages[0]);
              } else {
                setImageUri('');
              }
            }}
            maxImages={3}
            placeholder={t('equipment.addPhoto')}
            showAnalyzeButton={true}
            onAnalyzeImage={handleImageAnalysis}
            analyzingImage={analyzingImage}
          />

          <View style={styles.formContainer}>
            {/* Item ID field for QR code */}
            <View style={styles.itemIdContainer}>
              <View style={[styles.itemIdHeader, isRTL && { flexDirection: 'row-reverse' }]}>
                <Text style={styles.label}>{t("entity.equipment.itemId")}</Text>
                <QrCode size={20} color={colors.primary} />
              </View>
              <Input
                placeholder={t("entity.equipment.enterUniqueId")}
                value={itemId}
                onChangeText={(text) => {
                  setItemId(text);
                  if (text && !validateItemId(text)) {
                    setItemIdError(t('entity.equipment.idFormatNote'));
                  } else {
                    setItemIdError('');
                  }
                }}
                containerStyle={itemIdError ? styles.inputContainerError : styles.inputContainer}
              />
              {itemIdError ? (
                <Text style={styles.errorText}>{itemIdError}</Text>
              ) : (
                <Text style={[styles.helperText, isRTL && { textAlign: 'right' }]}>
                  {t('entity.equipment.idUsageNote')}
                </Text>
              )}
            </View>

            <Input
              label={t("entity.equipment.name")}
              placeholder={t("entity.equipment.enterEquipmentNamePlaceholder")}
              value={name}
              onChangeText={setName}
              containerStyle={styles.inputContainer}
              leftIcon={<Tractor size={20} color={colors.gray[500]} />}
            />

            <DropdownPicker
              label={t("entity.equipment.category")}
              options={categoryArray}
              onSelect={(val) => setCategory(val as any)}
              selectedValue={category}
              isMultiple={false}
              required={true}
            />

            {/* <Text style={[styles.label,isRTL && {textAlign:'right'}]}>{t("entity.equipment.equipmentType")}</Text> */}
            <DropdownPicker
              label={t("entity.equipment.equipmentType")}
              options={equipmentTypeArray}
              onSelect={(val) => setType(val as any)}
              selectedValue={type}
              isMultiple={false}
            />

            <Input
              label={t("entity.equipment.manufacturer")}
              placeholder={t("entity.equipment.enterManufacturerName")}
              value={manufacturer}
              onChangeText={setManufacturer}
              containerStyle={styles.inputContainer}
              leftIcon={<Factory size={20} color={colors.gray[500]} />}
            />

            <Input
              label={t("entity.equipment.model")}
              placeholder={t("entity.equipment.enterModelNumberOrName")}
              value={model}
              onChangeText={setModel}
              containerStyle={styles.inputContainer}
              leftIcon={<Gauge size={20} color={colors.gray[500]} />}
            />

            <DatePicker
              label={t("entity.equipment.purchaseDate")}
              value={purchaseDate}
              onChange={setPurchaseDate}
              placeholder={t("entity.equipment.selectADate")}
              startOffset={-60} // 5 years in the past
              endOffset={0}
            />

            <Input
              label={t("entity.equipment.purchasePrice")}
              placeholder={t("entity.equipment.enterPurchasePrice")}
              value={purchasePrice}
              onChangeText={setPurchasePrice}
              keyboardType="numeric"
              containerStyle={styles.inputContainer}
              leftIcon={<DollarSign size={20} color={colors.gray[500]} />}
            />

            {/* <Text style={[styles.label,isRTL && {textAlign:'right'}]}>{t("entity.equipment.status")}</Text> */}
            <DropdownPicker
              label={t("entity.equipment.status")}
              options={statusArray}
              onSelect={(val) => setStatus(val as any)}
              selectedValue={status}
              isMultiple={false}
            />

            <DatePicker
              label={t("entity.equipment.lastMaintenanceDate")}
              value={lastMaintenanceDate}
              onChange={setLastMaintenanceDate}
              placeholder={t("entity.equipment.selectADate")}
              startOffset={-24} // 2 years in the past
              endOffset={0}
            />

            <DatePicker
              label={t("entity.equipment.nextMaintenanceDate")}
              value={nextMaintenanceDate}
              onChange={setNextMaintenanceDate}
              placeholder={t("entity.equipment.selectADate")}
              startOffset={0}
              endOffset={24} // 2 years in the future
            />

            <Input
              label={t("entity.equipment.location")}
              placeholder={t("entity.equipment.enterStorageLocation")}
              value={location}
              onChangeText={setLocation}
              containerStyle={styles.inputContainer}
              leftIcon={<MapPin size={20} color={colors.gray[500]} />}
            />

            <Input
              label={t("entity.equipment.notes")}
              placeholder={t("entity.equipment.enterNotes")}
              value={notes}
              onChangeText={setNotes}
              multiline
              numberOfLines={4}
              containerStyle={styles.inputContainer}
              inputStyle={styles.textArea}
              leftIcon={<Clipboard size={20} color={colors.gray[500]} />}
            />
          </View>

          <View style={styles.buttonContainer}>
            <Button
              title={t("common.cancel")}
              variant="outline"
              onPress={() => router.back()}
              style={styles.cancelButton}
              disabled={isLoading}
            />
            <Button
              title={isLoading ? t("entity.equipment.adding") : t("entity.equipment.addEquipment")}
              onPress={handleCreateEquipment}
              style={styles.createButton}
              disabled={isLoading}
              leftIcon={isLoading ? <ActivityIndicator size="small" color={colors.white} /> : undefined}
            />
          </View>
        </ScrollView>

        {/* Farm Selection Modal */}
        <Modal
          visible={showFarmModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowFarmModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Select Farm</Text>
                <TouchableOpacity onPress={() => setShowFarmModal(false)}>
                  <Text style={styles.modalCloseText}>Close</Text>
                </TouchableOpacity>
              </View>
              {farms.length > 0 ? (
                <FlatList
                  data={farms}
                  renderItem={renderFarmItem}
                  keyExtractor={item => item.id}
                  style={styles.modalList}
                />
              ) : (
                <View style={styles.emptyListContainer}>
                  <Text style={styles.emptyListText}>No farms available. Please create a farm first.</Text>
                </View>
              )}
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  formContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputContainerError: {
    marginBottom: 4,
    borderColor: colors.danger,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
    paddingTop: 12,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
  },
  dropdownContainer: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    marginBottom: 16,
    position: 'relative',
  },
  dropdownHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  dropdownText: {
    fontSize: 14,
    color: colors.gray[800],
    flex: 1,
  },
  dropdownOptions: {
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    backgroundColor: colors.white,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    elevation: 2,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    maxHeight: 200,
  },
  dropdownOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  dropdownOptionText: {
    fontSize: 14,
    color: colors.gray[800],
    marginLeft: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  createButton: {
    flex: 1,
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  modalCloseText: {
    fontSize: 16,
    color: colors.primary,
  },
  modalList: {
    maxHeight: '80%',
  },
  modalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalItemIcon: {
    marginRight: 12,
  },
  modalItemText: {
    fontSize: 16,
    color: colors.gray[800],
  },
  emptyListContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyListText: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
  },
  farmSelectionContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  farmSelectionLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
  },
  farmSelectionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  farmIcon: {
    marginRight: 8,
  },
  farmSelectionText: {
    fontSize: 14,
    color: colors.gray[800],
    flex: 1,
  },
  itemIdContainer: {
    marginBottom: 16,
  },
  itemIdHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  errorText: {
    color: colors.danger,
    fontSize: 12,
    marginBottom: 8,
  },
  helperText: {
    color: colors.gray[500],
    fontSize: 12,
    marginTop: 4,
  },
});
