import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';
import { useFarmStore } from '@/store/farm-store';
import { AnimalHealthCheck } from '@/types';
import { Ionicons } from '@expo/vector-icons';
import { normalizeDate } from '@/utils/dateUtils';
// import { format } from 'date-fns';

interface AnimalHealthChecksSectionProps {
  animalId: string;
  isEditable?: boolean;
}

export const AnimalHealthChecksSection: React.FC<AnimalHealthChecksSectionProps> = ({
  animalId,
  isEditable = true,
}) => {
  const { t, isRTL } = useTranslation();
  const { fetchAnimalHealthChecks, animalHealthChecks } = useFarmStore();
  const [loading, setLoading] = useState(false);
  const [healthChecks, setHealthChecks] = useState<AnimalHealthCheck[]>([]);

  useEffect(() => {
    loadHealthChecks();
  }, [animalId]);

  const loadHealthChecks = async () => {
    try {
      setLoading(true);
      const checks = await fetchAnimalHealthChecks(animalId);
      setHealthChecks(checks);
    } catch (error) {
      console.error('Error loading health checks:', error);
      Alert.alert(t('common.error'), t('animal.healthChecks.loadError'));
    } finally {
      setLoading(false);
    }
  };

  const getHealthCheckIcon = (type: string) => {
    switch (type) {
      case 'checkup':
        return 'medical';
      case 'illness':
        return 'warning';
      case 'vaccine':
        return 'shield-checkmark';
      case 'treatment':
        return 'bandage';
      default:
        return 'clipboard';
    }
  };

  const getHealthCheckColor = (type: string) => {
    switch (type) {
      case 'checkup':
        return colors.primary;
      case 'illness':
        return colors.danger;
      case 'vaccine':
        return colors.success;
      case 'treatment':
        return colors.warning;
      default:
        return colors.gray[500];
    }
  };

  const renderHealthCheckItem = ({ item }: { item: AnimalHealthCheck }) => (
    <View style={[styles.healthCheckCard, isRTL && styles.healthCheckCardRTL]}>
      <View style={[styles.healthCheckHeader, isRTL && styles.healthCheckHeaderRTL]}>
        <View style={[styles.healthCheckIconContainer, isRTL && styles.healthCheckIconContainerRTL]}>
          <Ionicons
            name={getHealthCheckIcon(item.type) as any}
            size={20}
            color={getHealthCheckColor(item.type)}
          />
          <Text style={[styles.healthCheckTitle, isRTL && styles.textRTL]}>
            {item.title}
          </Text>
        </View>
        <Text style={[styles.healthCheckDate, isRTL && styles.textRTL]}>
          {normalizeDate(new Date(item.date), 'DD-MM-YYYY')}
        </Text>
      </View>

      {item.description && (
        <Text style={[styles.healthCheckDescription, isRTL && styles.textRTL]}>
          {item.description}
        </Text>
      )}

      <View style={[styles.healthCheckDetails, isRTL && styles.healthCheckDetailsRTL]}>
        {item.veterinarian && (
          <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
            <Ionicons name="person" size={16} color={colors.gray[600]} />
            <Text style={[styles.detailText, isRTL && styles.textRTL]}>
              {item.veterinarian}
            </Text>
          </View>
        )}

        {item.temperature && (
          <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
            <Ionicons name="thermometer" size={16} color={colors.gray[600]} />
            <Text style={[styles.detailText, isRTL && styles.textRTL]}>
              {item.temperature}°C
            </Text>
          </View>
        )}

        {item.weight && (
          <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
            <Ionicons name="scale" size={16} color={colors.gray[600]} />
            <Text style={[styles.detailText, isRTL && styles.textRTL]}>
              {item.weight} {item.weightUnit || 'kg'}
            </Text>
          </View>
        )}

        {item.cost && (
          <View style={[styles.detailRow, isRTL && styles.detailRowRTL]}>
            <Ionicons name="cash" size={16} color={colors.gray[600]} />
            <Text style={[styles.detailText, isRTL && styles.textRTL]}>
              ${item.cost}
            </Text>
          </View>
        )}
      </View>

      {item.medications && item.medications.length > 0 && (
        <View style={styles.medicationsContainer}>
          <Text style={[styles.medicationsTitle, isRTL && styles.textRTL]}>
            {t('animal.healthChecks.medications')}:
          </Text>
          {item.medications.map((medication, index) => (
            <Text key={index} style={[styles.medicationItem, isRTL && styles.textRTL]}>
              • {medication}
            </Text>
          ))}
        </View>
      )}

      {item.notes && (
        <Text style={[styles.healthCheckNotes, isRTL && styles.textRTL]}>
          {item.notes}
        </Text>
      )}
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, isRTL && styles.textRTL]}>
          {t('animal.healthChecks.loading')}
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={[styles.header, isRTL && styles.headerRTL]}>
        <Text style={[styles.sectionTitle, isRTL && styles.textRTL]}>
          {t('animal.healthChecks.title')}
        </Text>
        {isEditable && (
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => {
              // TODO: Navigate to add health check screen
              Alert.alert(t('common.info'), 'Add health check functionality coming soon');
            }}
          >
            <Ionicons name="add" size={20} color={colors.primary} />
          </TouchableOpacity>
        )}
      </View>

      {healthChecks.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="medical" size={48} color={colors.gray[400]} />
          <Text style={[styles.emptyText, isRTL && styles.textRTL]}>
            {t('animal.healthChecks.noRecords')}
          </Text>
          {isEditable && (
            <TouchableOpacity
              style={styles.emptyAddButton}
              onPress={() => {
                // TODO: Navigate to add health check screen
                Alert.alert(t('common.info'), 'Add health check functionality coming soon');
              }}
            >
              <Text style={styles.emptyAddButtonText}>
                {t('animal.healthChecks.addFirst')}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      ) : (
        <FlatList
          data={healthChecks}
          renderItem={renderHealthCheckItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerRTL: {
    flexDirection: 'row-reverse',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.primary,
  },
  addButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: colors.primary + '20',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.gray[600],
  },
  listContainer: {
    paddingBottom: 16,
  },
  healthCheckCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  healthCheckCardRTL: {
    alignItems: 'flex-end',
  },
  healthCheckHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  healthCheckHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  healthCheckIconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  healthCheckIconContainerRTL: {
    flexDirection: 'row-reverse',
  },
  healthCheckTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.primary,
    marginLeft: 8,
  },
  healthCheckDate: {
    fontSize: 14,
    color: colors.gray[600],
  },
  healthCheckDescription: {
    fontSize: 14,
    color: colors.secondary,
    marginBottom: 12,
    lineHeight: 20,
  },
  healthCheckDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  healthCheckDetailsRTL: {
    flexDirection: 'row-reverse',
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 4,
  },
  detailRowRTL: {
    flexDirection: 'row-reverse',
    marginRight: 0,
    marginLeft: 16,
  },
  detailText: {
    fontSize: 12,
    color: colors.gray[600],
    marginLeft: 4,
  },
  medicationsContainer: {
    marginTop: 8,
    padding: 12,
    backgroundColor: colors.gray[50],
    borderRadius: 8,
  },
  medicationsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.primary,
    marginBottom: 4,
  },
  medicationItem: {
    fontSize: 12,
    color: colors.secondary,
    marginBottom: 2,
  },
  healthCheckNotes: {
    fontSize: 12,
    color: colors.gray[600],
    fontStyle: 'italic',
    marginTop: 8,
    lineHeight: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  emptyAddButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyAddButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  textRTL: {
    textAlign: 'right',
  },
});
