import * as SplashScreen from 'expo-splash-screen';
import React, { useEffect, useState, useRef, useTransition } from 'react';
import { Slot, useRouter, useSegments } from 'expo-router';
import { useFonts } from 'expo-font';
// import SplashScreen from 'expo-splash-screen';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useAuthStore } from '@/store/auth-store';
import { LanguageProvider, useLanguage } from '@/i18n/translations/LanguageProvider';

import {
  changeLanguage,
  getCurrentLocale,
  isRTL as checkRTL,
  onLanguageChange,
  offLanguageChange,
  t as translate,
} from '../i18n/index';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Toast from 'react-native-toast-message';
import { toastConfig } from '@/utils/toastConfig';
import { useTranslation } from '@/i18n/useTranslation';
import { StatusBar } from 'expo-status-bar';
import { I18nManager } from 'react-native';
import * as Updates from 'expo-updates';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
export const unstable_settings = {
  initialRouteName: "(auth)",
};



// Custom hook to handle protected routes
function useProtectedRoute(isAuthenticated: boolean) {
  const segments = useSegments();
  const router = useRouter();
  const [isNavigationReady, setIsNavigationReady] = useState(false);
  const initialNavigationDone = useRef(false);
  const isMounted = useRef(false);

  useEffect(() => {
    async function prepare() {
      try {
        // Prevent the splash screen from auto-hiding before asset loading is complete.
        await SplashScreen.preventAutoHideAsync();
        // Load assets, fonts, data etc.
      } catch (e) {
        console.warn(e);
      }
    }

    prepare();
  }, []);

  //   useEffect(() => {
  //   const bootstrapLanguage = async () => {
  //     const lang = await AsyncStorage.getItem('lang');
  //     i18n.changeLanguage(lang || 'en'); // set language
  //     setIsUrdu(lang === 'ur'); // flag for RTL usage
  //   };
  //   bootstrapLanguage();
  // }, []);
  // Set up the navigation ready state
  useEffect(() => {
    setIsNavigationReady(true);
  }, []);

  // Set the mounted ref
  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  useEffect(() => {
    // Skip navigation if component is not mounted or navigation is not ready
    if (!isMounted.current || !isNavigationReady) return;

    const inAuthGroup = segments[0] === "(auth)";

    // Only navigate if we haven't done the initial navigation yet
    // or if the auth state has changed
    if (!initialNavigationDone.current || (isAuthenticated && inAuthGroup) || (!isAuthenticated && !inAuthGroup)) {
      initialNavigationDone.current = true;

      // Use setTimeout to ensure navigation happens after component is fully mounted
      setTimeout(() => {
        if (isMounted.current) {
          if (!isAuthenticated && !inAuthGroup) {
            router.replace("/(auth)");
          } else if (isAuthenticated && inAuthGroup) {
            router.replace("/(app)/(tabs)");
          }
        }
      }, 100);
    }
  }, [isAuthenticated, segments, router, isNavigationReady]);
}

// Function to initialize language settings from AsyncStorage
async function initializeLanguageSettings() {

  const { t, setLocale, isRTL } = useTranslation();
  try {
    // Get the stored language preference
    const storedLang = await AsyncStorage.getItem('appLanguage');


    if (storedLang) {

      const isRTL = storedLang === 'ur';
      setLocale(storedLang);
      // Set RTL direction if needed
      // if (I18nManager.isRTL !== isRTL) {
      //   I18nManager.allowRTL(isRTL);
      //   I18nManager.forceRTL(isRTL);
      //   // Note: We don't restart the app here as LanguageProvider will handle this
      // }
    }

    return storedLang || 'en';
  } catch (error) {
    console.error('Error initializing language settings:', error);
    return 'en'; // Default to English on error
  }
}

export default function RootLayout() {
  const [loaded, error] = useFonts({
    ...FontAwesome.font,
  });
  const { t, setLocale } = useTranslation();
  const [languageInitialized, setLanguageInitialized] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  // Initialize language settings
  // useEffect(() => {
  //   const setupLanguage = async () => {
  //     const res = await initializeLanguageSettings();

  //     Toast.show({
  //       type: 'success',
  //       text1: 'Success',
  //       text2: JSON.stringify(res),// 'Equipment deleted successfully',
  //     });
  //     setSelectedLanguage(res);
  //     setLanguageInitialized(true);
  //   };

  //   setupLanguage();
  // }, []);

  useEffect(() => {
    const initLanguage = async () => {
      const lang = await AsyncStorage.getItem('lang');;
      if (lang) {
        setLocale(lang)
        // i18n.changeLanguage(lang);
        // setIsUrdu(lang === 'ur');
      }
    };
    initLanguage();
  }, []);

  // Use a stable reference to isAuthenticated
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);

  // Use our protection hook
  useProtectedRoute(isAuthenticated);

  useEffect(() => {
    if (error) {
      console.error(error);
      throw error;
    }
  }, [error]);

  useEffect(() => {
    if (I18nManager.isRTL) {
      I18nManager.forceRTL(false);
      Updates.reloadAsync(); // force reset
    }
  }, []);
  useEffect(() => {
    if (loaded && languageInitialized) {
      SplashScreen.hideAsync();
    }
  }, [loaded, languageInitialized]);

  if (!loaded || !languageInitialized) {
    return <Slot />;
  }

  return (
    // <GestureHandlerRootView style={styles.container}>
      <LanguageProvider>

        <StatusBar style="auto" />
        <Slot />
        <Toast config={toastConfig} />
      </LanguageProvider>
    
  );
}

// Add styles object for container
const styles = {
  container: {
    flex: 1,
  },
};

