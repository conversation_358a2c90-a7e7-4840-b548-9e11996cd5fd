import React from 'react';
import { TouchableOpacity, Text, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { colors } from '@/constants/colors';
import { StyleSheet } from 'react-native';

export default function MCPChatFAB() {
  return (
    <TouchableOpacity
      style={styles.fab}
      onPress={() => router.push('/mcp-chat')}
      activeOpacity={0.8}
    >
      <Ionicons name="chatbubbles" size={24} color={colors.white} />
      <Text style={styles.fabText}>MCP</Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  fab: {
    position: 'absolute',
    bottom: 90, // Position above the regular AI FAB
    right: 20,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.secondary || colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  fabText: {
    color: colors.white,
    fontSize: 9,
    fontWeight: 'bold',
    marginTop: 2,
  },
});
