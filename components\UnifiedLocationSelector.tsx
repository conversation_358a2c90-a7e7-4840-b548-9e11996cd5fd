import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleSheet,
  Alert,
} from 'react-native';
import { colors } from '@/constants/colors';
import { MaterialIcons } from '@expo/vector-icons';
import { MapPin, Plus, TreeDeciduous, Leaf } from 'lucide-react-native';
import { useFarmStore } from '@/store/farm-store';
import { useTranslation } from '@/i18n/useTranslation';
import { router } from 'expo-router';

interface Location {
  id: string;
  name: string;
  type: 'garden' | 'field';
  size?: number;
  sizeUnit?: string;
  description?: string;
}

interface UnifiedLocationSelectorProps {
  label?: string;
  value?: string;
  onLocationSelect: (location: Location | null) => void;
  placeholder?: string;
  required?: boolean;
  error?: string;
  farmId?: string;
  allowedTypes?: ('garden' | 'field')[];
  showCreateOption?: boolean;
}

export default function UnifiedLocationSelector({
  label = 'Location',
  value,
  onLocationSelect,
  placeholder = 'Select location',
  required = false,
  error,
  farmId,
  allowedTypes = ['garden', 'field'],
  showCreateOption = true,
}: UnifiedLocationSelectorProps) {
  const { t, isRTL } = useTranslation();
  const { gardens, fields, fetchGardens, fetchFields, currentFarm } = useFarmStore();
  
  const [showModal, setShowModal] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null);
  const [filterType, setFilterType] = useState<'all' | 'garden' | 'field'>('all');

  const effectiveFarmId = farmId || currentFarm?.id;

  useEffect(() => {
    if (effectiveFarmId) {
      if (allowedTypes.includes('garden')) {
        fetchGardens(effectiveFarmId);
      }
      if (allowedTypes.includes('field')) {
        fetchFields(effectiveFarmId);
      }
    }
  }, [effectiveFarmId, allowedTypes]);

  // Combine gardens and fields into unified locations
  const allLocations: Location[] = [
    ...(allowedTypes.includes('garden') ? gardens.map(garden => ({
      id: garden.id,
      name: garden.name,
      type: 'garden' as const,
      size: garden.size,
      sizeUnit: garden.sizeUnit,
      description: garden.description,
    })) : []),
    ...(allowedTypes.includes('field') ? fields.map(field => ({
      id: field.id,
      name: field.name,
      type: 'field' as const,
      size: field.size,
      sizeUnit: field.sizeUnit,
      description: field.description,
    })) : []),
  ];

  // Filter locations based on selected filter
  const filteredLocations = allLocations.filter(location => {
    if (filterType === 'all') return true;
    return location.type === filterType;
  });

  // Find selected location by value
  useEffect(() => {
    if (value) {
      const location = allLocations.find(loc => loc.id === value);
      setSelectedLocation(location || null);
    } else {
      setSelectedLocation(null);
    }
  }, [value, allLocations]);

  const handleLocationSelect = (location: Location) => {
    setSelectedLocation(location);
    onLocationSelect(location);
    setShowModal(false);
  };

  const handleClearSelection = () => {
    setSelectedLocation(null);
    onLocationSelect(null);
  };

  const getLocationIcon = (type: 'garden' | 'field') => {
    return type === 'garden' ? 
      <TreeDeciduous size={16} color={colors.gray[600]} /> :
      <Leaf size={16} color={colors.gray[600]} />;
  };

  const getLocationTypeLabel = (type: 'garden' | 'field') => {
    return type === 'garden' ? t('entity.garden.garden') : t('entity.field.field');
  };

  const renderLocationItem = ({ item }: { item: Location }) => (
    <TouchableOpacity
      style={styles.locationItem}
      onPress={() => handleLocationSelect(item)}
    >
      <View style={[styles.locationItemContent, isRTL && { flexDirection: 'row-reverse' }]}>
        <View style={styles.locationIcon}>
          {getLocationIcon(item.type)}
        </View>
        <View style={styles.locationInfo}>
          <Text style={[styles.locationName, isRTL && { textAlign: 'right' }]}>
            {item.name}
          </Text>
          <Text style={[styles.locationType, isRTL && { textAlign: 'right' }]}>
            {getLocationTypeLabel(item.type)}
            {item.size && ` • ${item.size} ${item.sizeUnit || 'acres'}`}
          </Text>
        </View>
      </View>
      {selectedLocation?.id === item.id && (
        <MaterialIcons name="check" size={20} color={colors.primary} />
      )}
    </TouchableOpacity>
  );

  const renderFilterTabs = () => (
    <View style={[styles.filterTabs, isRTL && { flexDirection: 'row-reverse' }]}>
      <TouchableOpacity
        style={[styles.filterTab, filterType === 'all' && styles.filterTabActive]}
        onPress={() => setFilterType('all')}
      >
        <Text style={[styles.filterTabText, filterType === 'all' && styles.filterTabTextActive]}>
          {t('common.all')}
        </Text>
      </TouchableOpacity>
      
      {allowedTypes.includes('garden') && (
        <TouchableOpacity
          style={[styles.filterTab, filterType === 'garden' && styles.filterTabActive]}
          onPress={() => setFilterType('garden')}
        >
          <Text style={[styles.filterTabText, filterType === 'garden' && styles.filterTabTextActive]}>
            {t('entity.garden.gardens')}
          </Text>
        </TouchableOpacity>
      )}
      
      {allowedTypes.includes('field') && (
        <TouchableOpacity
          style={[styles.filterTab, filterType === 'field' && styles.filterTabActive]}
          onPress={() => setFilterType('field')}
        >
          <Text style={[styles.filterTabText, filterType === 'field' && styles.filterTabTextActive]}>
            {t('entity.field.fields')}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Label */}
      <View style={[styles.labelContainer, isRTL && { flexDirection: 'row-reverse' }]}>
        <Text style={[styles.label, isRTL && { textAlign: 'right' }]}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      </View>

      {/* Selector Button */}
      <TouchableOpacity
        style={[
          styles.selectorButton,
          error && styles.selectorButtonError,
          selectedLocation && styles.selectorButtonSelected,
          isRTL && { flexDirection: 'row-reverse' }
        ]}
        onPress={() => setShowModal(true)}
      >
        <View style={[styles.selectorContent, isRTL && { flexDirection: 'row-reverse' }]}>
          <MapPin size={20} color={selectedLocation ? colors.primary : colors.gray[500]} />
          <View style={styles.selectorTextContainer}>
            {selectedLocation ? (
              <>
                <Text style={[styles.selectedLocationName, isRTL && { textAlign: 'right' }]}>
                  {selectedLocation.name}
                </Text>
                <Text style={[styles.selectedLocationType, isRTL && { textAlign: 'right' }]}>
                  {getLocationTypeLabel(selectedLocation.type)}
                  {selectedLocation.size && ` • ${selectedLocation.size} ${selectedLocation.sizeUnit || 'acres'}`}
                </Text>
              </>
            ) : (
              <Text style={[styles.placeholderText, isRTL && { textAlign: 'right' }]}>
                {placeholder}
              </Text>
            )}
          </View>
        </View>
        
        <View style={[styles.selectorActions, isRTL && { flexDirection: 'row-reverse' }]}>
          {selectedLocation && (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={handleClearSelection}
            >
              <MaterialIcons name="close" size={16} color={colors.gray[500]} />
            </TouchableOpacity>
          )}
          <MaterialIcons name="keyboard-arrow-down" size={20} color={colors.gray[500]} />
        </View>
      </TouchableOpacity>

      {/* Error Message */}
      {error && (
        <Text style={[styles.errorText, isRTL && { textAlign: 'right' }]}>
          {error}
        </Text>
      )}

      {/* Selection Modal */}
      <Modal
        visible={showModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {/* Modal Header */}
            <View style={[styles.modalHeader, isRTL && { flexDirection: 'row-reverse' }]}>
              <Text style={[styles.modalTitle, isRTL && { textAlign: 'right' }]}>
                {label}
              </Text>
              <TouchableOpacity onPress={() => setShowModal(false)}>
                <MaterialIcons name="close" size={24} color={colors.gray[600]} />
              </TouchableOpacity>
            </View>

            {/* Filter Tabs */}
            {allowedTypes.length > 1 && renderFilterTabs()}

            {/* Locations List */}
            {filteredLocations.length > 0 ? (
              <FlatList
                data={filteredLocations}
                renderItem={renderLocationItem}
                keyExtractor={item => item.id}
                style={styles.locationsList}
                showsVerticalScrollIndicator={false}
              />
            ) : (
              <View style={styles.emptyState}>
                <MapPin size={40} color={colors.gray[400]} />
                <Text style={[styles.emptyStateText, isRTL && { textAlign: 'right' }]}>
                  {filterType === 'all' 
                    ? t('location.noLocationsAvailable')
                    : filterType === 'garden'
                    ? t('entity.garden.noGardensAvailable')
                    : t('entity.field.noFieldsAvailable')
                  }
                </Text>
                
                {showCreateOption && (
                  <TouchableOpacity
                    style={styles.createButton}
                    onPress={() => {
                      setShowModal(false);
                      if (filterType === 'garden' || (filterType === 'all' && allowedTypes.includes('garden'))) {
                        router.push('/garden/create');
                      } else {
                        router.push('/field/create');
                      }
                    }}
                  >
                    <Plus size={16} color={colors.white} />
                    <Text style={styles.createButtonText}>
                      {filterType === 'garden' 
                        ? t('add.garden')
                        : filterType === 'field'
                        ? t('add.field')
                        : t('location.createLocation')
                      }
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
  },
  required: {
    color: colors.danger,
  },
  selectorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: colors.white,
  },
  selectorButtonError: {
    borderColor: colors.danger,
  },
  selectorButtonSelected: {
    borderColor: colors.primary,
  },
  selectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  selectorTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  selectedLocationName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
  },
  selectedLocationType: {
    fontSize: 14,
    color: colors.gray[600],
    marginTop: 2,
  },
  placeholderText: {
    fontSize: 16,
    color: colors.gray[500],
  },
  selectorActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  clearButton: {
    padding: 4,
    marginRight: 8,
  },
  errorText: {
    fontSize: 14,
    color: colors.danger,
    marginTop: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  filterTabs: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  filterTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: colors.gray[100],
  },
  filterTabActive: {
    backgroundColor: colors.primary,
  },
  filterTabText: {
    fontSize: 14,
    color: colors.gray[600],
    fontWeight: '500',
  },
  filterTabTextActive: {
    color: colors.white,
  },
  locationsList: {
    paddingHorizontal: 20,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[100],
  },
  locationItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  locationIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  locationInfo: {
    flex: 1,
  },
  locationName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
  },
  locationType: {
    fontSize: 14,
    color: colors.gray[600],
    marginTop: 2,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyStateText: {
    fontSize: 16,
    color: colors.gray[600],
    marginTop: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  createButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
    marginLeft: 8,
  },
});
