import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  Farm, Field, Task, InventoryItem, WeatherForecast,
  Garden, Plant, Animal, Equipment, Yield, User, FarmPermission,
  Crop,
  UserType,
  HarvestRecord,
  AnimalCost,
  AnimalMilkRecord,
  AnimalCleanliness,
  FinanceRecord,
  FinanceSummary,
  MonthlyFinanceData,
  ChecklistItem,
  EquipmentChecklistItem,
  ChecklistRecord,
  AnimalHealthCheck,
  AnimalPregnancy,
  AnimalRecord,
  Machinery,
  MaintenanceRecord
} from '@/types';
import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDocs,
  query,
  where,
  serverTimestamp,
  Timestamp,
  getDoc,
  setDoc,
  arrayUnion,
  onSnapshot,
  orderBy
} from 'firebase/firestore';
import { firestore } from '@/firebase/config';
import { useAuthStore } from './auth-store';
// import { UserType } from '@/services/user-service';
import { useLookupStore } from './lookup-store';
import { saveSelectedFarm, getSelectedFarm, clearSelectedFarm } from '@/utils/farmPersistence';
import { TransactionCategory, Transaction, CropFinancials } from '@/types';
// import { analyzeTransactionWithAI } from '@/utils/ai-utils';

import { v4 as uuidv4 } from 'uuid';
import { analyzeTransactionWithAI } from '@/utils/ai-utils';
import { uploadImageAsync } from '@/utils/firebase-storage';
import { logEvent } from './logging';
// import { useLookupStore  } from './lookup-store';
interface FarmState {
  farms: Farm[];
  fields: Field[];
  tasks: Task[];
  inventory: InventoryItem[];
  inventoryEquipment: any[]; // Equipment and tools from inventory collection
  weather: WeatherForecast[];
  gardens: Garden[];
  plants: Plant[];
  animals: Animal[];
  equipment: Equipment[];
  machinery: Machinery[]; // Specialized farm machinery
  yields: Yield[];
  farmPermissions: FarmPermission[];
  currentFarm: Farm | null;
  isLoading: boolean;
  error: string | null;
  animalCheckList: any[];

  // Animal subcollections
  animalHealthChecks: AnimalHealthCheck[];
  animalPregnancies: AnimalPregnancy[];
  animalRecords: AnimalRecord[];

  // Machinery maintenance
  maintenanceRecords: MaintenanceRecord[];
  // Add these functions to the FarmState interface
  markPlantInactive: (plantId: string, data: {
    reason: string;
    notes?: string;
    image?: string;
  }) => Promise<void>;
  generateRecurringTasks: (baseTask: any, frequency: string, repeatUntil: Date) => any[];

  markAnimalInactive: (animalId: string, data: {
    reason: string;
    notes?: string;
    image?: string;
  }) => Promise<void>;

  markEquipmentInactive: (equipmentId: string, data: {
    reason: string;
    notes?: string;
    image?: string;
  }) => Promise<void>;

  markGardenInactive: (gardenId: string, data: {
    reason: string;
    notes?: string;
    image?: string;
    cascadeToPlants: boolean;
  }) => Promise<void>;

  markFieldInactive: (fieldId: string, data: {
    reason: string;
    notes?: string;
    image?: string;
  }) => Promise<void>;

  recordFieldHarvest: (fieldId: string, data: {
    harvestDate: string;
    season: string;
    yieldQuantity: number;
    yieldUnit: string;
    cropType: string;
    notes?: string;
    images?: string[];
  }) => Promise<string>;

  getFieldHarvestHistory: (fieldId: string) => Promise<HarvestRecord[]>;

  // Add to FarmState interface
  financeRecords: FinanceRecord[];
  financeSummary: FinanceSummary;
  resetState: () => void; // Add resetState to the interface
  monthlyFinanceData: MonthlyFinanceData;

  // Add finance methods to FarmState interface
  addFinanceRecord: (data: Omit<FinanceRecord, 'id' | 'farmId' | 'createdAt' | 'createdBy'>) => Promise<string>;
  fetchFinanceRecords: (filters?: {
    entityType?: string;
    entityId?: string;
    transactionType?: 'income' | 'expense' | 'all';
    category?: string;
    currency?: string;
    startDate?: Date;
    endDate?: Date;
  }) => Promise<FinanceRecord[]>;
  getEntityName: (entityType: string, entityId: string) => Promise<string>;
  exportFinanceRecordsToCSV: () => Promise<string>;
  // generateChartData: (records: FinanceRecord[], period: 'weekly' | 'monthly' | 'yearly') => Promise<{}>


  // Fetch data
  fetchFarms: (userId: string) => Promise<void>;
  fetchFields: (farmId: string) => Promise<void>;
  fetchTasks: (farmId: string) => Promise<void>;
  fetchInventory: (farmId: string) => Promise<void>;
  fetchWeather: (location: string) => Promise<void>;
  fetchGardens: (farmId: string) => Promise<void>;
  fetchPlants: (farmId: string) => Promise<void>;
  fetchAnimals: (farmId: string) => Promise<void>;
  fetchEquipment: (farmId: string) => Promise<void>;
  fetchYields: (farmId: string) => Promise<void>;
  fetchFarmPermissions: (farmId: string) => Promise<void>;

  // Get single entity
  getAnimal: (id: string) => Animal | undefined;
  getPlant: (id: string) => Plant | undefined;
  getField: (id: string) => Field | undefined;
  getGarden: (id: string) => Garden | undefined;
  getGardenUpdated: (farmId: string, gardenId: string) => Promise<Garden | undefined | null>;
  getFieldUpdated: (farmId: string, fieldId: string) => Promise<Field | undefined | null>;
  getPlantUpdated: (farmId: string, plantId: string) => Promise<Plant | undefined | null>;
  getEquipmentUpdated: (farmId: string, equipmentId: string) => Promise<Equipment | undefined | null>;
  getAnimalUpdated: (farmId: string, animalId: string) => Promise<Animal | undefined | null>;

  getEquipment: (id: string) => Equipment | undefined;
  getYield: (id: string) => Yield | undefined;
  getTask: (id: string) => Task | undefined;
  getCrop: (Id: string) => Crop | undefined;

  // Farm actions
  setCurrentFarm: (farmId: string) => void;
  setCurrentFarmWithPersistence: (farmId: string | null) => Promise<void>;
  loadPersistedFarm: () => Promise<void>;
  clearPersistedFarm: () => Promise<void>;
  addFarm: (farm: Partial<Farm>) => Promise<string>;
  updateFarm: (farmId: string, data: Partial<Farm>) => Promise<void>;
  deleteFarm: (farmId: string) => Promise<void>;

  // Field actions
  addField: (field: Partial<Field>) => Promise<string>;
  updateField: (fieldId: string, data: Partial<Field>) => Promise<void>;
  deleteField: (fieldId: string) => Promise<void>;

  // Task actions
  addTask: (task: Partial<Task>) => Promise<string>;
  updateTask: (taskId: string, data: Partial<Task>) => Promise<void>;
  deleteTask: (taskId: string) => Promise<void>;
  completeTask: (taskId: string) => Promise<void>;

  // Inventory actions
  fetchEquipmentFromInventory: (farmId: string) => Promise<any[]>;
  addInventoryItem: (item: Partial<InventoryItem>) => Promise<string>;
  updateInventoryItem: (itemId: string, data: Partial<InventoryItem>) => Promise<void>;
  deleteInventoryItem: (itemId: string) => Promise<void>;

  // Garden actions
  addGarden: (garden: Partial<Garden>) => Promise<string>;
  updateGarden: (gardenId: string, data: Partial<Garden>) => Promise<void>;
  deleteGarden: (gardenId: string) => Promise<void>;

  // Plant actions
  addPlant: (plant: Partial<Plant>) => Promise<string>;
  updatePlant: (plantId: string, data: Partial<Plant>) => Promise<void>;
  deletePlant: (plantId: string) => Promise<void>;

  // Animal actions
  addAnimal: (animal: Partial<Animal>) => Promise<string>;
  updateAnimal: (animalId: string, data: Partial<Animal>) => Promise<void>;
  deleteAnimal: (animalId: string) => Promise<void>;

  // Equipment actions
  addEquipment: (equipment: Partial<Equipment>) => Promise<string>;
  updateEquipment: (equipmentId: string, data: Partial<Equipment>) => Promise<void>;
  deleteEquipment: (equipmentId: string) => Promise<void>;

  // Yield actions
  addYield: (yieldData: Partial<Yield>) => Promise<string>;
  updateYield: (yieldId: string, data: Partial<Yield>) => Promise<void>;
  deleteYield: (yieldId: string) => Promise<void>;

  // Farm Permission actions
  addFarmPermission: (permission: Partial<FarmPermission>) => Promise<string>;
  updateFarmPermission: (permissionId: string, data: Partial<FarmPermission>) => Promise<void>;
  deleteFarmPermission: (permissionId: string) => Promise<void>;

  // User management
  inviteUserToFarm: (farmId: string, email: string, phone: string, role: 'admin' | 'caretaker', name: string, newUserId: string, updatedAssignedFarms: any) => Promise<void>;
  removeUserFromFarm: (farmId: string, userId: string) => Promise<void>;

  // Add to FarmState interface
  crops: Crop[];
  getFieldCrops: (fieldId: string) => Promise<{ activeCrop: Crop | null, crops: Crop[] }>;
  addCropToField: (fieldId: string, cropData: any) => Promise<void>;
  updateCrop: (cropId: string, cropData: any) => Promise<void>;
  markCropAsHarvested: (cropId: string, harvestData: any) => Promise<void>;
  getCrop: (cropId: string) => Crop | undefined; // Get crop from local state
  getCropById: (fieldId: string, cropId: string) => Promise<Crop | null>; // Get crop from Firestore


  getUsersByFarm: (farmId: string) => Promise<User[]>;
  getUserById: (userId: string) => Promise<User | null>;
  // getUsersByFarm: (farmId: string) => UserType[] | null;
  // getUsersByFarm: (farmId: string) => Promise<UserType | null>;
  // Add new state properties
  animalCosts: AnimalCost[];
  animalMilkRecords: AnimalMilkRecord[];
  animalCleanlinessRecords: AnimalCleanliness[];

  // Add new functions
  addAnimalCost: (animalId: string, data: Omit<AnimalCost, 'id' | 'animal_id' | 'farmId' | 'createdAt' | 'createdBy'>) => Promise<string>;
  getAnimalCosts: (animalId: string) => Promise<AnimalCost[]>;

  addAnimalMilkRecord: (animalId: string, data: Omit<AnimalMilkRecord, 'id' | 'animal_id' | 'farmId' | 'createdAt' | 'createdBy'>) => Promise<string>;
  getAnimalMilkRecords: (animalId: string) => Promise<AnimalMilkRecord[]>;

  addAnimalCleanlinessRecord: (animalId: string, data: Omit<AnimalCleanliness, 'id' | 'animal_id' | 'farmId' | 'createdAt' | 'createdBy'>) => Promise<string>;
  getAnimalCleanlinessRecords: (animalId: string) => Promise<AnimalCleanliness[]>;

  // Fix: Correct signature for saveAnimalChecklist
  saveAnimalChecklist: (farmId: string, animalId: string, items: ChecklistItem[], userId: string) => Promise<void>;
  getAnimalChecklists: (farmId: string, animalId: string) => Promise<ChecklistItem>;
  saveEquipmentChecklist: (farmId: string, equipmentId: string, items: EquipmentChecklistItem[], userId: string) => Promise<void>
  getEquipmentChecklists: (farmId: string, equipmentId: string) => Promise<EquipmentChecklistItem>;
  generateInventoryStatusReport: (farmId: string, title: string) => Promise<any>;
  getDailyReport: (farmId: string, selectedDate: Date, title: string) => Promise<any>;
  getReportById: (reportId: string) => Promise<any>;

  // Animal subcollection methods
  fetchAnimalHealthChecks: (animalId: string) => Promise<AnimalHealthCheck[]>;
  addAnimalHealthCheck: (animalId: string, healthCheckData: Omit<AnimalHealthCheck, 'id' | 'animalId' | 'createdAt' | 'updatedAt'>) => Promise<string>;
  fetchAnimalPregnancies: (animalId: string) => Promise<AnimalPregnancy[]>;
  addAnimalPregnancy: (animalId: string, pregnancyData: Omit<AnimalPregnancy, 'id' | 'animalId' | 'createdAt' | 'updatedAt'>) => Promise<string>;
  fetchAnimalRecords: (animalId: string) => Promise<AnimalRecord[]>;
  addAnimalRecord: (animalId: string, recordData: Omit<AnimalRecord, 'id' | 'animalId' | 'createdAt' | 'updatedAt'>) => Promise<string>;

  // Machinery management methods
  fetchMachinery: () => Promise<Machinery[]>;
  addMachinery: (machineryData: Omit<Machinery, 'id' | 'createdAt' | 'updatedAt'>) => Promise<string>;
  updateMachinery: (machineryId: string, updates: Partial<Machinery>) => Promise<void>;
  deleteMachinery: (machineryId: string) => Promise<void>;
  fetchMaintenanceRecords: (machineryId: string) => Promise<MaintenanceRecord[]>;
  addMaintenanceRecord: (machineryId: string, recordData: Omit<MaintenanceRecord, 'id' | 'machineryId' | 'createdAt'>) => Promise<string>;
  // savePlantChecklist: (farmId: string, plantId: string, userId: string, checklistItems: any) => Promise<any>
  saveChecklist: (farmId: string, data: any) => Promise<any>;


  fetchChecklists: (category?: string) => Promise<any>;
  getTodayChecklist: (farmId: string, plantId: string) => Promise<any>
  listeners: (() => void)[];
  isRealTimeEnabled: boolean;

  // Add these methods
  enableRealTimeSync: (farmId: string) => void;
  disableRealTimeSync: () => void;
  setupCollectionListener: (collectionPath: string, callback: (data: any[]) => void) => () => void;
}

export const useFarmStore = create<FarmState>()(
  persist(
    (set, get) => ({
      farms: [],
      fields: [],
      tasks: [],
      inventory: [],
      inventoryEquipment: [],
      weather: [],
      gardens: [],
      plants: [],
      animals: [],
      equipment: [],
      yields: [],
      farmPermissions: [],
      currentFarm: null,
      isLoading: false,
      error: null,
      crops: [],
      // Add to the store state
      cropTransactions: [] as Transaction[],
      cropFinancials: {} as Record<string, CropFinancials>,
      animalCosts: [],
      animalMilkRecords: [],
      animalCleanlinessRecords: [],
      animalCheckList: [] as ChecklistItem[],

      // Animal subcollections
      animalHealthChecks: [],
      animalPregnancies: [],
      animalRecords: [],

      // Machinery
      machinery: [],
      maintenanceRecords: [],

      //////
      financeRecords: [],
      financeSummary: {
        totalIncome: 0,
        totalExpense: 0,
        netBalance: 0
      },
      monthlyFinanceData: {
        labels: [],
        income: [],
        expense: []
      },


      listeners: [],
      isRealTimeEnabled: false,
      // Enable real-time synchronization for a farm
      enableRealTimeSync: (farmId: string) => {
        const state = get();

        // Disable existing listeners first
        state.disableRealTimeSync();

        const newListeners: (() => void)[] = [];

        // Tasks listener
        const tasksListener = onSnapshot(
          query(collection(firestore, `farms/${farmId}/tasks`)),
          (snapshot) => {
            const tasksData = snapshot.docs.map(doc => {
              const data = doc.data();
              // Apply lookups to task data
              const priority = getLookupTitleLocal(data.priority);
              const category = getLookupTitleLocal(data.category);

              return {
                id: doc.id,
                title: data.title || '',
                description: data.description || '',
                status: data.status || 'pending',
                priority: priority || data.priority || '',
                category: category || data.category || '',
                assignedTo: data.assignedTo || '',
                assignedBy: data.assignedBy || '',
                farmId: data.farmId || '',
                dueDate: data.dueDate?.toDate?.()?.toISOString() || data.dueDate || '',
                createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt || new Date().toISOString(),
                updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt || new Date().toISOString(),
                completedDate: data.completedDate?.toDate?.()?.toISOString() || data.completedDate,
                checklistDetails: data.checklistDetails || [],
                attachments: data.attachments || [],
                notes: data.notes || '',
              };
            });

            // Simple replacement - let Firestore handle deduplication
            set({ tasks: tasksData });
            logEvent('data_sync', { collection: 'tasks', count: tasksData.length });
          },
          (error) => {
            console.error('Tasks listener error:', error);
            set({ error: 'Failed to sync tasks data' });
          }
        );
        newListeners.push(tasksListener);

        // Plants listener
        const plantsListener = onSnapshot(
          query(collection(firestore, `farms/${farmId}/plants`)),
          async (snapshot) => {
            const plantsData = [];

            for (const doc of snapshot.docs) {
              const data = doc.data();
              const plantId = doc.id;

              // Get lookup values
              const growthStage = getLookupTitleLocal(data.growthStage);
              const healthStatus = getLookupTitleLocal(data.healthStatus);

              plantsData.push({
                id: plantId,
                ...data,
                growthStage,
                healthStatus,
                plantedDate: data.plantedDate?.toDate?.()?.toISOString() || data.plantedDate,
                expectedHarvestDate: data.expectedHarvestDate?.toDate?.()?.toISOString() || data.expectedHarvestDate,
                createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
                updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
              });
            }

            set({ plants: plantsData });
            logEvent('data_sync', { collection: 'plants', count: plantsData.length });
          },
          (error) => {
            console.error('Plants listener error:', error);
            set({ error: 'Failed to sync plants data' });
          }
        );
        newListeners.push(plantsListener);

        // Animals listener
        const animalsListener = onSnapshot(
          query(collection(firestore, `farms/${farmId}/animals`)),
          (snapshot) => {
            const animalsData = snapshot.docs.map(doc => {
              const data = doc.data();
              return {
                id: doc.id,
                ...data,
                birthDate: data.birthDate?.toDate?.()?.toISOString() || data.birthDate,
                createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
                updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
              };
            });
            set({ animals: animalsData });
            logEvent('data_sync', { collection: 'animals', count: animalsData.length });
          },
          (error) => {
            console.error('Animals listener error:', error);
            set({ error: 'Failed to sync animals data' });
          }
        );
        newListeners.push(animalsListener);

        // Gardens listener (zones with type 'garden')
        const gardensListener = onSnapshot(
          query(
            collection(firestore, `farms/${farmId}/zones`),
            where('type', '==', 'garden')
          ),
          (snapshot) => {
            const gardensData = snapshot.docs.map(doc => {
              const data = doc.data();
              // Apply lookups to garden data
              const gardenType = getLookupTitleLocal(data.gardenType);
              const status = getLookupTitleLocal(data.status);
              const sizeUnit = getLookupTitleLocal(data.sizeUnit);
              const soilType = getLookupTitleLocal(data.soilType);
              const irrigationSystem = getLookupTitleLocal(data.irrigationSystem);

              return {
                id: doc.id,
                name: data.name,
                type: data.type,
                gardenType: gardenType || data?.gardenType,
                size: data.size,
                sizeUnit: sizeUnit || data.sizeUnit,
                location: data.location || undefined,
                status: status || data.status,
                soilType: soilType || data.soilType || undefined,
                irrigationSystem: irrigationSystem || data.irrigationSystem || undefined,
                farmId: farmId,
                createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
                updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
                image: data.photoURL || undefined,
                isInactive: data?.isInactive || false,
                inactiveReason: data?.inactiveReason || undefined,
                inactiveDate: data.inactiveDate?.toDate?.() ? data.inactiveDate.toDate().toISOString() : new Date().toISOString(),
              };
            });
            set({ gardens: gardensData });
            logEvent('data_sync', { collection: 'gardens', count: gardensData.length });
          },
          (error) => {
            console.error('Gardens listener error:', error);
            set({ error: 'Failed to sync gardens data' });
          }
        );
        newListeners.push(gardensListener);

        // Fields listener (zones with type 'field')
        const fieldsListener = onSnapshot(
          query(
            collection(firestore, `farms/${farmId}/zones`),
            where('type', '==', 'field')
          ),
          (snapshot) => {
            const fieldsData = snapshot.docs.map(doc => {
              const data = doc.data();
              // Apply lookups to field data
              const sizeUnit = getLookupTitleLocal(data.sizeUnit);
              const type = getLookupTitleLocal(data.type);
              const status = getLookupTitleLocal(data.status);

              return {
                id: doc.id,
                name: data.name,
                type: type || data.type,
                size: data.size,
                sizeUnit: sizeUnit || data.sizeUnit,
                status: status || data.status,
                activeCropId: data?.activeCropId,
                plantedDate: data.plantedDate ?
                  (data.plantedDate.toDate ? data.plantedDate.toDate().toISOString() : new Date(data.plantedDate).toISOString())
                  : undefined,
                harvestDate: data.harvestDate ?
                  (data.harvestDate.toDate ? data.harvestDate.toDate().toISOString() : new Date(data.harvestDate).toISOString())
                  : undefined,
                farmId: farmId,
                location: data.location || undefined,
                soilType: data.soilType || undefined,
                createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
                updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
                image: data.photoURL || undefined,
                isInactive: data?.isInactive || false,
                inactiveReason: data?.inactiveReason || undefined,
                inactiveDate: data.inactiveDate?.toDate?.() ? data.inactiveDate.toDate().toISOString() : new Date().toISOString(),
              };
            });
            set({ fields: fieldsData });
            logEvent('data_sync', { collection: 'fields', count: fieldsData.length });
          },
          (error) => {
            console.error('Fields listener error:', error);
            set({ error: 'Failed to sync fields data' });
          }
        );
        newListeners.push(fieldsListener);

        // Equipment listener
        const equipmentListener = onSnapshot(
          query(collection(firestore, `farms/${farmId}/equipment`)),
          (snapshot) => {
            const equipmentData = snapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data(),
              purchaseDate: doc.data().purchaseDate?.toDate?.()?.toISOString() || doc.data().purchaseDate,
              createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
              updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
            }));
            set({ equipment: equipmentData });
            logEvent('data_sync', { collection: 'equipment', count: equipmentData.length });
          },
          (error) => {
            console.error('Equipment listener error:', error);
            set({ error: 'Failed to sync equipment data' });
          }
        );
        newListeners.push(equipmentListener);

        // Inventory listener
        const inventoryListener = onSnapshot(
          query(collection(firestore, `farms/${farmId}/inventory`)),
          (snapshot) => {
            const inventoryData = snapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data(),
              createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
              updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
            }));
            set({ inventory: inventoryData });
            logEvent('data_sync', { collection: 'inventory', count: inventoryData.length });
          },
          (error) => {
            console.error('Inventory listener error:', error);
            set({ error: 'Failed to sync inventory data' });
          }
        );
        newListeners.push(inventoryListener);

        // Activities listener for real-time activity updates
        const activitiesListener = onSnapshot(
          query(
            collection(firestore, `farms/${farmId}/activities`),
            orderBy('CreatedAt', 'desc')
          ),
          (snapshot) => {
            // This can trigger UI updates for activity feeds
            logEvent('data_sync', { collection: 'activities', count: snapshot.docs.length });
          },
          (error) => {
            console.error('Activities listener error:', error);
          }
        );
        newListeners.push(activitiesListener);

        set({
          listeners: newListeners,
          isRealTimeEnabled: true,
          error: null
        });

        logEvent('realtime_sync_enabled', { farmId, listenersCount: newListeners.length });
      },

      // Disable real-time synchronization
      disableRealTimeSync: () => {
        const { listeners } = get();

        listeners.forEach(unsubscribe => {
          if (typeof unsubscribe === 'function') {
            unsubscribe();
          }
        });

        set({
          listeners: [],
          isRealTimeEnabled: false
        });

        logEvent('realtime_sync_disabled', {});
      },

      // Generic collection listener setup
      setupCollectionListener: (collectionPath: string, callback: (data: any[]) => void) => {
        const unsubscribe = onSnapshot(
          query(collection(firestore, collectionPath)),
          (snapshot) => {
            const data = snapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            }));
            callback(data);
          },
          (error) => {
            console.error(`Listener error for ${collectionPath}:`, error);
          }
        );

        return unsubscribe;
      },

      // Add method to listen to subcollections
      setupSubcollectionListeners: (farmId: string) => {
        const newListeners: (() => void)[] = [];

        // Plant health check listener
        const plantHealthListener = onSnapshot(
          query(collection(firestore, `farms/${farmId}/DailyPlantHealthCheck`)),
          (snapshot) => {
            // Trigger plant data refresh when health checks are updated
            get().fetchPlants(farmId);
            logEvent('data_sync', { collection: 'plant_health_checks', count: snapshot.docs.length });
          },
          (error) => {
            console.error('Plant health listener error:', error);
          }
        );
        newListeners.push(plantHealthListener);

        // Animal checklist listener
        const animalChecklistListener = onSnapshot(
          query(collection(firestore, `farms/${farmId}/animals-checklist`)),
          (snapshot) => {
            // Trigger animal data refresh when checklists are updated
            get().fetchAnimals(farmId);
            logEvent('data_sync', { collection: 'animal_checklists', count: snapshot.docs.length });
          },
          (error) => {
            console.error('Animal checklist listener error:', error);
          }
        );
        newListeners.push(animalChecklistListener);

        // Crop transactions listener
        const cropTransactionsListener = onSnapshot(
          query(collection(firestore, `farms/${farmId}/plant_transactions`)),
          (snapshot) => {
            logEvent('data_sync', { collection: 'crop_transactions', count: snapshot.docs.length });
          },
          (error) => {
            console.error('Crop transactions listener error:', error);
          }
        );
        newListeners.push(cropTransactionsListener);

        // Harvest history listener for fields
        const harvestListener = get().setupCollectionListener(
          `farms/${farmId}/zones`,
          (zones) => {
            // Listen to harvest history for each field
            zones.filter(zone => zone.type === 'field').forEach(field => {
              const harvestHistoryListener = onSnapshot(
                query(collection(firestore, `farms/${farmId}/zones/${field.id}/harvestHistory`)),
                (snapshot) => {
                  // Update field data when harvest history changes
                  get().fetchFields(farmId);
                  logEvent('data_sync', {
                    collection: 'harvest_history',
                    fieldId: field.id,
                    count: snapshot.docs.length
                  });
                },
                (error) => {
                  console.error(`Harvest history listener error for field ${field.id}:`, error);
                }
              );
              newListeners.push(harvestHistoryListener);
            });
          }
        );

        return newListeners;
      },
      // New action to reset the state
      resetState: () => {
        set({
          farms: [],
          fields: [],
          tasks: [],
          inventory: [],
          weather: [],
          gardens: [],
          plants: [],
          animals: [],
          equipment: [],
          yields: [],
          farmPermissions: [],
          currentFarm: null,
          isLoading: false,
          error: null,
          crops: [],
          animalCosts: [],
          animalMilkRecords: [],
          animalCleanlinessRecords: [],
          animalCheckList: [],
          financeRecords: [],
          financeSummary: { totalIncome: 0, totalExpense: 0, netBalance: 0 },
          monthlyFinanceData: { labels: [], income: [], expense: [] },
        });
      },
      // Get single entity functions
      getAnimal: (id: string) => {
        return get().animals.find(animal => animal.id === id);
      },

      getPlant: (id: string) => {
        return get().plants.find(plant => plant.id === id);
      },

      getField: (id: string) => {
        return get().fields.find(field => field.id === id);
      },

      getGarden: (id: string) => {
        return get().gardens.find(garden => garden.id === id);
      },

      getEquipment: (id: string) => {
        return get().equipment.find(equip => equip.id === id);
      },

      getYield: (id: string) => {
        return get().yields.find(yieldItem => yieldItem.id === id);
      },

      getTask: (id: string) => {
        return get().tasks.find(task => task.id === id);
      },
      getCrop: (id: string) => {
        return get().crops.find(crop => crop.id === id);
      },




      getUserById: async (userId: string): Promise<User | null> => {
        try {
          const userRef = doc(firestore, 'users', userId);
          const userSnap = await getDoc(userRef);

          if (userSnap.exists()) {
            const userData = userSnap.data();
            return {
              id: userSnap.id,
              name: userData.name,
              displayName: userData.displayName,
              email: userData.email,
              role: userData.role,
              status: userData.status,
              photoURL: userData.photoURL,
            };
          }

          return null;
        } catch (error) {
          console.error('Error fetching user by ID:', error);
          throw error;
        }
      },
      getUsersByFarm: async (farmId: string): Promise<User[]> => {
        try {
          const farmUsersRef = collection(firestore, 'farm_users');
          const q = query(farmUsersRef, where('farmId', '==', farmId));
          const querySnapshot = await getDocs(q);

          const userIds = querySnapshot.docs.map(doc => doc.data().userId);
          const users: UserType[] = [];

          for (const userId of userIds) {
            const user = await getUserById(userId);
            if (user) {
              users.push(user);
            }
          }

          return users;
        } catch (error) {
          console.error('Error fetching users by farm:', error);
          throw error;
        }
      },

      ///Get From live functions
      getGardenUpdated: async (farmId, gardenId) => {
        try {
          set({ isLoading: true })
          // const docRef = doc(firestroe, `farms/${farmId}/zones`, gardenId);
          // const snapshot = await getDoc(docRef);
          const snapshot = await getDoc(doc(firestore, `farms/${farmId}/zones`, gardenId));
          // if (farmDoc.exists()) {
          if (!snapshot.exists()) {
            console.warn(`No garden task found with id: ${gardenId}`);
            return null;
          }

          return {
            id: snapshot.id,
            ...snapshot.data()
          } as Garden;
        } catch (error) {
          console.error("Error fetching garden task from Firestore:", error);
          throw error;
        } finally {
          set({ isLoading: false });
        }

      },

      getFieldUpdated: async (farmId, fieldId) => {
        try {
          set({ isLoading: true });
          const snapshot = await getDoc(doc(firestore, `farms/${farmId}/zones`, fieldId));
          if (!snapshot.exists()) {
            console.warn(`No field found with id: ${fieldId}`);
            return null;
          }
          return {
            id: snapshot.id,
            ...snapshot.data()
          } as Field;
        } catch (error) {
          console.error("Error fetching field from Firestore:", error);
          throw error;
        } finally {
          set({ isLoading: false });
        }
      },

      getPlantUpdated: async (farmId, plantId) => {
        try {
          set({ isLoading: true });
          const snapshot = await getDoc(doc(firestore, `farms/${farmId}/plants`, plantId));
          if (!snapshot.exists()) {
            console.warn(`No plant found with id: ${plantId}`);
            return null;
          }

          const data = snapshot.data();
          const growthStage = getLookupTitleLocal(data.growthStage);
          const healthStatus = getLookupTitleLocal(data.healthStatus);

          // Fetch the latest health check data for this plant
          const healthCheckRef = collection(firestore, `farms/${farmId}/DailyPlantHealthCheck`);
          const healthCheckQuery = query(
            healthCheckRef,
            where('plantId', '==', plantId),
            orderBy('date', 'desc'),
            limit(1)
          );

          const healthCheckSnapshot = await getDocs(healthCheckQuery);
          let latestHealthCheck = null;

          if (!healthCheckSnapshot.empty) {
            latestHealthCheck = {
              id: healthCheckSnapshot.docs[0].id,
              ...healthCheckSnapshot.docs[0].data()
            };
          }

          // Also fetch all health checks for history
          const allHealthChecksQuery = query(
            healthCheckRef,
            where('plantId', '==', plantId),
            orderBy('date', 'desc')
          );

          const allHealthChecksSnapshot = await getDocs(allHealthChecksQuery);
          const healthChecks = allHealthChecksSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));

          return {
            id: snapshot.id,
            ...data,
            status: growthStage || data.growthStage,
            health: healthStatus || data.healthStatus,
            healthCheck: latestHealthCheck,
            healthChecks: healthChecks
          } as Plant;
        } catch (error) {
          console.error("Error fetching plant from Firestore:", error);
          throw error;
        } finally {
          set({ isLoading: false });
        }
      },

      getEquipmentUpdated: async (farmId, equipmentId) => {
        try {
          set({ isLoading: true });
          const snapshot = await getDoc(doc(firestore, `farms/${farmId}/machinery`, equipmentId));
          if (!snapshot.exists()) {
            console.warn(`No equipment found with id: ${equipmentId}`);
            return null;
          }
          return {
            id: snapshot.id,
            ...snapshot.data()
          } as Equipment;
        } catch (error) {
          console.error("Error fetching equipment from Firestore:", error);
          throw error;
        } finally {
          set({ isLoading: false });
        }
      },
      getAnimalUpdated: async (farmId, animalId) => {
        try {
          set({ isLoading: true });
          const snapshot = await getDoc(doc(firestore, `farms/${farmId}/animals`, animalId));
          if (!snapshot.exists()) {
            console.warn(`No animal found with id: ${animalId}`);
            return null;
          }
          return {
            id: snapshot.id,
            ...snapshot.data()
          } as Animal;
        } catch (error) {
          console.error("Error fetching animal from Firestore:", error);
          throw error;
        } finally {
          set({ isLoading: false });
        }
      },










      // fetchFarms: async (userId) => {
      //   set({ isLoading: true, error: null });

      //   try {
      //     // Get user document to access assignedFarmIds
      //     const userDoc = await getDoc(doc(firestore, 'users', userId));

      //     if (!userDoc.exists()) {
      //       set({
      //         farms: [],
      //         currentFarm: null,
      //         isLoading: false
      //       });
      //       return;
      //     }

      //     const userData = userDoc.data();
      //     const assignedFarmIds = userData.assignedFarmIds || [];

      //     if (assignedFarmIds.length === 0) {
      //       set({
      //         farms: [],
      //         currentFarm: null,
      //         isLoading: false
      //       });
      //       return;
      //     }

      //     // Fetch farm details for each farmId in assignedFarmIds
      //     const farmsData: Farm[] = [];

      //     for (const farmId of assignedFarmIds) {
      //       const farmDoc = await getDoc(doc(firestore, 'farms', farmId));

      //       if (farmDoc.exists()) {
      //         const data = farmDoc.data();
      //         farmsData.push({
      //           id: farmDoc.id,
      //           name: data.name,
      //           description: data.description || '',
      //           location: data.location?.address || '',
      //           size: data.size,
      //           sizeUnit: data.sizeUnit,
      //           type: data.type || 'mixed',
      //           ownerId: data.ownerId,
      //           status: data.status || 'active',
      //           image: data.photoURL || '',
      //           createdAt: data.createdAt?.toDate?.() ? data.createdAt.toDate().toISOString() : new Date().toISOString(),
      //           updatedAt: data.updatedAt?.toDate?.() ? data.updatedAt.toDate().toISOString() : new Date().toISOString(),
      //         });
      //       }
      //     }

      //     set({
      //       farms: farmsData,
      //       currentFarm: farmsData.length > 0 ? farmsData[0] : null,
      //       isLoading: false
      //     });
      //   } catch (error: any) {
      //     console.error('Fetch farms error:', error);
      //     set({
      //       error: error.message || 'Failed to fetch farms',
      //       isLoading: false
      //     });
      //   }
      // },



 // Gallery management functions
      loadEntityGallery: async (farmId: string, entityId: string, entityType: string) => {
        try {
          const galleryRef = collection(firestore, `farms/${farmId}/gallery`);
          const q = query(
            galleryRef, 
            where('entityId', '==', entityId),
            where('entityType', '==', entityType)
          );
          const snapshot = await getDocs(q);
          
          const photos = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
          }));
          
          return photos;
        } catch (error) {
          console.error('Error loading entity gallery:', error);
          return [];
        }
      },

      addToGallery: async (farmId: string, photoData: any) => {
        try {
          const galleryRef = collection(firestore, `farms/${farmId}/gallery`);
          const docRef = await addDoc(galleryRef, {
            ...photoData,
            createdAt: new Date(),
          });
          return docRef.id;
        } catch (error) {
          console.error('Error adding to gallery:', error);
          throw error;
        }
      },

      removeFromGallery: async (farmId: string, photoId: string) => {
        try {
          const photoRef = doc(firestore, `farms/${farmId}/gallery`, photoId);
          await deleteDoc(photoRef);
        } catch (error) {
          console.error('Error removing from gallery:', error);
          throw error;
        }
      },

      fetchFarms: async (userId) => {
        set({ isLoading: true, error: null });

        try {
          const userDoc = await getDoc(doc(firestore, 'users', userId));
          if (!userDoc.exists()) {
            set({ farms: [], currentFarm: null, isLoading: false });
            return;
          }

          const userData = userDoc.data();
          const assignedFarmIds = userData.assignedFarmIds || [];
          if (assignedFarmIds.length === 0) {
            set({ farms: [], currentFarm: null, isLoading: false });
            return;
          }

          // Fetch all lookup documents once
          // const lookupsSnapshot = await getDocs(collection(firestore, 'lookups'));
          // const lookups = {};
          // lookupsSnapshot.forEach(doc => {
          //   const data = doc.data();
          //   lookups[data.id] = data.title;
          // });
          // console.log({ lookups })
          const farmsData: Farm[] = [];

          for (const farmId of assignedFarmIds) {
            const farmDoc = await getDoc(doc(firestore, 'farms', farmId));
            if (farmDoc.exists()) {
              const data = farmDoc.data();
              const typeTitle = getLookupTitleLocal(data.type);
              const sizeUnitTitle = getLookupTitleLocal(data.sizeUnit);
              const statusTitle = getLookupTitleLocal(data.status);
              farmsData.push({
                id: farmDoc.id,
                name: data.name,
                description: data.description || '',
                location: data.location?.address || '',
                size: data.size,
                sizeUnit: sizeUnitTitle,// data.sizeUnit,//getLookupTitle(data.sizeUnit), //lookups[data.sizeUnit] ||  || '', // lookup here
                type: typeTitle,//data.type || 'Unknown',//lookups[data.type] ||      // lookup here
                ownerId: data.ownerId,
                status: data.status || 'Active',// lookups[data.status] || // lookup here
                image: data.photoURL || '',
                createdAt: data.createdAt?.toDate?.() ? data.createdAt.toDate().toISOString() : new Date().toISOString(),
                updatedAt: data.updatedAt?.toDate?.() ? data.updatedAt.toDate().toISOString() : new Date().toISOString(),
              });
            }
          }

          set({
            farms: farmsData,
            currentFarm: farmsData.length > 0 ? farmsData[0] : null,
            isLoading: false,
          });
        } catch (error: any) {
          console.error('Fetch farms error:', error);
          set({
            error: error.message || 'Failed to fetch farms',
            isLoading: false,
          });
        }
      },

      fetchFields: async (farmId) => {
        set({ isLoading: true, error: null });

        try {
          // Query zones collection (which replaces fields in new structure)
          const zonesQuery = query(
            collection(firestore, `farms/${farmId}/zones`),
            where('type', 'in', ['nSEVmRWD9OqffU6uwqPp'])
          );

          const zonesSnapshot = await getDocs(zonesQuery);
          const fieldsData: Field[] = [];


          zonesSnapshot.forEach((doc) => {
            const data = doc.data();
            const sizeUnit = getLookupTitleLocal(data.sizeUnit)
            const type = getLookupTitleLocal(data.type)
            const status = getLookupTitleLocal(data.status)
            // console.log({ status }, { sizeUnit }, { type })
            fieldsData.push({
              id: doc.id,
              name: data.name,
              type: type || data.type,
              size: data.size,
              sizeUnit: sizeUnit || data.sizeUnit,
              status: status || data.status,
              // cropType: data.cropType || undefined,
              activeCropId: data?.activeCropId,
              // Fix for date handling - check if toDate exists before calling it
              plantedDate: data.plantedDate ?
                (data.plantedDate.toDate ? data.plantedDate.toDate().toISOString() : new Date(data.plantedDate).toISOString())
                : undefined,
              harvestDate: data.harvestDate ?
                (data.harvestDate.toDate ? data.harvestDate.toDate().toISOString() : new Date(data.harvestDate).toISOString())
                : undefined,
              health: data.health || undefined,
              farmId: farmId,
              location: data.location || undefined,
              image: data.photoURL || undefined,
            });
          });

          set({ fields: fieldsData, isLoading: false });
        } catch (error: any) {
          console.error('Fetch fields error:', error);
          set({
            error: error.message || 'Failed to fetch fields',
            isLoading: false
          });
        }
      },

      // fetchTasks: async (farmId) => {
      //   set({ isLoading: true, error: null });

      //   try {
      //     const tasksQuery = query(
      //       collection(firestore, `farms/${farmId}/tasks`)
      //     );

      //     const tasksSnapshot = await getDocs(tasksQuery);
      //     const tasksData: Task[] = [];

      //     tasksSnapshot.forEach((doc) => {
      //       const data = doc.data();
      //       // console.log({ data })
      //       const status = getLookupTitleLocal(data.status)
      //       const priority = getLookupTitleLocal(data.priority)
      //       const Frequency = getLookupTitleLocal(data.frequency)
      //       tasksData.push({
      //         id: doc.id,
      //         title: data.title,
      //         description: data.description || undefined,
      //         status: status || data.status,
      //         priority: priority || data.priority,
      //         dueDate: data.dueDate?.toDate?.() ? data.dueDate.toDate().toISOString() : new Date().toISOString(),
      //         assignedTo: data.assignedTo || undefined,
      //         assignedBy: data.createdBy || undefined,
      //         assignedToName: '',// getUserById(data.assignedTo)?.displayName || '', //data.assignedToName || undefined,
      //         assignedByName: '',// getUserById(data.assignedBy)?.displayName || '',
      //         fieldId: data.location?.zoneId || undefined,
      //         farmId: farmId,
      //         createdAt: data.createdAt?.toDate?.() ? data.createdAt.toDate().toISOString() : new Date().toISOString(),
      //         updatedAt: data.updatedAt?.toDate?.() ? data.updatedAt.toDate().toISOString() : new Date().toISOString(),
      //         completedAt: data.completedDate?.toDate?.() ? data.completedDate.toDate().toISOString() : undefined,
      //         checklist: data.checklist || undefined,
      //         evidence: data.evidence || undefined,
      //         frequency: Frequency || data?.frequency,
      //         AssignedCheckListId: data?.AssignedCheckListId,
      //       });
      //     });

      //     set({ tasks: tasksData, isLoading: false });
      //   } catch (error: any) {
      //     console.error('Fetch tasks error:', error);
      //     set({
      //       error: error.message || 'Failed to fetch tasks',
      //       isLoading: false
      //     });
      //   }
      // },
      // fetchTasks: async (farmId) => {
      //   set({ isLoading: true, error: null });

      //   try {
      //     const tasksRef = collection(firestore, `farms/${farmId}/tasks`);
      //     const tasksSnapshot = await getDocs(tasksRef);

      //     const tasksData: Task[] = [];
      //     const checklistIds: Set<string> = new Set();

      //     // 1. Parse tasks and collect checklist IDs
      //     tasksSnapshot.forEach((doc) => {
      //       const data = doc.data();
      //       if (data.assignedChecklistId) {
      //         checklistIds.add(data.assignedChecklistId);
      //       }
      //       const status = getLookupTitleLocal(data.status);
      //       const priority = getLookupTitleLocal(data.priority);
      //       const frequency = getLookupTitleLocal(data.frequency);
      //       console.log("checklist id", data?.AssignedCheckListId)

      //       tasksData.push({
      //         id: doc.id,
      //         title: data.title,
      //         description: data.description || undefined,
      //         status: status || data.status,
      //         priority: priority || data.priority,
      //         dueDate: data.dueDate?.toDate?.() ? data.dueDate.toDate().toISOString() : new Date().toISOString(),
      //         assignedTo: data.assignedTo || undefined,
      //         assignedBy: data.createdBy || undefined,
      //         assignedToName: '',
      //         assignedByName: '',
      //         fieldId: data.location?.zoneId || undefined,
      //         farmId,
      //         createdAt: data.createdAt?.toDate?.() ? data.createdAt.toDate().toISOString() : new Date().toISOString(),
      //         updatedAt: data.updatedAt?.toDate?.() ? data.updatedAt.toDate().toISOString() : new Date().toISOString(),
      //         completedAt: data.completedDate?.toDate?.() ? data.completedDate.toDate().toISOString() : undefined,
      //         checklist: data.checklist || undefined,
      //         evidence: data.evidence || undefined,
      //         frequency: frequency || data?.frequency,
      //         assignedChecklistId: data.assignedChecklistId || undefined,
      //         checklistDetails: undefined, // we'll populate this next
      //       });
      //     });

      //     // 2. Batch fetch checklists if any
      //     const checklistMap: Record<string, any> = {};
      //     const checklistIdArray = Array.from(checklistIds);

      //     if (checklistIdArray.length) {
      //       const checklistCollection = collection(firestore, 'checklists');

      //       // Firestore 'in' query supports max 10 items, so batch
      //       const batches = [];
      //       for (let i = 0; i < checklistIdArray.length; i += 10) {
      //         const chunk = checklistIdArray.slice(i, i + 10);
      //         const q = query(checklistCollection, where('__name__', 'in', chunk));
      //         batches.push(getDocs(q));
      //       }

      //       const checklistSnapshots = await Promise.all(batches);
      //       checklistSnapshots.forEach((snap) => {
      //         snap.forEach((doc) => {
      //           checklistMap[doc.id] = { id: doc.id, ...doc.data() };
      //         });
      //       });
      //     }

      //     console.log({ checklistMap })
      //     // 3. Merge checklist details back to tasks
      //     const enrichedTasks = tasksData.map((task) => {
      //       if (task.assignedChecklistId && checklistMap[task.assignedChecklistId]) {
      //         return {
      //           ...task,
      //           checklistDetails: checklistMap[task.assignedChecklistId],
      //         };
      //       }
      //       return task;
      //     });

      //     set({ tasks: enrichedTasks, isLoading: false });
      //   } catch (error: any) {
      //     console.error('Fetch tasks error:', error);
      //     set({
      //       error: error.message || 'Failed to fetch tasks',
      //       isLoading: false,
      //     });
      //   }
      // },

      fetchTasks: async (farmId) => {
        set({ isLoading: true, error: null });

        try {
          const tasksRef = collection(firestore, `farms/${farmId}/tasks`);
          const tasksSnapshot = await getDocs(tasksRef);

          const tasksData: any[] = [];
          const checklistIds = new Set<string>();

          // Step 1: Collect all tasks and assignedChecklistId
          tasksSnapshot.forEach((docSnap) => {
            const data = docSnap.data();
            const status = getLookupTitleLocal(data.status);
            const priority = getLookupTitleLocal(data.priority);
            const frequency = getLookupTitleLocal(data.frequency);

            const task = {
              id: docSnap.id,
              title: data.title,
              description: data.description || undefined,
              status: status || data.status,
              priority: priority || data.priority,
              dueDate: data.dueDate?.toDate?.() ? data.dueDate.toDate().toISOString() : new Date().toISOString(),
              assignedTo: data.assignedTo || undefined,
              assignedBy: data.createdBy || undefined,
              assignedToName: '',
              assignedByName: '',
              fieldId: data.location?.zoneId || undefined,
              farmId,
              createdAt: data.createdAt?.toDate?.() ? data.createdAt.toDate().toISOString() : new Date().toISOString(),
              updatedAt: data.updatedAt?.toDate?.() ? data.updatedAt.toDate().toISOString() : new Date().toISOString(),
              completedAt: data.completedDate?.toDate?.() ? data.completedDate.toDate().toISOString() : undefined,
              // checklist: data.checklist || undefined,
              evidence: data.evidence || undefined,
              frequency: frequency || data?.frequency,
              assignedChecklistId: data.AssignedCheckListId || undefined,
              checklistDetails: data?.checklist || undefined,
              imageUrl: data?.photos,
              entityId: data?.entityId,
              relatedEntityType: data?.relatedEntityType,
            };

            if (data.AssignedCheckListId) {
              checklistIds.add(data.AssignedCheckListId);
            }

            tasksData.push(task);
          });

          // Step 2: Fetch all checklists in batches (Firestore allows max 10 items in 'in' query)
          const checklistIdArray = Array.from(checklistIds);
          const checklistMap: Record<string, any> = {};

          if (checklistIdArray.length > 0) {
            const checklistCollection = collection(firestore, 'checklists');

            for (let i = 0; i < checklistIdArray.length; i += 10) {
              const batchIds = checklistIdArray.slice(i, i + 10);
              const checklistQuery = query(checklistCollection, where('__name__', 'in', batchIds));
              const checklistSnapshot = await getDocs(checklistQuery);

              checklistSnapshot.forEach((checklistDoc) => {
                checklistMap[checklistDoc.id] = {
                  id: checklistDoc.id,
                  ...checklistDoc.data(),
                };
              });
            }
          }

          // Step 3: Attach checklistDetails to each task
          const enrichedTasks = tasksData?.map((task) => ({
            ...task,
            checklistDetails: task?.checklistDetails ? task.checklistDetails : task.assignedChecklistId
              ? checklistMap[task.assignedChecklistId]
              : undefined,
          }));

          // Final set state
          set({ tasks: enrichedTasks, isLoading: false });
        } catch (error: any) {
          console.error('Fetch tasks error:', error);
          set({
            error: error.message || 'Failed to fetch tasks',
            isLoading: false,
          });
        }
      },
      fetchInventory: async (farmId) => {
        set({ isLoading: true, error: null });

        try {
          const inventoryQuery = query(
            collection(firestore, `farms/${farmId}/inventory`)
          );

          const inventorySnapshot = await getDocs(inventoryQuery);
          const inventoryData: InventoryItem[] = [];

          inventorySnapshot.forEach((doc) => {
            const data = doc.data();
            inventoryData.push({
              id: doc.id,
              name: data.name,
              category: data.category,
              quantity: data.quantity,
              unit: data.unitOfMeasure || data.unit,
              status: data.status,
              farmId: farmId,
            });
          });

          set({ inventory: inventoryData, isLoading: false });
        } catch (error: any) {
          console.error('Fetch inventory error:', error);
          set({
            error: error.message || 'Failed to fetch inventory',
            isLoading: false
          });
        }
      },

      // Fetch equipment and tools from inventory collection
      fetchEquipmentFromInventory: async (farmId) => {
        set({ isLoading: true, error: null });

        try {
          const inventoryQuery = query(
            collection(firestore, `farms/${farmId}/inventory`),
            where('category', 'in', ['Equipment', 'Tools','Electronics'])
          );

          const inventorySnapshot = await getDocs(inventoryQuery);
          const equipmentData: any[] = [];

          inventorySnapshot.forEach((doc) => {
            const data = doc.data();
            equipmentData.push({
              id: doc.id,
              name: data.name,
              category: data.category,
              quantity: data.quantity,
              unit: data.unit,
              price: data.price,
              supplier: data.supplier,
              location: data.location,
              description: data.description,
              imageUrl: data.imageUrl,
              isConsumable: data.isConsumable,
              minQuantity: data.minQuantity,
              purchaseDate: data.purchaseDate,
              expiryDate: data.expiryDate,
              history: data.history || [],
              createdAt: data.createdAt,
              updatedAt: data.updatedAt,
              farmId: farmId,
            });
          });

          set({ inventoryEquipment: equipmentData, isLoading: false });
          return equipmentData;
        } catch (error: any) {
          console.error('Fetch equipment from inventory error:', error);
          set({
            error: error.message || 'Failed to fetch equipment',
            isLoading: false
          });
          return [];
        }
      },

      fetchWeather: async (location) => {
        set({ isLoading: true, error: null });

        try {
          // In a real app, you would call a weather API here
          // For now, we'll use mock data
          const mockWeather: WeatherForecast[] = [
            {
              date: new Date().toISOString(),
              temperature: 28,
              condition: 'sunny',
              humidity: 45,
              windSpeed: 10,
            },
            {
              date: new Date(Date.now() + 86400000).toISOString(),
              temperature: 26,
              condition: 'cloudy',
              humidity: 60,
              windSpeed: 8,
            },
            {
              date: new Date(Date.now() + 172800000).toISOString(),
              temperature: 25,
              condition: 'rainy',
              humidity: 75,
              windSpeed: 12,
            },
            {
              date: new Date(Date.now() + 259200000).toISOString(),
              temperature: 27,
              condition: 'sunny',
              humidity: 50,
              windSpeed: 7,
            },
          ];

          set({ weather: mockWeather, isLoading: false });
        } catch (error: any) {
          console.error('Fetch weather error:', error);
          set({
            error: error.message || 'Failed to fetch weather',
            isLoading: false
          });
        }
      },

      fetchGardens: async (farmId) => {
        set({ isLoading: true, error: null });

        try {
          // In the new structure, gardens are a type of zone
          const gardensQuery = query(
            collection(firestore, `farms/${farmId}/zones`),
            where('type', '==', 'garden')
          );

          const gardensSnapshot = await getDocs(gardensQuery);
          const gardensData: Garden[] = [];

          gardensSnapshot.forEach((doc) => {
            const data = doc.data();
            const gardenType = getLookupTitleLocal(data.gardenType)
            const status = getLookupTitleLocal(data.status)
            const sizeUnit = getLookupTitleLocal(data.sizeUnit)
            const soilType = getLookupTitleLocal(data.soilType)
            const irrigationSystem = getLookupTitleLocal(data.irrigationSystem)
            // console.log({ gardenType }, data.type)
            gardensData.push({
              id: doc.id,
              name: data.name,
              type: data.type,
              gardenType: gardenType || data?.gardenType,
              size: data.size,
              sizeUnit: sizeUnit || data.sizeUnit,
              location: data.location || undefined,
              status: status || data.status,
              soilType: soilType || data.soilType || undefined,
              irrigationSystem: irrigationSystem || data.irrigationSystem || undefined,
              farmId: farmId,
              createdAt: data.createdAt?.toDate?.() ? data.createdAt.toDate().toISOString() : new Date().toISOString(),
              updatedAt: data.lastModified?.toDate?.() ? data.lastModified.toDate().toISOString() : new Date().toISOString(),
              image: data.photoURL || undefined,
              isInactive: data?.isInactive || false,
              inactiveReason: data?.inactiveReason || undefined,
              inactiveDate: data.inactiveDate?.toDate?.() ? data.inactiveDate.toDate().toISOString() : new Date().toISOString(),

            });
          });

          set({ gardens: gardensData, isLoading: false });
        } catch (error: any) {
          console.error('Fetch gardens error:', error);
          set({
            error: error.message || 'Failed to fetch gardens',
            isLoading: false
          });
        }
      },

      fetchPlants: async (farmId) => {
        set({ isLoading: true, error: null });

        try {
          const plantsQuery = query(
            collection(firestore, `farms/${farmId}/plants`)
          );

          const plantsSnapshot = await getDocs(plantsQuery);
          const plantsData: Plant[] = [];

          // Create an array of promises for fetching health check data for each plant
          const plantPromises = plantsSnapshot.docs.map(async (doc) => {
            const data = doc.data();
            const plantId = doc.id;
            console.log({ data });
            const growthStage = getLookupTitleLocal(data.growthStage);
            const healthStatus = getLookupTitleLocal(data.healthStatus);

            // Fetch the latest health check data for this plant
            const healthCheckRef = collection(firestore, `farms/${farmId}/DailyPlantHealthCheck`);
            const healthCheckQuery = query(
              healthCheckRef,
              where('plantId', '==', plantId),
              // orderBy('date', 'desc'),
              // limit(1)
            );

            const healthCheckSnapshot = await getDocs(healthCheckQuery);
            let latestHealthCheck = null;

            if (!healthCheckSnapshot.empty) {
              latestHealthCheck = healthCheckSnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
              }));
              //  {
              //   id: healthCheckSnapshot.docs[0].id,
              //   ...healthCheckSnapshot.docs[0].data()
              // };
            }

            return {
              id: plantId,
              name: data.name,
              species: data.species || '',
              variety: data.variety || undefined,
              plantedDate: data.plantingDate?.toDate?.() ? data.plantingDate.toDate().toISOString() : new Date().toISOString(),
              expectedHarvestDate: data.expectedHarvestDate || '',
              status: growthStage || '',
              health: healthStatus || '',
              gardenId: data.location?.zoneId || undefined,
              fieldId: data.location?.zoneId || undefined,
              farmId: farmId,
              location: data?.location || undefined,
              notes: data.notes || undefined,
              image: data.photoURL || undefined,
              createdAt: data.addedAt?.toDate?.() ? data.addedAt.toDate().toISOString() : new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              isInactive: data?.isInactive || false,
              inactiveReason: data?.inactiveReason || undefined,
              inactiveDate: data.inactiveDate?.toDate?.() ? data.inactiveDate.toDate().toISOString() : new Date().toISOString(),
              identificationID: data?.identificationID,
              healthCheck: latestHealthCheck, // Add the latest health check data
              helpGuide: data?.helpGuide || undefined,
            };
          });

          // Wait for all plant data to be fetched
          const plantsWithHealthData = await Promise.all(plantPromises);

          set({ plants: plantsWithHealthData, isLoading: false });
        } catch (error: any) {
          console.error('Fetch plants error:', error);
          set({
            error: error.message || 'Failed to fetch plants',
            isLoading: false
          });
        }
      },

      fetchAnimals: async (farmId) => {
        set({ isLoading: true, error: null });

        try {
          const animalsRef = collection(firestore, 'farms', farmId, 'animals');
          const animalSnapshots = await getDocs(animalsRef);

          const animalPromises = animalSnapshots.docs.map(async (animalDoc) => {
            const animalData = animalDoc.data();
            const animalId = animalDoc.id;

            // Fire both subcollection fetches in parallel
            const [healthChecksSnap, pregnanciesSnap] = await Promise.all([
              getDocs(collection(firestore, 'farms', farmId, 'animals', animalId, 'healthChecks')),
              getDocs(collection(firestore, 'farms', farmId, 'animals', animalId, 'pregnancies'))
            ]);

            const healthChecks = healthChecksSnap.docs.map(doc => ({
              id: doc.id,
              ...doc.data(),
            }));

            const pregnancies = pregnanciesSnap.docs.map(doc => ({
              id: doc.id,
              ...doc.data(),
            }));

            return {
              id: animalId,
              age: animalData?.age,
              name: animalData.name || undefined,
              species: animalData?.species || animalData.type || '',
              breed: animalData.breed || undefined,
              birthDate: animalData?.acquisitionDate?.toDate?.() ? animalData.acquisitionDate.toDate().toISOString() : undefined,
              gender: animalData?.gender || 'unknown',
              status: animalData?.status || 'healthy',
              purpose: animalData?.purpose || 'meat',
              identificationNumber: animalData?.identificationNumber || undefined,
              farmId: farmId,
              fieldId: animalData?.location?.zoneId || undefined,
              notes: animalData?.notes || undefined,
              image: animalData?.photoURL || animalData?.imageUri || undefined,
              createdAt: animalData?.addedAt?.toDate?.() ? animalData?.addedAt?.toDate()?.toISOString() : new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              isInactive: animalData?.isInactive || false,
              inactiveReason: animalData?.inactiveReason || undefined,
              inactiveDate: animalData?.inactiveDate?.toDate?.() ? animalData?.inactiveDate?.toDate().toISOString() : new Date().toISOString(),
              healthChecks,
              pregnancies,
            };
          });

          // Wait for all animal data to resolve in parallel
          const animals = await Promise.all(animalPromises);

          set({ animals: animals, isLoading: false });
        } catch (error: any) {
          console.error("error", error);
        }
      },

      fetchEquipment: async (farmId) => {
        set({ isLoading: true, error: null });

        try {
          // In the new structure, equipment would be in inventory with category "equipment"
          const equipmentQuery = collection(firestore, `farms/${farmId}/machinery`);

          const equipmentSnapshot = await getDocs(equipmentQuery);
          const equipmentData: Equipment[] = [];

          equipmentSnapshot.forEach((doc) => {
            const data = doc.data();
            // console.log({ data })
            const type = getLookupTitleLocal(data?.type)
            equipmentData.push({
              id: doc.id,
              name: data.name,
              type: type || data.type || 'tool',
              fuelType: data.fuelType,
              manufacturer: data.manufacturer || undefined,
              model: data.model || undefined,
              purchaseDate: data.acquisitionDate?.toDate?.() ? data.acquisitionDate.toDate().toISOString() : undefined,
              purchasePrice: data.acquisitionCost || undefined,
              status: data.status || 'operational',
              lastMaintenanceDate: data.lastMaintenanceDate?.toDate?.() ? data.lastMaintenanceDate.toDate().toISOString() : undefined,
              nextMaintenanceDate: data.nextMaintenanceDate?.toDate?.() ? data.nextMaintenanceDate.toDate().toISOString() : undefined,
              farmId: farmId,
              location: data.location?.zoneId || undefined,
              notes: data.notes || undefined,
              image: data.photoURL || data?.imageUrl || undefined,
              createdAt: data.addedAt?.toDate?.() ? data.addedAt.toDate().toISOString() : new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              isInactive: data?.isInactive || false,
              inactiveReason: data?.inactiveReason || undefined,
              inactiveDate: data.inactiveDate?.toDate?.() ? data.inactiveDate.toDate().toISOString() : new Date().toISOString(),
              currentFuelLevel: data?.currentFuelLevel || '',
              fuelType: data?.fuelType || '',
              photoUrl: data?.imageUrl
            });
          });

          set({ equipment: equipmentData, isLoading: false });
        } catch (error: any) {
          console.error('Fetch equipment error:', error);
          set({
            error: error.message || 'Failed to fetch equipment',
            isLoading: false
          });
        }
      },

      fetchYields: async (farmId) => {
        set({ isLoading: true, error: null });

        try {
          // In the new structure, yields would be tracked in a separate collection
          // For now, we'll simulate this with a query
          const yieldsQuery = query(
            collection(firestore, `farms/${farmId}/activities`),
            where('action', '==', 'harvest')
          );

          const yieldsSnapshot = await getDocs(yieldsQuery);
          const yieldsData: Yield[] = [];

          yieldsSnapshot.forEach((doc) => {
            const data = doc.data();
            yieldsData.push({
              id: doc.id,
              name: data.details || 'Harvest',
              cropType: data.targetType || '',
              harvestDate: data.timestamp?.toDate?.() ? data.timestamp.toDate().toISOString() : new Date().toISOString(),
              quantity: data.quantity || 0,
              unit: data.unit || 'kg',
              quality: data.quality || 'good',
              fieldId: data.location?.zoneId || undefined,
              gardenId: data.location?.gardenId || undefined,
              farmId: farmId,
              notes: data.notes || undefined,
              image: data.photoURL || undefined,
              createdAt: data.timestamp?.toDate?.() ? data.timestamp.toDate().toISOString() : new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            });
          });

          set({ yields: yieldsData, isLoading: false });
        } catch (error: any) {
          console.error('Fetch yields error:', error);
          set({
            error: error.message || 'Failed to fetch yields',
            isLoading: false
          });
        }
      },

      fetchFarmPermissions: async (farmId) => {
        set({ isLoading: true, error: null });

        try {
          // Query users with this farmId in their assignedFarmIds array
          const usersQuery = query(
            collection(firestore, 'users'),
            where('assignedFarmIds', 'array-contains', farmId)
          );

          const usersSnapshot = await getDocs(usersQuery);
          const permissionsData: FarmPermission[] = [];

          usersSnapshot.forEach((doc) => {
            const data = doc.data();
            permissionsData.push({
              id: doc.id, // Using user ID as permission ID
              farmId: farmId,
              userId: doc.id,
              role: data.role,
              status: data.status || 'active',
              assignedBy: data.assignedBy || '',
              assignedAt: data.assignedAt?.toDate?.() ? data.assignedAt.toDate().toISOString() : new Date().toISOString(),
              areas: data.areas || []
            });
          });

          set({ farmPermissions: permissionsData, isLoading: false });
        } catch (error: any) {
          console.error('Fetch farm permissions error:', error);
          set({
            error: error.message || 'Failed to fetch farm permissions',
            isLoading: false
          });
        }
      },

      setCurrentFarm: (farmId) => {
        const farm = get().farms.find(f => f.id === farmId) || null;
        set({ currentFarm: farm });

        // If farm is found, fetch its data
        if (farm) {
          get().fetchFields(farm.id);
          get().fetchTasks(farm.id);
          get().fetchInventory(farm.id);
          get().fetchEquipmentFromInventory(farm.id);
          get().fetchGardens(farm.id);
          get().fetchPlants(farm.id);
          get().fetchAnimals(farm.id);
          get().fetchEquipment(farm.id);
          get().fetchYields(farm.id);
          get().fetchFarmPermissions(farm.id);
        }
      },

      // Set current farm with persistence
      setCurrentFarmWithPersistence: async (farmId) => {
        if (!farmId) {
          set({ currentFarm: null });
          await clearSelectedFarm();
          return;
        }

        const farm = get().farms.find(f => f.id === farmId) || null;

        if (farm) {
          set({ currentFarm: farm });

          // Save to AsyncStorage
          await saveSelectedFarm(farm.id, farm.name);

          // Fetch farm data
          get().fetchFields(farm.id);
          get().fetchTasks(farm.id);
          get().fetchInventory(farm.id);
          get().fetchEquipmentFromInventory(farm.id);
          get().fetchGardens(farm.id);
          get().fetchPlants(farm.id);
          get().fetchAnimals(farm.id);
          get().fetchEquipment(farm.id);
          get().fetchYields(farm.id);
          get().fetchFarmPermissions(farm.id);
        }
      },

      // Load persisted farm on app start
      loadPersistedFarm: async () => {
        try {
          const persistedFarm = await getSelectedFarm();

          if (persistedFarm) {
            const farm = get().farms.find(f => f.id === persistedFarm.farmId);

            if (farm) {
              // Set current farm without triggering persistence again
              get().setCurrentFarm(farm.id);
              console.log('Loaded persisted farm:', farm.name);
            } else {
              // Farm no longer exists, clear persistence
              await clearSelectedFarm();
              console.log('Persisted farm no longer exists, cleared selection');
            }
          }
        } catch (error) {
          console.error('Error loading persisted farm:', error);
        }
      },

      // Clear persisted farm
      clearPersistedFarm: async () => {
        try {
          await clearSelectedFarm();
          set({ currentFarm: null });
          console.log('Cleared persisted farm selection');
        } catch (error) {
          console.error('Error clearing persisted farm:', error);
        }
      },

      addFarm: async (farm) => {
        set({ isLoading: true, error: null });

        try {
          // Create farm data according to new structure
          const farmData = {
            name: farm.name || 'New Farm',
            description: farm.description || '',
            type: farm.type || 'mixed',
            size: farm.size || 0,
            sizeUnit: farm.sizeUnit || 'acres',
            location: {
              latitude: 0,
              longitude: 0,
              address: farm.location || ''
            },
            createdAt: new Date(),
            createdBy: farm.ownerId || '',
            ownerId: farm.ownerId || '',
            // ownerUid: farm.ownerId || '',
            photoURL: farm.image || '',
            status: farm.status || 'active'
          };

          const docRef = await addDoc(collection(firestore, 'farms'), farmData);

          // Add farm to owner's assignedFarmIds
          if (farm.ownerId) {
            const userRef = doc(firestore, 'users', farm.ownerId);
            const userDoc = await getDoc(userRef);

            if (userDoc.exists()) {
              // Use arrayUnion to add farmId to assignedFarmIds array
              await updateDoc(userRef, {
                assignedFarmIds: [...(userDoc.data().assignedFarmIds || []), docRef.id],
                updatedAt: serverTimestamp()
              });

              // Update current user in auth store if this is the current user
              const authStore = useAuthStore.getState();
              if (authStore.user && authStore.user.id === farm.ownerId) {
                const updatedUser = {
                  ...authStore.user,
                  assignedFarmIds: [...(authStore.user.assignedFarmIds || []), docRef.id]
                };
                authStore.updateUserProfile(updatedUser);
              }
            }
          }

          const newFarm: Farm = {
            id: docRef.id,
            name: farmData.name,
            description: farmData.description,
            location: farmData.location.address,
            size: farmData.size,
            sizeUnit: farmData.sizeUnit,
            type: farmData.type,
            ownerId: farmData.ownerUid,
            status: 'active',
            image: farmData.photoURL,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };

          set(state => ({
            farms: [...state.farms, newFarm],
            currentFarm: newFarm,
            isLoading: false,
          }));

          return docRef.id;
        } catch (error: any) {
          console.error('Add farm error:', error);
          set({
            error: error.message || 'Failed to add farm',
            isLoading: false
          });
          return '';
        }
      },

      updateFarm: async (farmId, data) => {
        set({ isLoading: true, error: null });

        try {
          const farmRef = doc(firestore, 'farms', farmId);

          // Create update data according to new structure
          const updateData: any = {
            updatedAt: new Date()
          };

          if (data.name) updateData.name = data.name;
          if (data.description !== undefined) updateData.description = data.description;
          if (data.size !== undefined) updateData.size = data.size;
          if (data.sizeUnit) updateData.sizeUnit = data.sizeUnit;
          if (data.type) updateData.type = data.type;
          if (data.status) updateData.status = data.status;
          if (data.image) updateData.photoURL = data.image;
          if (data.location) {
            updateData['location.address'] = data.location;
          }

          await updateDoc(farmRef, updateData);

          set(state => ({
            farms: state.farms.map(farm =>
              farm.id === farmId
                ? {
                  ...farm,
                  ...data,
                  updatedAt: new Date().toISOString()
                }
                : farm
            ),
            currentFarm: state.currentFarm?.id === farmId
              ? {
                ...state.currentFarm,
                ...data,
                updatedAt: new Date().toISOString()
              }
              : state.currentFarm,
            isLoading: false,
          }));
        } catch (error: any) {
          console.error('Update farm error:', error);
          set({
            error: error.message || 'Failed to update farm',
            isLoading: false
          });
        }
      },

      deleteFarm: async (farmId) => {
        set({ isLoading: true, error: null });

        try {
          await deleteDoc(doc(firestore, 'farms', farmId));

          // Remove farm from all users' assignedFarmIds
          const usersQuery = query(
            collection(firestore, 'users'),
            where('assignedFarmIds', 'array-contains', farmId)
          );

          const usersSnapshot = await getDocs(usersQuery);

          const updatePromises = usersSnapshot.docs.map(async (userDoc) => {
            const userData = userDoc.data();
            const updatedFarmIds = (userData.assignedFarmIds || []).filter(id => id !== farmId);

            await updateDoc(doc(firestore, 'users', userDoc.id), {
              assignedFarmIds: updatedFarmIds,
              updatedAt: serverTimestamp()
            });

            // Update current user in auth store if this is the current user
            const authStore = useAuthStore.getState();
            if (authStore.user && authStore.user.id === userDoc.id) {
              const updatedUser = {
                ...authStore.user,
                assignedFarmIds: updatedFarmIds
              };
              authStore.updateUserProfile(updatedUser);
            }
          });

          await Promise.all(updatePromises);

          set(state => ({
            farms: state.farms.filter(farm => farm.id !== farmId),
            currentFarm: state.currentFarm?.id === farmId
              ? (state.farms.length > 1
                ? state.farms.find(f => f.id !== farmId) || null
                : null)
              : state.currentFarm,
            isLoading: false,
          }));
        } catch (error: any) {
          console.error('Delete farm error:', error);
          set({
            error: error.message || 'Failed to delete farm',
            isLoading: false
          });
        }
      },

      addField: async (field) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = field.farmId || get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          // Create zone data according to new structure
          const zoneData: any = {
            name: field.name || 'New Field',
            type: field.type || 'cropland',
            size: field.size || 0,
            sizeUnit: field.sizeUnit || 'acres',
            status: field.status || 'active',
            assignedCaretakers: [],
            createdAt: new Date(),
            createdBy: '', // This should be the current user ID
            lastModified: new Date(),
          };

           if (field.location) zoneData.location = field.location;
          if (field.image) zoneData.photoURL = field.image;

          const docRef = await addDoc(collection(firestore, `farms/${farmId}/zones`), zoneData);

          // Don't add to local state here - let the real-time listener handle it
          // This prevents duplication since the listener will process the new document

          set({ isLoading: false });

          return docRef.id;
        } catch (error: any) {
          console.error('Add field error:', error);
          set({
            error: error.message || 'Failed to add field',
            isLoading: false
          });
          return '';
        }
      },

      updateField: async (fieldId, data) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          const fieldRef = doc(firestore, `farms/${farmId}/zones`, fieldId);

          // Create update data according to new structure
          const updateData: any = {
            lastModified: new Date()
          };

          // Map field properties to zone properties
          if (data.name) updateData.name = data.name;
          if (data.type) updateData.type = data.type;
          if (data.size !== undefined) updateData.size = data.size;
          if (data.sizeUnit) updateData.sizeUnit = data.sizeUnit;
          if (data.status) updateData.status = data.status;
          if (data.cropType) updateData.cropType = data.cropType;
          if (data.plantedDate) updateData.plantedDate = new Date(data.plantedDate);
          if (data.harvestDate) updateData.harvestDate = new Date(data.harvestDate);
          if (data.health) updateData.health = data.health;
          if (data.location) updateData.location = data.location;
          if (data.image) updateData.photoURL = data.image;

          await updateDoc(fieldRef, updateData);

          const health = getLookupTitleLocal(data.health)
          const sizeUnit = getLookupTitleLocal(data.sizeUnit)
          const status = getLookupTitleLocal(data.status)
          const type = getLookupTitleLocal(data?.type)
          set(state => ({
            fields: state.fields.map(field =>
              field.id === fieldId ? { ...field, ...data, health, sizeUnit, status, type } : field
            ),
            isLoading: false,
          }));
        } catch (error: any) {
          console.error('Update field error:', error);
          set({
            error: error.message || 'Failed to update field',
            isLoading: false
          });
        }
      },

      deleteField: async (fieldId) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          await deleteDoc(doc(firestore, `farms/${farmId}/zones`, fieldId));

          set(state => ({
            fields: state.fields.filter(field => field.id !== fieldId),
            isLoading: false,
          }));
        } catch (error: any) {
          console.error('Delete field error:', error);
          set({
            error: error.message || 'Failed to delete field',
            isLoading: false
          });
        }
      },

      // Helper function to generate recurring tasks
      generateRecurringTasks: (baseTask: any, frequency: string, repeatUntil: Date) => {
        const tasks = [];
        const startDate = new Date(baseTask.dueDate);
        const endDate = new Date(repeatUntil);
        let currentDate = new Date(startDate);

        while (currentDate <= endDate) {
          const taskData = {
            ...baseTask,
            id: uuidv4(),
            dueDate: new Date(currentDate),
            createdAt: new Date(),
            updatedAt: new Date(),
            parentTaskId: baseTask.id, // Link to original recurring task
            isRecurringInstance: true,
          };

          tasks.push(taskData);

          // Calculate next occurrence
          switch (frequency) {
            case 'daily':
              currentDate.setDate(currentDate.getDate() + 1);
              break;
            case 'weekly':
              currentDate.setDate(currentDate.getDate() + 7);
              break;
            case 'monthly':
              currentDate.setMonth(currentDate.getMonth() + 1);
              break;
            default:
              return tasks; // Stop if frequency is not recognized
          }
        }

        return tasks;
      },

      addTask: async (task) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = task.farmId || get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          // Create task data according to new structure
          const taskData: any = {
            title: task.title || 'New Task',
            description: task.description || '',
            category: task.category || 'general',
            priority: task.priority || 'medium',
            status: task.status || 'pending',
            createdBy: task.assignedBy || '',
            frequency: task.frequency,
            createdAt: new Date(),
            isRecurring: task?.isRecurring,
            assignedTo: task.assignedTo || '',
            dueDate: task.dueDate ? new Date(task.dueDate) : new Date(),
            location: {
              zoneId: task.fieldId || '',
              gardenId: task.gardenId || ''
            },
            relatedItems: task.relatedItems || [],
            steps: task.steps || [],
            relatedEntityType: task?.relatedEntityType,
            entityId: task?.entityId,
            evidence: task.evidence || {
              required: false,
              type: 'photo',
              submitted: false
            },
            AssignedCheckListId: task?.AssignedCheckListId
          };

          // Add photos if available
          if (task.imageUrl) {
            taskData.photos = [{
              url: task.imageUrl,
              timestamp: new Date(),
              takenBy: task.assignedBy || ''
            }];
          }

          const docRef = await addDoc(collection(firestore, `farms/${farmId}/tasks`), taskData);
          taskData.id = docRef.id;

          // Handle recurring tasks
          if (task.isRecurring && task.frequency !== 'once' && task.repeatUntil) {
            const recurringTasks = get().generateRecurringTasks(
              taskData,
              task.frequency,
              new Date(task.repeatUntil)
            );

            // Save all recurring task instances
            const batch = [];
            for (const recurringTask of recurringTasks) {
              const recurringDocRef = await addDoc(
                collection(firestore, `farms/${farmId}/tasks`),
                recurringTask
              );
              batch.push({ ...recurringTask, id: recurringDocRef.id });
            }
          }

          // Create notification for assignee
          if (task.assignedTo) {
            const notificationData = {
              userId: task.assignedTo,
              farmId: farmId,
              type: 'task_assigned',
              title: 'New Task Assigned',
              message: `You have been assigned a new task: ${task.title}`,
              createdAt: new Date(),
              read: false,
              data: {
                targetId: docRef.id,
                targetType: 'task'
              }
            };

            try {
              await addDoc(collection(firestore, 'notifications'), notificationData);
            } catch (error) {
              console.error('Error creating notification:', error);
            }
          }

          // Create activity log
          const activityLogData = {
            title: `Created task`,
            decrption: `Created task: ${task.title}`,
            CreatedAt: new Date(),
            Createdby: task.assignedBy,
            AppType: "3",
            // userId: task.assignedBy || '',
            // userName: '', // This should be filled with the user's name
            // action: 'created_task',
            // targetType: 'task',
            // targetId: docRef.id,
            // details: `Created task: ${task.title}`,
            // timestamp: new Date()


          };

          try {
            await addDoc(collection(firestore, `farms/${farmId}/activities`), activityLogData);
          } catch (error) {
            console.error('Error creating activity log:', error);
          }



          // Don't add to local state here - let the real-time listener handle it
          // This prevents duplication since the listener will process the new document

          set({ isLoading: false });

          // Trigger related data refresh
          logEvent('task_created', {
            taskId: docRef.id,
            taskTitle: task.title,
            assignedTo: task.assignedTo,
            farmId: farmId
          });

          return docRef.id;
        } catch (error: any) {
          console.error('Add task error:', error);
          set({
            error: error.message || 'Failed to add task',
            isLoading: false
          });
          return '';
        }
      },

      updateTask: async (taskId, data) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          const taskRef = doc(firestore, `farms/${farmId}/tasks`, taskId);

          // Create update data according to new structure
          const updateData: any = {
            updatedAt: new Date()
          };

          // Map task properties
          if (data.title) updateData.title = data.title;
          if (data.description !== undefined) updateData.description = data.description;
          if (data.status) updateData.status = data.status;
          if (data.priority) updateData.priority = data.priority;
          if (data.dueDate) updateData.dueDate = new Date(data.dueDate);
          if (data.assignedTo) updateData.assignedTo = data.assignedTo;
          if (data.fieldId) updateData['location.zoneId'] = data.fieldId;
          if (data.completedAt) updateData.completedDate = new Date(data.completedAt);

          if (data.checklistDetails) updateData.checklist = data.checklistDetails;
          if (data.evidence) updateData.evidence = data.evidence;
          //    checklist,
          // evidence: {
          //   ...task.evidence,
          //   images: uploadedImages,
          //   notes,
          // },
          await updateDoc(taskRef, updateData);

          // Create activity log for task update
          const activityLogData = {
            title: 'Task Updated',
            decrption: `Updated task: ${data.title || 'Task'}`,
            CreatedAt: new Date(),
            Createdby: data.assignedBy || get().currentFarm?.ownerId || '',
            AppType: "3",
          };

          try {
            await addDoc(collection(firestore, `farms/${farmId}/activities`), activityLogData);
          } catch (error) {
            console.error('Error creating activity log:', error);
          }

          set(state => ({
            tasks: state.tasks.map(task =>
              task.id === taskId
                ? {
                  ...task,
                  ...data,
                  updatedAt: new Date().toISOString()
                }
                : task
            ),
            isLoading: false,
          }));

          // Trigger related data refresh
          logEvent('task_updated', {
            taskId: taskId,
            taskTitle: data.title || 'Task',
            farmId: farmId
          });
        } catch (error: any) {
          console.error('Update task error:', error);
          set({
            error: error.message || 'Failed to update task',
            isLoading: false
          });
        }
      },

      deleteTask: async (taskId) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          await deleteDoc(doc(firestore, `farms/${farmId}/tasks`, taskId));

          // Delete related notifications
          const notificationsQuery = query(
            collection(firestore, 'notifications'),
            where('data.targetId', '==', taskId),
            where('data.targetType', '==', 'task')
          );

          const notificationsSnapshot = await getDocs(notificationsQuery);

          notificationsSnapshot.forEach(async (doc) => {
            await deleteDoc(doc.ref);
          });

          set(state => ({
            tasks: state.tasks.filter(task => task.id !== taskId),
            isLoading: false,
          }));
        } catch (error: any) {
          console.error('Delete task error:', error);
          set({
            error: error.message || 'Failed to delete task',
            isLoading: false
          });
        }
      },

      completeTask: async (taskId) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          const taskRef = doc(firestore, `farms/${farmId}/tasks`, taskId);
          const now = new Date();

          await updateDoc(taskRef, {
            status: 'completed',
            completedDate: now,
            updatedAt: now,
          });

          // Create activity log for task completion
          const task = get().tasks.find(t => t.id === taskId);

          // const activityLogData = {
          //   // userId: task?.assignedTo || '',
          //   // userName: '', // This should be filled with the user's name
          //   // action: 'completed_task',
          //   // targetType: 'task',
          //   // targetId: taskId,
          //   // details: `Completed task: ${task?.title || 'Task'}`,
          //   // timestamp: now
          //   title: `Created task`,
          //   decrption: `Completed task: ${task?.title || 'Task'}`,
          //   // CreatedAt: new Date(),
          //   // Createdby: task.assignedTo,
          //   updatedAt: new Date(),
          //   updatedBy: task?.assignedTo || '',
          //   AppType: "3",
          // };

          // Create activity log for task completion
          const activityLogData = {
            title: 'Task Completed',
            decrption: `Completed task: ${task?.title || 'Task'}`,
            CreatedAt: new Date(),
            Createdby: task?.assignedTo || get().currentFarm?.ownerId || '',
            AppType: "3",
          };

          try {
            await addDoc(collection(firestore, `farms/${farmId}/activities`), activityLogData);
          } catch (error) {
            console.error('Error creating activity log:', error);
          }

          // Create notification for task creator
          if (task?.assignedBy) {
            const notificationData = {
              userId: task.assignedBy,
              farmId: farmId,
              type: 'task_completed',
              title: 'Task Completed',
              message: `Task "${task.title}" has been completed`,
              createdAt: now,
              read: false,
              data: {
                targetId: taskId,
                targetType: 'task'
              }
            };

            try {
              await addDoc(collection(firestore, 'notifications'), notificationData);
            } catch (error) {
              console.error('Error creating notification:', error);
            }
          }

          set(state => ({
            tasks: state.tasks.map(task =>
              task.id === taskId
                ? {
                  ...task,
                  status: 'completed',
                  completedAt: now.toISOString(),
                  updatedAt: now.toISOString(),
                }
                : task
            ),
            isLoading: false,
          }));

          // Trigger related data refresh
          logEvent('task_completed', {
            taskId: taskId,
            taskTitle: task?.title || 'Task',
            assignedTo: task?.assignedTo,
            farmId: farmId
          });
        } catch (error: any) {
          console.error('Complete task error:', error);
          set({
            error: error.message || 'Failed to complete task',
            isLoading: false
          });
        }
      },

      addInventoryItem: async (item) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = item.farmId || get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          // Create inventory item data according to new structure
          const itemData: any = {
            name: item.name || 'New Item',
            category: item.category || 'resource',
            quantity: item.quantity || 0,
            unitOfMeasure: item.unit || 'units',
            status: item.status || 'active',
            acquisitionDate: new Date(),
            addedBy: '', // This should be the current user ID
            addedAt: new Date(),
            notes: item.notes || '',
            location: {
              zoneId: item.fieldId || ''
            }
          };

          if (item.acquisitionCost) itemData.acquisitionCost = item.acquisitionCost;

          const docRef = await addDoc(collection(firestore, `farms/${farmId}/inventory`), itemData);

          const newItem: InventoryItem = {
            id: docRef.id,
            name: itemData.name,
            category: itemData.category,
            quantity: itemData.quantity,
            unit: itemData.unitOfMeasure,
            status: itemData.status,
            farmId: farmId,
          };

          set(state => ({
            inventory: [...state.inventory, newItem],
            isLoading: false,
          }));

          return docRef.id;
        } catch (error: any) {
          console.error('Add inventory item error:', error);
          set({
            error: error.message || 'Failed to add inventory item',
            isLoading: false
          });
          return '';
        }
      },

      updateInventoryItem: async (itemId, data) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          const itemRef = doc(firestore, `farms/${farmId}/inventory`, itemId);

          // Create update data according to new structure
          const updateData: any = {};

          if (data.name) updateData.name = data.name;
          if (data.category) updateData.category = data.category;
          if (data.quantity !== undefined) updateData.quantity = data.quantity;
          if (data.unit) updateData.unitOfMeasure = data.unit;
          if (data.status) updateData.status = data.status;
          if (data.fieldId) updateData['location.zoneId'] = data.fieldId;

          await updateDoc(itemRef, updateData);

          set(state => ({
            inventory: state.inventory.map(item =>
              item.id === itemId ? { ...item, ...data } : item
            ),
            isLoading: false,
          }));
        } catch (error: any) {
          console.error('Update inventory item error:', error);
          set({
            error: error.message || 'Failed to update inventory item',
            isLoading: false
          });
        }
      },

      deleteInventoryItem: async (itemId) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          await deleteDoc(doc(firestore, `farms/${farmId}/inventory`, itemId));

          set(state => ({
            inventory: state.inventory.filter(item => item.id !== itemId),
            isLoading: false,
          }));
        } catch (error: any) {
          console.error('Delete inventory item error:', error);
          set({
            error: error.message || 'Failed to delete inventory item',
            isLoading: false
          });
        }
      },

      // Garden CRUD operations
      addGarden: async (garden) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = garden.farmId || get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          // Create zone data with type 'garden' according to new structure
          const gardenData: any = {
            name: garden.name || 'New Garden',
            type: 'garden',
            size: garden.size || 0,
            gardenType: garden.type,
            sizeUnit: garden.sizeUnit || 'square_meters',
            status: garden.status || 'active',
            assignedCaretakers: [],
            createdAt: new Date(),
            createdBy: garden.createdBy || '',
            lastModified: new Date(),
          };

          // Add optional fields
          if (garden.location) gardenData.location = garden.location;
          if (garden.soilType) gardenData.soilType = garden.soilType;
          if (garden.irrigationSystem) gardenData.irrigationSystem = garden.irrigationSystem;
          if (garden.image) gardenData.photoURL = garden.image;

          const docRef = await addDoc(collection(firestore, `farms/${farmId}/zones`), gardenData);

          // Don't add to local state here - let the real-time listener handle it
          // This prevents duplication since the listener will process the new document

          set({ isLoading: false });

          return docRef.id;
        } catch (error: any) {
          console.error('Add garden error:', error);
          set({
            error: error.message || 'Failed to add garden',
            isLoading: false
          });
          return '';
        }
      },

      updateGarden: async (gardenId, data) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          const gardenRef = doc(firestore, `farms/${farmId}/zones`, gardenId);

          // Create update data according to new structure
          const updateData: any = {
            lastModified: new Date()
          };

          // console.log({ gardenId }, { data })
          // Map garden properties to zone properties
          if (data.name) updateData.name = data.name;
          if (data.size !== undefined) updateData.size = data.size;
          if (data.sizeUnit) updateData.sizeUnit = data.sizeUnit;
          if (data.status) updateData.status = data.status;
          if (data.soilType) updateData.soilType = data.soilType;
          if (data.irrigationSystem) updateData.irrigationSystem = data.irrigationSystem;
          if (data.location) updateData.location = data.location;
          if (data.image) updateData.photoURL = data.image;
          if (data.type) updateData.gardenType = data.type;

          const gardenType = getLookupTitleLocal(updateData.gardenType)
          const status = getLookupTitleLocal(updateData.status)
          const sizeUnit = getLookupTitleLocal(updateData.sizeUnit)
          const soilType = getLookupTitleLocal(updateData.soilType)
          const irrigationSystem = getLookupTitleLocal(updateData.irrigationSystem)
          await updateDoc(gardenRef, updateData);

          set(state => ({
            gardens: state.gardens.map(garden =>
              garden.id === gardenId ? { ...garden, ...data, status, sizeUnit, soilType, irrigationSystem, gardenType, updatedAt: new Date().toISOString() } : garden
            ),
            isLoading: false,
          }));
        } catch (error: any) {
          console.error('Update garden error:', error);
          set({
            error: error.message || 'Failed to update garden',
            isLoading: false
          });
        }
      },

      deleteGarden: async (gardenId) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          await deleteDoc(doc(firestore, `farms/${farmId}/zones`, gardenId));

          set(state => ({
            gardens: state.gardens.filter(garden => garden.id !== gardenId),
            isLoading: false,
          }));
        } catch (error: any) {
          console.error('Delete garden error:', error);
          set({
            error: error.message || 'Failed to delete garden',
            isLoading: false
          });
        }
      },

      // Plant CRUD operations
      addPlant: async (plant) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = plant.farmId || get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          // Create plant data according to new structure
          const plantData: any = {
            name: plant.name || 'New Plant',
            species: plant.species,
            variety: plant.variety || '',
            category: 'crop',
            quantity: 1,
            identificationID: plant?.identificationID, // Removed because itemId does not exist on Plant
            plantingDate: plant.plantedDate ? new Date(plant.plantedDate) : new Date(),
            expectedHarvestDate: plant.expectedHarvestDate ? new Date(plant.expectedHarvestDate) : '',
            growthStage: plant.status || 'seedling',
            healthStatus: plant.health || 'good',
            location: {
              zoneId: plant.fieldId || plant.gardenId || ''
            },
            helpGuide: plant?.helpGuide ||[],
            notes: plant.notes || '',
            addedBy: '', // This should be the current user ID
            addedAt: new Date(),
          };

        
          if (plant.image) plantData.photoURL = plant.image;

          const docRef = await addDoc(collection(firestore, `farms/${farmId}/plants`), plantData);

          // Don't add to local state here - let the real-time listener handle it
          // This prevents duplication since the listener will process the new document

          set({ isLoading: false });

          return docRef.id;
        } catch (error: any) {
          console.error('Add plant error:', error);
          set({
            error: error.message || 'Failed to add plant',
            isLoading: false
          });
          return '';
        }
      },

      updatePlant: async (plantId, data) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          const plantRef = doc(firestore, `farms/${farmId}/plants`, plantId);

          // Create update data according to new structure
          const updateData: any = {};

          if (data.name) updateData.name = data.name;
          if (data.species) updateData.variety = data.species;
          if (data.variety) updateData.variety = data.variety;
          if (data.plantedDate) updateData.plantingDate = new Date(data.plantedDate);
          if (data.expectedHarvestDate) updateData.expectedHarvestDate = new Date(data.expectedHarvestDate);
          if (data.status) updateData.growthStage = data.status;
          if (data.health) updateData.healthStatus = data.health;
          if (data.gardenId) updateData['location.zoneId'] = data.gardenId;
          if (data.fieldId) updateData['location.zoneId'] = data.fieldId;
          if (data.notes) updateData.notes = data.notes;
          if (data.image) updateData.photoURL = data.image;
          // console.log({updateData})
          const docRef = await updateDoc(plantRef, updateData);
          const health = getLookupTitleLocal(data?.health)
          const status = getLookupTitleLocal(data?.status)

          set(state => ({
            plants: state.plants.map(plant =>
              plant.id === plantId ? { ...plant, ...data, health, status, updatedAt: new Date().toISOString() } : plant
            ),
            isLoading: false,
          }));
        } catch (error: any) {
          console.error('Update plant error:', error);
          set({
            error: error.message || 'Failed to update plant',
            isLoading: false
          });
        }
      },

      deletePlant: async (plantId) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          await deleteDoc(doc(firestore, `farms/${farmId}/plants`, plantId));

          set(state => ({
            plants: state.plants.filter(plant => plant.id !== plantId),
            isLoading: false,
          }));
        } catch (error: any) {
          console.error('Delete plant error:', error);
          set({
            error: error.message || 'Failed to delete plant',
            isLoading: false
          });
        }
      },

      // Animal CRUD operations
      addAnimal: async (animal) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = animal.farmId || get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          // Create inventory item with category 'animal' according to new structure
          const animalData: any = {
            name: animal.name || '',
            category: 'animal',
            type: animal.species || '',
            breed: animal.breed || '',
            gender: animal.gender || 'unknown',
            status: animal.status || 'healthy',
            purpose: animal.purpose || 'meat',
            identificationNumber: animal.identificationNumber || '',
            quantity: 1,
            unitOfMeasure: 'head',
            acquisitionDate: animal.birthDate ? new Date(animal.birthDate) : new Date(),
            location: {
              zoneId: animal.fieldId || ''
            },
            notes: animal.notes || '',
            addedBy: '', // This should be the current user ID
            addedAt: new Date(),
          };

          if (animal.image) animalData.photoURL = animal.image;

          const docRef = await addDoc(collection(firestore, `farms/${farmId}/animals`), animalData);

          const newAnimal: Animal = {
            id: docRef.id,
            name: animalData.name,
            species: animalData.type,
            breed: animalData.breed,
            birthDate: animalData.acquisitionDate.toISOString(),
            gender: animalData.gender,
            status: animalData.status,
            purpose: animalData.purpose,
            identificationNumber: animalData.identificationNumber,
            farmId: farmId,
            fieldId: animal.fieldId,
            notes: animalData.notes,
            image: animal.image,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };

          set(state => ({
            animals: [...state.animals, newAnimal],
            isLoading: false,
          }));

          return docRef.id;
        } catch (error: any) {
          console.error('Add animal error:', error);
          set({
            error: error.message || 'Failed to add animal',
            isLoading: false
          });
          return '';
        }
      },

      updateAnimal: async (animalId, data) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          const animalRef = doc(firestore, `farms/${farmId}/animals`, animalId);

          // Create update data according to new structure
          const updateData: any = {};

          if (data.name) updateData.name = data.name;
          if (data.species) updateData.type = data.species;
          if (data.breed) updateData.breed = data.breed;
          if (data.birthDate) updateData.acquisitionDate = new Date(data.birthDate);
          if (data.gender) updateData.gender = data.gender;
          if (data.status) updateData.status = data.status;
          if (data.purpose) updateData.purpose = data.purpose;
          // if (data.identificationNumber) updateData.identificationNumber = data.identificationNumber;
          if (data.fieldId) updateData['location.zoneId'] = data.fieldId;
          if (data.notes) updateData.notes = data.notes;
          if (data.image) updateData.photoURL = data.image;

          await updateDoc(animalRef, updateData);

          set(state => ({
            animals: state.animals.map(animal =>
              animal.id === animalId ? { ...animal, ...data, updatedAt: new Date().toISOString() } : animal
            ),
            isLoading: false,
          }));
        } catch (error: any) {
          console.error('Update animal error:', error);
          set({
            error: error.message || 'Failed to update animal',
            isLoading: false
          });
        }
      },

      deleteAnimal: async (animalId) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          await deleteDoc(doc(firestore, `farms/${farmId}/animals`, animalId));

          set(state => ({
            animals: state.animals.filter(animal => animal.id !== animalId),
            isLoading: false,
          }));
        } catch (error: any) {
          console.error('Delete animal error:', error);
          set({
            error: error.message || 'Failed to delete animal',
            isLoading: false
          });
        }
      },
      addEquipment: async (equipment) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = equipment.farmId || get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          // Create inventory item with category 'equipment' according to new structure
          const equipmentData: any = {
            name: equipment.name || 'New Equipment',
            type: equipment.type || 'tool',
            manufacturer: equipment.manufacturer || '',
            model: equipment.model || '',
            status: equipment.status || 'operational',
            quantity: 1,
            unitOfMeasure: 'unit',
            acquisitionDate: equipment.purchaseDate ? new Date(equipment.purchaseDate) : new Date(),
            acquisitionCost: equipment.purchasePrice || 0,
            location: {
              zoneId: equipment.location || ''
            },
            notes: equipment.notes || '',
            addedBy: '', // This should be the current user ID
            addedAt: new Date(),
          };

          if (equipment.lastMaintenanceDate) {
            equipmentData.lastMaintenanceDate = new Date(equipment.lastMaintenanceDate);
          }

          if (equipment.nextMaintenanceDate) {
            equipmentData.nextMaintenanceDate = new Date(equipment.nextMaintenanceDate);
          }

          if (equipment.image) equipmentData.photoURL = equipment.image;

          const docRef = await addDoc(collection(firestore, `farms/${farmId}/machinery`), equipmentData);

          const newEquipment: Equipment = {
            id: docRef.id,
            name: equipmentData.name,
            type: equipmentData.type,
            manufacturer: equipmentData.manufacturer,
            model: equipmentData.model,
            purchaseDate: equipmentData.acquisitionDate.toISOString(),
            purchasePrice: equipmentData.acquisitionCost,
            status: equipmentData.status,
            lastMaintenanceDate: equipmentData.lastMaintenanceDate?.toISOString(),
            nextMaintenanceDate: equipmentData.nextMaintenanceDate?.toISOString(),
            farmId: farmId,
            location: equipment.location,
            notes: equipmentData.notes,
            image: equipment.image,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };

          set(state => ({
            equipment: [...state.equipment, newEquipment],
            isLoading: false,
          }));

          return docRef.id;
        } catch (error: any) {
          console.error('Add equipment error:', error);
          set({
            error: error.message || 'Failed to add equipment',
            isLoading: false
          });
          return '';
        }
      },

      updateEquipment: async (equipmentId, data) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          const equipmentRef = doc(firestore, `farms/${farmId}/machinery`, equipmentId);

          // Create update data according to new structure
          const updateData: any = {};

          if (data.name) updateData.name = data.name;
          if (data.type) updateData.type = data.type;
          if (data.manufacturer) updateData.manufacturer = data.manufacturer;
          if (data.model) updateData.model = data.model;
          if (data.purchaseDate) updateData.acquisitionDate = new Date(data.purchaseDate);
          if (data.purchasePrice !== undefined) updateData.acquisitionCost = data.purchasePrice;
          if (data.status) updateData.status = data.status;
          if (data.lastMaintenanceDate) updateData.lastMaintenanceDate = new Date(data.lastMaintenanceDate);
          if (data.nextMaintenanceDate) updateData.nextMaintenanceDate = new Date(data.nextMaintenanceDate);
          if (data.location) updateData['location.zoneId'] = data.location;
          if (data.notes) updateData.notes = data.notes;
          if (data.image) updateData.photoURL = data.image;

          await updateDoc(equipmentRef, updateData);

          set(state => ({
            equipment: state.equipment.map(equip =>
              equip.id === equipmentId ? { ...equip, ...data, updatedAt: new Date().toISOString() } : equip
            ),
            isLoading: false,
          }));
        } catch (error: any) {
          console.error('Update equipment error:', error);
          set({
            error: error.message || 'Failed to update equipment',
            isLoading: false
          });
        }
      },

      deleteEquipment: async (equipmentId) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          await deleteDoc(doc(firestore, `farms/${farmId}/machinery`, equipmentId));

          set(state => ({
            equipment: state.equipment.filter(equip => equip.id !== equipmentId),
            isLoading: false,
          }));
        } catch (error: any) {
          console.error('Delete equipment error:', error);
          set({
            error: error.message || 'Failed to delete equipment',
            isLoading: false
          });
        }
      },
      // Add to FarmState interface
      getFieldCrops: async (fieldId: string) => {
        set({ isLoading: true });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          // Get crops from Firestore
          const cropsSnapshot = await getDocs(
            query(
              collection(firestore, `farms/${farmId}/zones/${fieldId}/crops`),
              // orderBy('plantedDate', 'desc')
            )
          );

          const crops: Crop[] = [];
          cropsSnapshot.forEach(doc => {
            crops.push({ id: doc.id, ...doc.data() } as Crop);
          });

          // Find active crop
          const activeCrop = crops.find(crop => crop.status === 'active') || null;

          // Update local state
          set(state => ({
            crops: [...state.crops.filter(c => c.fieldId !== fieldId), ...crops],
            isLoading: false
          }));

          return { activeCrop, crops };
        } catch (error: any) {
          console.error('Get field crops error:', error);
          set({
            error: error.message || 'Failed to get field crops',
            isLoading: false
          });
          return { activeCrop: null, crops: [] };
        }
      },
      getCropById: async (fieldId: string, cropId: string): Promise<Crop | null> => {
        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) throw new Error('No farm selected');
          const cropRef = doc(firestore, `farms/${farmId}/zones/${fieldId}/crops/${cropId}`);
          const cropSnap = await getDoc(cropRef);

          if (cropSnap.exists()) {
            const data = cropSnap.data();
            // Ensure all required Crop fields are present
            return {
              id: cropSnap.id,
              fieldId: fieldId,
              cropType: data.cropType,
              status: data.status,
              plantedDate: data.plantedDate?.toDate?.() ? data.plantedDate.toDate().toISOString() : (data.plantedDate || ''),
              expectedHarvestDate: data.expectedHarvestDate?.toDate?.() ? data.expectedHarvestDate.toDate().toISOString() : (data.expectedHarvestDate || ''),
              actualHarvestDate: data.actualHarvestDate?.toDate?.() ? data.actualHarvestDate.toDate().toISOString() : (data.actualHarvestDate || undefined),
              variety: data.variety || '',
              name: data.name || '',
              notes: data.notes || '',
              images: data.images || [],
              statusHistory: data.statusHistory || [],
              createdAt: data.createdAt?.toDate?.() ? data.createdAt.toDate().toISOString() : (data.createdAt || ''),
              updatedAt: data.updatedAt?.toDate?.() ? data.updatedAt.toDate().toISOString() : (data.updatedAt || ''),
              // Add any other required fields from your Crop type here
              // If your Crop type has more required fields, add them accordingly
            };
          } else {
            return null;
          }
        } catch (error) {
          console.error('Error fetching crop:', error);
          return null;
        }
      },
      addCropToField: async (fieldId: string, cropData: any) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          console.log('Adding crop to field:', { fieldId, cropData, farmId });

          // Check if there's already an active crop
          const { activeCrop } = await get().getFieldCrops(fieldId);

          if (activeCrop) {
            throw new Error('This field already has an active crop. Please harvest it first.');
          }

          // Add crop to Firestore with the exact structure provided
          const cropPath = `farms/${farmId}/zones/${fieldId}/crops/${cropData.id}`;
          console.log('Adding crop to path:', cropPath);

          await setDoc(
            doc(firestore, cropPath),
            cropData
          );

          // Update field with activeCropId
          const fieldPath = `farms/${farmId}/zones/${fieldId}`;
          console.log('Updating field activeCropId:', { fieldPath, activeCropId: cropData.id });

          await updateDoc(
            doc(firestore, fieldPath),
            { activeCropId: cropData.id }
          );

          // Update local state
          set(state => ({
            crops: [...state.crops, cropData],
            fields: state.fields.map(field =>
              field.id === fieldId
                ? { ...field, activeCropId: cropData.id }
                : field
            ),
            isLoading: false
          }));

          console.log('Crop added successfully. Local state updated.');
          console.log('Updated field activeCropId:', cropData.id);

          // Refresh field data to ensure we have the latest
          await get().fetchFields(farmId);

          // Specifically refresh the crops for this field
          await get().getFieldCrops(fieldId);

          console.log('Data refreshed after crop addition.');

        } catch (error: any) {
          console.error('Add crop error:', error);
          set({
            error: error.message || 'Failed to add crop',
            isLoading: false
          });
          throw error;
        }
      },

      updateCrop: async (cropId: string, cropData: any) => {
        set({ isLoading: true });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          // Find the crop to get its fieldId
          const crop = get().crops.find(c => c.id === cropId);
          if (!crop) {
            throw new Error('Crop not found');
          }

          const cropRef = doc(firestore, `farms/${farmId}/zones/${crop.fieldId}/crops/${cropId}`);

          await updateDoc(cropRef, cropData);

          // Update local state
          set(state => ({
            crops: state.crops.map(crop =>
              crop.id === cropId
                ? { ...crop, ...cropData }
                : crop
            ),
            isLoading: false
          }));
        } catch (error: any) {
          console.error('Update crop error:', error);
          set({
            error: error.message || 'Failed to update crop',
            isLoading: false
          });
          throw error;
        }
      },

      markCropAsHarvested: async (cropId: string, harvestData: any) => {
        set({ isLoading: true });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          // Find the crop to get its fieldId
          const crop = get().crops.find(c => c.id === cropId);
          if (!crop) {
            throw new Error('Crop not found');
          }

          const cropRef = doc(firestore, `farms/${farmId}/zones/${crop.fieldId}/crops/${cropId}`);

          await updateDoc(cropRef, { status: 'harvested', ...harvestData });

          // Update local state
          set(state => ({
            crops: state.crops.map(crop =>
              crop.id === cropId
                ? { ...crop, status: 'harvested', ...harvestData }
                : crop
            ),
            isLoading: false
          }));
        } catch (error: any) {
          console.error('Mark crop as harvested error:', error);
          set({
            error: error.message || 'Failed to mark crop as harvested',
            isLoading: false
          });
          throw error;
        }
      },
      // Yield CRUD operations
      addYield: async (yieldData) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = yieldData.farmId || get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          // Create activity log with action 'harvest' according to new structure
          const harvestData: any = {
            userId: '', // This should be the current user ID
            userName: '', // This should be the user's name
            action: 'harvest',
            targetType: yieldData.cropType || 'crop',
            targetId: yieldData.fieldId || yieldData.gardenId || '',
            details: yieldData.name || 'Harvest',
            timestamp: yieldData.harvestDate ? new Date(yieldData.harvestDate) : new Date(),
            quantity: yieldData.quantity || 0,
            unit: yieldData.unit || 'kg',
            quality: yieldData.quality || 'good',
            notes: yieldData.notes || '',
            location: {
              zoneId: yieldData.fieldId || '',
              gardenId: yieldData.gardenId || ''
            }
          };

          if (yieldData.image) harvestData.photoURL = yieldData.image;


        } catch (error: any) {
          console.error('Add yield error:', error);
          set({
            error: error.message || 'Failed to add yield',
            isLoading: false
          });
          return '';
        }
      },


      updateYield: async (yieldId, data) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          const yieldRef = doc(firestore, `farms/${farmId}/activity_logs`, yieldId);

          // Create update data according to new structure
          const updateData: any = {};

          if (data.name) updateData.details = data.name;
          if (data.cropType) updateData.targetType = data.cropType;
          if (data.harvestDate) updateData.timestamp = new Date(data.harvestDate);
          if (data.quantity !== undefined) updateData.quantity = data.quantity;
          if (data.unit) updateData.unit = data.unit;
          if (data.quality) updateData.quality = data.quality;
          if (data.fieldId) updateData['location.zoneId'] = data.fieldId;
          if (data.gardenId) updateData['location.gardenId'] = data.gardenId;
          if (data.notes) updateData.notes = data.notes;
          if (data.image) updateData.photoURL = data.image;

          await updateDoc(yieldRef, updateData);

          set(state => ({
            yields: state.yields.map(yieldItem =>
              yieldItem.id === yieldId ? { ...yieldItem, ...data, updatedAt: new Date().toISOString() } : yieldItem
            ),
            isLoading: false,
          }));
        } catch (error: any) {
          console.error('Update yield error:', error);
          set({
            error: error.message || 'Failed to update yield',
            isLoading: false
          });
        }
      },

      deleteYield: async (yieldId) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          await deleteDoc(doc(firestore, `farms/${farmId}/activity_logs`, yieldId));

          set(state => ({
            yields: state.yields.filter(yieldItem => yieldItem.id !== yieldId),
            isLoading: false,
          }));
        } catch (error: any) {
          console.error('Delete yield error:', error);
          set({
            error: error.message || 'Failed to delete yield',
            isLoading: false
          });
        }
      },

      // Farm Permission operations
      addFarmPermission: async (permission) => {
        set({ isLoading: true, error: null });

        try {
          // Instead of creating a separate permission document, add farmId to user's assignedFarmIds
          const userRef = doc(firestore, 'users', permission.userId || '');

          // Get current user data
          const userDoc = await getDoc(userRef);

          if (!userDoc.exists()) {
            throw new Error('User not found');
          }

          const userData = userDoc.data();
          const currentAssignedFarmIds = userData.assignedFarmIds || [];

          // Check if farm is already assigned to avoid duplicates
          if (!currentAssignedFarmIds.includes(permission.farmId)) {
            // Update user with farm assignment
            await updateDoc(userRef, {
              assignedFarmIds: [...currentAssignedFarmIds, permission.farmId],
              role: permission.role || userData.role || 'caretaker',
              assignedBy: permission.assignedBy || '',
              assignedAt: new Date(),
              updatedAt: new Date()
            });
          }

          // Create a permission object for state
          const newPermission: FarmPermission = {
            id: permission.userId || '', // Using user ID as permission ID
            farmId: permission.farmId || '',
            userId: permission.userId || '',
            role: permission.role || 'caretaker',
            status: permission.status || 'active',
            assignedBy: permission.assignedBy || '',
            assignedAt: new Date().toISOString(),
            areas: permission.areas || []
          };

          set(state => ({
            farmPermissions: [...state.farmPermissions, newPermission],
            isLoading: false,
          }));

          return permission.userId || '';
        } catch (error: any) {
          console.error('Add farm permission error:', error);
          set({
            error: error.message || 'Failed to add farm permission',
            isLoading: false
          });
          return '';
        }
      },

      updateFarmPermission: async (permissionId, data) => {
        set({ isLoading: true, error: null });

        try {
          // In this model, permissionId is the userId
          const userRef = doc(firestore, 'users', permissionId);

          // Create update data
          const updateData: any = {
            updatedAt: new Date()
          };

          if (data.role) updateData.role = data.role;
          if (data.status) updateData.status = data.status;

          await updateDoc(userRef, updateData);

          set(state => ({
            farmPermissions: state.farmPermissions.map(permission =>
              permission.id === permissionId ? { ...permission, ...data } : permission
            ),
            isLoading: false,
          }));
        } catch (error: any) {
          console.error('Update farm permission error:', error);
          set({
            error: error.message || 'Failed to update farm permission',
            isLoading: false
          });
        }
      },

      deleteFarmPermission: async (permissionId) => {
        set({ isLoading: true, error: null });

        try {
          // Find the permission to get farmId
          const permission = get().farmPermissions.find(p => p.id === permissionId);

          if (!permission) {
            throw new Error('Permission not found');
          }

          // Remove farmId from user's assignedFarmIds
          const userRef = doc(firestore, 'users', permissionId); // permissionId is userId
          const userDoc = await getDoc(userRef);

          if (userDoc.exists()) {
            const userData = userDoc.data();
            const updatedFarmIds = (userData.assignedFarmIds || []).filter(id => id !== permission.farmId);

            await updateDoc(userRef, {
              assignedFarmIds: updatedFarmIds,
              updatedAt: new Date()
            });
          }

          set(state => ({
            farmPermissions: state.farmPermissions.filter(p => p.id !== permissionId),
            isLoading: false,
          }));
        } catch (error: any) {
          console.error('Delete farm permission error:', error);
          set({
            error: error.message || 'Failed to delete farm permission',
            isLoading: false
          });
        }
      },

      // User management
      // inviteUserToFarm: async (farmId, email, phone, role, name,newUserId) => {
      //   set({ isLoading: true, error: null });

      //   try {
      //     // Check if user exists
      //     const usersQuery = query(
      //       collection(firestore, 'users'),
      //       where('email', '==', email)
      //     );

      //     const usersSnapshot = await getDocs(usersQuery);
      //     let userId = '';

      //     if (usersSnapshot.empty) {
      //       // Create a new user
      //       const userData = {
      //         email: email,
      //         name: name,
      //         displayName: name,
      //         role: role,
      //         phone: phone,
      //         phoneNumber: phone,
      //         preferredLanguage: 'en',
      //         createdAt: new Date(),
      //         updatedAt: new Date(),
      //         isInvited: true,
      //         invitedBy: get().currentFarm?.ownerId || '',
      //         invitedAt: new Date(),
      //         assignedFarmIds: [farmId], // Add farmId to assignedFarmIds array
      //         assignedBy: get().currentFarm?.ownerId || '',
      //         assignedAt: new Date()
      //       };

      //       const userRef = await addDoc(collection(firestore, 'users'), userData);
      //       userId = userRef.id;

      //       // Create a notification for the user
      //       const notificationData = {
      //         userId: userId,
      //         farmId: farmId,
      //         type: 'invitation',
      //         title: 'Farm Invitation',
      //         message: `You have been invited to join a farm as ${role}`,
      //         createdAt: new Date(),
      //         read: false,
      //         data: {
      //           farmId: farmId,
      //           role: role
      //         }
      //       };

      //       await addDoc(collection(firestore, 'notifications'), notificationData);
      //     } else {
      //       // User exists, get their ID
      //       userId = usersSnapshot.docs[0].id;

      //       // Update user with phone if not set and add farmId to assignedFarmIds
      //       const userData = usersSnapshot.docs[0].data();
      //       const currentAssignedFarmIds = userData.assignedFarmIds || [];

      //       // Check if farm is already assigned to avoid duplicates
      //       if (!currentAssignedFarmIds.includes(farmId)) {
      //         const updateData: any = {
      //           assignedFarmIds: [...currentAssignedFarmIds, farmId],
      //           role: role, // Update role based on invitation
      //           assignedBy: get().currentFarm?.ownerId || '',
      //           assignedAt: new Date(),
      //           updatedAt: new Date()
      //         };

      //         if (!userData.phone) {
      //           updateData.phone = phone;
      //           updateData.phoneNumber = phone;
      //         }

      //         await updateDoc(doc(firestore, 'users', userId), updateData);

      //         // Create a notification for the user
      //         const notificationData = {
      //           userId: userId,
      //           farmId: farmId,
      //           type: 'invitation',
      //           title: 'Farm Invitation',
      //           message: `You have been invited to join a farm as ${role}`,
      //           createdAt: new Date(),
      //           read: false,
      //           data: {
      //             farmId: farmId,
      //             role: role
      //           }
      //         };

      //         await addDoc(collection(firestore, 'notifications'), notificationData);
      //       }
      //     }

      //     // Create a permission object for state
      //     const newPermission: FarmPermission = {
      //       id: userId,
      //       farmId: farmId,
      //       userId: userId,
      //       role: role,
      //       status: 'active',
      //       assignedBy: get().currentFarm?.ownerId || '',
      //       assignedAt: new Date().toISOString(),
      //       areas: []
      //     };

      //     set(state => ({
      //       farmPermissions: [...state.farmPermissions, newPermission],
      //       isLoading: false,
      //     }));
      //   } catch (error: any) {
      //     console.error('Invite user error:', error);
      //     set({
      //       error: error.message || 'Failed to invite user',
      //       isLoading: false
      //     });
      //   }
      // },
      // inviteUserToFarm: async (farmId, email, phone, role, name, newUserId) => {
      //   set({ isLoading: true, error: null });

      //   try {
      //     const usersQuery = query(
      //       collection(firestore, 'users'),
      //       where('email', '==', email)
      //     );

      //     const usersSnapshot = await getDocs(usersQuery);
      //     let userId = '';

      //     if (usersSnapshot.empty) {
      //       // Create new user with specific UID
      //       const userData = {
      //         email,
      //         name,
      //         displayName: name,
      //         role,
      //         phone,
      //         phoneNumber: phone,
      //         preferredLanguage: 'en',
      //         createdAt: new Date(),
      //         updatedAt: new Date(),
      //         isInvited: true,
      //         invitedBy: get().currentFarm?.ownerId || '',
      //         invitedAt: new Date(),
      //         assignedFarmIds: [farmId],
      //         assignedBy: get().currentFarm?.ownerId || '',
      //         assignedAt: new Date()
      //       };

      //       const userRef = doc(firestore, 'users', newUserId); // Use provided UID
      //       await setDoc(userRef, userData);
      //       userId = newUserId;

      //       // Create notification
      //       const notificationData = {
      //         userId,
      //         farmId,
      //         type: 'invitation',
      //         title: 'Farm Invitation',
      //         message: `You have been invited to join a farm as ${role}`,
      //         createdAt: new Date(),
      //         read: false,
      //         data: { farmId, role }
      //       };

      //       await addDoc(collection(firestore, 'notifications'), notificationData);
      //     } else {
      //       // User exists
      //       userId = usersSnapshot.docs[0].id;
      //       const userData = usersSnapshot.docs[0].data();
      //       const currentAssignedFarmIds = userData.assignedFarmIds || [];

      //       if (!currentAssignedFarmIds.includes(farmId)) {
      //         const updateData: any = {
      //           assignedFarmIds: [...currentAssignedFarmIds, farmId],
      //           role,
      //           assignedBy: get().currentFarm?.ownerId || '',
      //           assignedAt: new Date(),
      //           updatedAt: new Date()
      //         };

      //         if (!userData.phone) {
      //           updateData.phone = phone;
      //           updateData.phoneNumber = phone;
      //         }

      //         await updateDoc(doc(firestore, 'users', userId), updateData);

      //         // Notification
      //         const notificationData = {
      //           userId,
      //           farmId,
      //           type: 'invitation',
      //           title: 'Farm Invitation',
      //           message: `You have been invited to join a farm as ${role}`,
      //           createdAt: new Date(),
      //           read: false,
      //           data: { farmId, role }
      //         };

      //         await addDoc(collection(firestore, 'notifications'), notificationData);
      //       }
      //     }

      //     // const newPermission: FarmPermission = {
      //     //   id: userId,
      //     //   farmId,
      //     //   userId,
      //     //   role,
      //     //   status: 'active',
      //     //   assignedBy: get().currentFarm?.ownerId || '',
      //     //   assignedAt: new Date().toISOString(),
      //     //   areas: []
      //     // };

      //     // set(state => ({
      //     //   farmPermissions: [...state.farmPermissions, newPermission],
      //     //   isLoading: false,
      //     // }));
      //   } catch (error: any) {
      //     console.error('Invite user error:', error);
      //     set({
      //       error: error.message || 'Failed to invite user',
      //       isLoading: false
      //     });
      //   }
      // },
      // Update the inviteUserToFarm method to handle user creation with multiple farms
      // inviteUserToFarm: async (farmId, email, phone, role, name, userId) => {
      //   set({ isLoading: true, error: null });

      //   try {
      //     // Check if user already exists
      //     const userRef = doc(firestore, 'users', userId);
      //     const userDoc = await getDoc(userRef);

      //     if (userDoc.exists()) {
      //       // User exists, update their data
      //       const userData = userDoc.data();
      //       const currentAssignedFarmIds = userData.assignedFarmIds || [];

      //       // Check if farm is already assigned to avoid duplicates
      //       if (!currentAssignedFarmIds.includes(farmId)) {
      //         // Update user with farm assignment
      //         await updateDoc(userRef, {
      //           assignedFarmIds: [...currentAssignedFarmIds, farmId],
      //           role: role || userData.role || 'caretaker',
      //           phoneNumber: phone || userData.phoneNumber || '',
      //           displayName: name || userData.displayName || '',
      //           assignedBy: get().currentFarm?.ownerId || '',
      //           assignedAt: new Date(),
      //           updatedAt: new Date()
      //         });
      //       }
      //     } else {
      //       // Create new user document
      //       await setDoc(userRef, {
      //         email,
      //         phoneNumber: phone,
      //         displayName: name,
      //         role,
      //         assignedFarmIds: [farmId],
      //         assignedBy: get().currentFarm?.ownerId || '',
      //         assignedAt: new Date(),
      //         createdAt: new Date(),
      //         updatedAt: new Date(),
      //         status: 'active'
      //       });
      //     }

      //     // Create a notification for the user
      //     const notificationData = {
      //       userId,
      //       farmId,
      //       type: 'farm_invitation',
      //       title: 'Farm Invitation',
      //       message: `You have been invited to join ${get().currentFarm?.name || 'a farm'}`,
      //       createdAt: new Date(),
      //       read: false,
      //       data: {
      //         farmId,
      //         role
      //       }
      //     };
      //     logEvent('farm_invitation_updating_role', {
      //       userId,
      //       farmId,
      //       type: 'farm_invitation',
      //       title: 'Farm Invitation',
      //       message: `You have been invited to join ${get().currentFarm?.name || 'a farm'}`,
      //       createdAt: new Date(),
      //       read: false,
      //       data: {
      //         farmId,
      //         role
      //       }
      //     });
      //     await addDoc(collection(firestore, 'notifications'), notificationData);

      //     set({ isLoading: false });
      //   } catch (error: any) {
      //     console.error('Invite user error:', error);
      //     set({
      //       error: error.message || 'Failed to invite user',
      //       isLoading: false
      //     });
      //     throw error;
      //   }
      // },
      inviteUserToFarm: async (farmId, email, phone, role, name, userId, updatedAssignedFarms = []) => {
        set({ isLoading: true, error: null });

        try {
          const userRef = doc(firestore, 'users', userId);
          const userDoc = await getDoc(userRef);

          const currentFarm = get().currentFarm;
          const now = new Date();

          if (userDoc.exists()) {
            const userData = userDoc.data();
            const currentAssignedFarmIds = userData.assignedFarmIds || [];

            // 🧠 If updatedAssignedFarms is provided, replace with it; otherwise add farmId
            const newAssignedFarmIds = updatedAssignedFarms.length
              ? updatedAssignedFarms
              : currentAssignedFarmIds.includes(farmId)
                ? currentAssignedFarmIds
                : [...currentAssignedFarmIds, farmId];

            await updateDoc(userRef, {
              assignedFarmIds: newAssignedFarmIds,
              role: role || userData.role || 'caretaker',
              phoneNumber: phone || userData.phoneNumber || '',
              displayName: name || userData.displayName || '',
              assignedBy: currentFarm?.ownerId || '',
              assignedAt: now,
              updatedAt: now
            });
          } else {
            // 🧪 New user
            const assignedFarms = updatedAssignedFarms.length ? updatedAssignedFarms : [farmId];
            await setDoc(userRef, {
              email,
              phoneNumber: phone,
              displayName: name,
              role: role || 'caretaker',
              assignedFarmIds: assignedFarms,
              assignedBy: currentFarm?.ownerId || '',
              assignedAt: now,
              createdAt: now,
              updatedAt: now,
              status: 'active'
            });
          }

          // 🔔 Notification
          const notificationData = {
            userId,
            farmId,
            type: 'farm_invitation',
            title: 'Farm Invitation',
            message: `You have been invited to join ${currentFarm?.name || 'a farm'}`,
            createdAt: now,
            read: false,
            data: {
              farmId,
              role
            }
          };

          logEvent('farm_invitation_updating_role', notificationData);
          await addDoc(collection(firestore, 'notifications'), notificationData);

          set({ isLoading: false });
        } catch (error: any) {
          console.error('Invite user error:', error);
          set({
            error: error.message || 'Failed to invite user',
            isLoading: false
          });
          throw error;
        }
      },


      removeUserFromFarm: async (farmId, userId) => {
        set({ isLoading: true, error: null });

        try {
          // Remove farmId from user's assignedFarmIds
          const userRef = doc(firestore, 'users', userId);
          const userDoc = await getDoc(userRef);

          if (!userDoc.exists()) {
            throw new Error('User not found');
          }

          const userData = userDoc.data();
          const updatedFarmIds = (userData.assignedFarmIds || []).filter(id => id !== farmId);

          await updateDoc(userRef, {
            assignedFarmIds: updatedFarmIds,
            updatedAt: new Date()
          });

          // Create a notification for the user
          const notificationData = {
            userId: userId,
            farmId: farmId,
            type: 'removed_from_farm',
            title: 'Removed from Farm',
            message: 'You have been removed from a farm',
            createdAt: new Date(),
            read: false,
            data: {
              farmId: farmId
            }
          };

          await addDoc(collection(firestore, 'notifications'), notificationData);

          set(state => ({
            farmPermissions: state.farmPermissions.filter(
              permission => !(permission.farmId === farmId && permission.userId === userId)
            ),
            isLoading: false,
          }));
        } catch (error: any) {
          console.error('Remove user error:', error);
          set({
            error: error.message || 'Failed to remove user',
            isLoading: false
          });
        }
      },

      // Add these implementations to the store

      markPlantInactive: async (plantId, data) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          const plantRef = doc(firestore, `farms/${farmId}/plants`, plantId);
          const now = new Date();
          const userId = useAuthStore.getState().user?.id || '';

          const updateData = {
            isInactive: true,
            inactiveReason: data.reason,
            inactiveDate: now,
            inactiveNotes: data.notes || '',
            inactiveImage: data.image || '',
            markedByUserId: userId,
            lastModified: now
          };

          await updateDoc(plantRef, updateData);

          // Create activity log for plant inactivation
          const activityLogData = {

            title: 'marked_inactive',
            decrption: `Marked plant as inactive: ${data.reason}`,
            CreatedAt: new Date(),
            Createdby: userId,// useAuthStore.getState().user?.name || ''
            // UpdatedAt: new Date(),
            // Updatedby: user?.id,
            AppType: "3",
            // userId,
            // userName: useAuthStore.getState().user?.name || '',
            // action: 'marked_inactive',
            // targetType: 'plant',
            // targetId: plantId,
            // details: `Marked plant as inactive: ${data.reason}`,
            // timestamp: now,
            // notes: data.notes || '',
            // location: {
            //   zoneId: get().plants.find(p => p.id === plantId)?.fieldId ||
            //     get().plants.find(p => p.id === plantId)?.gardenId || ''
            // }
          };

          if (data.image) {
            activityLogData.photoURL = data.image;
          }

          await addDoc(collection(firestore, `farms/${farmId}/activities`), activityLogData);

          // Update local state
          set(state => ({
            plants: state.plants.map(plant =>
              plant.id === plantId
                ? {
                  ...plant,
                  isInactive: true,
                  inactiveReason: data.reason,
                  inactiveDate: now.toISOString(),
                  inactiveNotes: data.notes,
                  inactiveImage: data.image,
                  markedByUserId: userId
                }
                : plant
            ),
            isLoading: false
          }));
        } catch (error: any) {
          console.error('Mark plant inactive error:', error);
          set({
            error: error.message || 'Failed to mark plant as inactive',
            isLoading: false
          });
        }
      },

      markAnimalInactive: async (animalId, data) => {
        set({ isLoading: true, error: null });
        // console.log('Starting markAnimalInactive function', { animalId, data });

        try {
          const farmId = get().currentFarm?.id;
          // console.log('Current farm ID:', farmId);

          if (!farmId) {
            throw new Error('No farm selected');
          }

          const animalRef = doc(firestore, `farms/${farmId}/animals`, animalId);
          const now = new Date();
          const userId = useAuthStore.getState().user?.id || '';
          // console.log('User ID:', userId);

          const updateData = {
            isInactive: true,
            inactiveReason: data.reason,
            inactiveDate: now,
            inactiveNotes: data.notes || '',
            inactiveImage: data.image || '',
            markedByUserId: userId,
            lastModified: now
          };
          // console.log('Update data prepared:', updateData);

          await updateDoc(animalRef, updateData);
          // console.log('Animal document updated successfully');

          const activityLogData = {
            title: 'marked_inactive',
            decrption: `Marked animal as inactive: ${data.reason}`,
            CreatedAt: new Date(),
            Createdby: userId,
            AppType: "3",
            // userId,
            // userName: useAuthStore.getState().user?.name || '',
            // action: 'marked_inactive',
            // targetType: 'animal',
            // targetId: animalId,
            // details: `Marked animal as inactive: ${data.reason}`,
            // timestamp: now,
            // notes: data.notes || '',
            // location: {
            //   zoneId: get().animals.find(a => a.id === animalId)?.locationId || ''
            // }
          };

          if (data.image) {
            activityLogData.photoURL = data.image;
          }
          // console.log('Activity log data prepared:', activityLogData);

          await addDoc(collection(firestore, `farms/${farmId}/activities`), activityLogData);
          // console.log('Activity log added successfully');

          set(state => ({
            animals: state.animals.map(animal =>
              animal.id === animalId
                ? {
                  ...animal,
                  isInactive: true,
                  inactiveReason: data.reason,
                  inactiveDate: now.toISOString(),
                  inactiveNotes: data.notes,
                  inactiveImage: data.image,
                  markedByUserId: userId
                }
                : animal
            ),
            isLoading: false
          }));
          // console.log('Local state updated successfully');
        } catch (error: any) {
          // console.error('Mark animal inactive error:', error);
          set({
            error: error.message || 'Failed to mark animal as inactive',
            isLoading: false
          });
        }
      },

      markEquipmentInactive: async (equipmentId, data) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          const equipmentRef = doc(firestore, `farms/${farmId}/machinery`, equipmentId);
          const now = new Date();
          const userId = useAuthStore.getState().user?.id || '';

          const updateData = {
            isInactive: true,
            inactiveReason: data.reason,
            inactiveDate: now,
            inactiveNotes: data.notes || '',
            inactiveImage: data.image || '',
            markedByUserId: userId,
            lastModified: now
          };

          await updateDoc(equipmentRef, updateData);

          // Create activity log for equipment inactivation
          const activityLogData = {
            title: 'marked_inactive',
            decrption: `Marked equipment as inactive: ${data.reason}`,
            CreatedAt: new Date(),
            Createdby: userId,
            AppType: "3",
            // userId,
            // userName: useAuthStore.getState().user?.name || '',
            // action: 'marked_inactive',
            // targetType: 'equipment',
            // targetId: equipmentId,
            // details: `Marked equipment as inactive: ${data.reason}`,
            // timestamp: now,
            // notes: data.notes || ''
          };

          if (data.image) {
            activityLogData.photoURL = data.image;
          }

          await addDoc(collection(firestore, `farms/${farmId}/activites`), activityLogData);

          // Update local state
          set(state => ({
            equipment: state.equipment.map(equip =>
              equip.id === equipmentId
                ? {
                  ...equip,
                  isInactive: true,
                  inactiveReason: data.reason,
                  inactiveDate: now.toISOString(),
                  inactiveNotes: data.notes,
                  inactiveImage: data.image,
                  markedByUserId: userId
                }
                : equip
            ),
            isLoading: false
          }));
        } catch (error: any) {
          console.error('Mark equipment inactive error:', error);
          set({
            error: error.message || 'Failed to mark equipment as inactive',
            isLoading: false
          });
        }
      },

      markGardenInactive: async (gardenId, data) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          const gardenRef = doc(firestore, `farms/${farmId}/zones`, gardenId);
          const now = new Date();
          const userId = useAuthStore.getState().user?.id || '';

          const updateData = {
            isInactive: true,
            inactiveReason: data.reason,
            inactiveDate: now,
            inactiveNotes: data.notes || '',
            inactiveImage: data.image || '',
            markedByUserId: userId,
            lastModified: now
          };

          await updateDoc(gardenRef, updateData);

          // Create activity log for garden inactivation
          const activityLogData = {
            title: 'marked_inactive',
            decrption: `Marked garden as inactive: ${data.reason}`,
            CreatedAt: new Date(),
            Createdby: userId,
            AppType: "3",
            // userId,
            // userName: useAuthStore.getState().user?.name || '',
            // action: 'marked_inactive',
            // targetType: 'garden',
            // targetId: gardenId,
            // details: `Marked garden as inactive: ${data.reason}`,
            // timestamp: now,
            // notes: data.notes || ''
          };

          if (data.image) {
            activityLogData.photoURL = data.image;
          }

          await addDoc(collection(firestore, `farms/${farmId}/activities`), activityLogData);

          // Update local state
          set(state => ({
            gardens: state.gardens.map(garden =>
              garden.id === gardenId
                ? {
                  ...garden,
                  isInactive: true,
                  inactiveReason: data.reason,
                  inactiveDate: now.toISOString(),
                  inactiveNotes: data.notes,
                  inactiveImage: data.image,
                  markedByUserId: userId
                }
                : garden
            ),
            isLoading: false
          }));

          // If cascade to plants is enabled, mark all plants in this garden as inactive
          if (data.cascadeToPlants) {
            const plantsInGarden = get().plants.filter(plant => plant.gardenId === gardenId);

            for (const plant of plantsInGarden) {
              await get().markPlantInactive(plant.id, {
                reason: `Garden marked inactive: ${data.reason}`,
                notes: `Automatically marked inactive because parent garden was marked inactive. ${data.notes || ''}`,
                image: data.image
              });
            }
          }
        } catch (error: any) {
          console.error('Mark garden inactive error:', error);
          set({
            error: error.message || 'Failed to mark garden as inactive',
            isLoading: false
          });
        }
      },

      markFieldInactive: async (fieldId, data) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          const fieldRef = doc(firestore, `farms/${farmId}/zones`, fieldId);
          const now = new Date();
          const userId = useAuthStore.getState().user?.id || '';

          const updateData = {
            isInactive: true,
            inactiveReason: data.reason,
            inactiveDate: now,
            inactiveNotes: data.notes || '',
            inactiveImage: data.image || '',
            markedByUserId: userId,
            lastModified: now
          };

          await updateDoc(fieldRef, updateData);

          // Create activity log for field inactivation
          const activityLogData = {
            title: 'marked_inactive',
            decrption: `Marked field as inactive: ${data.reason}`,
            CreatedAt: new Date(),
            Createdby: userId,
            AppType: "3",
            // userId,
            // userName: useAuthStore.getState().user?.name || '',
            // action: 'marked_inactive',
            // targetType: 'field',
            // targetId: fieldId,
            // details: `Marked field as inactive: ${data.reason}`,
            // timestamp: now,
            // notes: data.notes || ''
          };

          if (data.image) {
            activityLogData.photoURL = data.image;
          }

          await addDoc(collection(firestore, `farms/${farmId}/activities`), activityLogData);

          // Update local state
          set(state => ({
            fields: state.fields.map(field =>
              field.id === fieldId
                ? {
                  ...field,
                  isInactive: true,
                  inactiveReason: data.reason,
                  inactiveDate: now.toISOString(),
                  inactiveNotes: data.notes,
                  inactiveImage: data.image,
                  markedByUserId: userId
                }
                : field
            ),
            isLoading: false
          }));
        } catch (error: any) {
          console.error('Mark field inactive error:', error);
          set({
            error: error.message || 'Failed to mark field as inactive',
            isLoading: false
          });
        }
      },

      recordFieldHarvest: async (fieldId, data) => {
        set({ isLoading: true });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          const now = new Date();
          const userId = useAuthStore.getState().user?.id || '';

          // Create harvest record in the field's harvestHistory subcollection
          const harvestData = {
            harvestDate: new Date(data.harvestDate),
            season: data.season,
            yieldQuantity: data.yieldQuantity,
            yieldUnit: data.yieldUnit,
            cropType: data.cropType,
            notes: data.notes || '',
            images: data.images || [],
            createdAt: now,
            createdBy: userId
          };

          const harvestRef = await addDoc(
            collection(firestore, `farms/${farmId}/zones/${fieldId}/harvestHistory`),
            harvestData
          );

          // Get the active crop for this field
          const { activeCrop } = await get().getFieldCrops(fieldId);

          // If there's an active crop, mark it as harvested
          if (activeCrop) {
            const cropRef = doc(firestore, `farms/${farmId}/zones/${fieldId}/crops/${activeCrop.id}`);

            await updateDoc(cropRef, {
              status: 'harvested',
              actualHarvestDate: new Date(data.harvestDate),
              harvestQuantity: data.yieldQuantity,
              harvestUnit: data.yieldUnit,
              harvestNotes: data.notes || '',
              harvestImages: data.images || [],
              updatedAt: now
            });

            // Update field to remove activeCropId
            const fieldRef = doc(firestore, `farms/${farmId}/zones`, fieldId);
            await updateDoc(fieldRef, { activeCropId: null });

            // Update local state for the crop
            set(state => ({
              crops: state.crops.map(crop =>
                crop.id === activeCrop.id
                  ? {
                    ...crop,
                    status: 'harvested',
                    actualHarvestDate: data.harvestDate,
                    harvestQuantity: data.yieldQuantity,
                    harvestUnit: data.yieldUnit,
                    harvestNotes: data.notes || '',
                    harvestImages: data.images || []
                  }
                  : crop
              )
            }));
          }

          // Create inventory item for the harvest
          const inventoryData = {
            name: `${data.cropType} Harvest`,
            type: 'crop',
            quantity: data.yieldQuantity,
            unit: data.yieldUnit,
            source: 'field',
            sourceId: fieldId,
            harvestDate: new Date(data.harvestDate),
            createdAt: now,
            createdBy: userId,
            farmId: farmId,
            status: 'available'
          };

          await addDoc(collection(firestore, `farms/${farmId}/inventory`), inventoryData);

          // Create activity log for the harvest
          const activityLogData = {

            title: 'harvest',
            decrption: `Harvested ${data.cropType}`,
            CreatedAt: new Date(),
            Createdby: userId,
            AppType: "3",
            location: {
              zoneId: fieldId
            },
            quantity: data.yieldQuantity,
            // userId,
            // userName: useAuthStore.getState().user?.name || '',
            // action: 'harvest',
            // targetType: 'crop',
            // targetId: fieldId,
            // details: `Harvested ${data.cropType}`,
            // timestamp: now,
            // quantity: data.yieldQuantity,
            // unit: data.yieldUnit,
            // notes: data.notes || '',
            // location: {
            //   zoneId: fieldId
            // }
          };

          if (data.images && data.images.length > 0) {
            activityLogData.photoURL = data.images[0];
          }

          await addDoc(collection(firestore, `farms/${farmId}/activites`), activityLogData);

          // Update local state with new harvest record
          const newHarvestRecord = {
            id: harvestRef.id,
            harvestDate: data.harvestDate,
            season: data.season,
            yieldQuantity: data.yieldQuantity,
            yieldUnit: data.yieldUnit,
            cropType: data.cropType,
            notes: data.notes,
            images: data.images,
            fieldId: fieldId,
            createdAt: now.toISOString(),
            createdBy: userId
          };

          // Update the field's local state to include this harvest record
          set(state => ({
            fields: state.fields.map(field =>
              field.id === fieldId
                ? {
                  ...field,
                  activeCropId: null, // Remove active crop reference
                  harvestHistory: field.harvestHistory
                    ? [...field.harvestHistory, newHarvestRecord]
                    : [newHarvestRecord]
                }
                : field
            ),
            isLoading: false
          }));

          return harvestRef.id;
        } catch (error: any) {
          console.error('Record field harvest error:', error);
          set({
            error: error.message || 'Failed to record field harvest',
            isLoading: false
          });
          return '';
        }
      },
      getFieldHarvestHistory: async (fieldId) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;

          if (!farmId) {
            throw new Error('No farm selected');
          }

          const harvestHistoryRef = collection(
            firestore,
            `farms/${farmId}/zones/${fieldId}/harvestHistory`
          );

          const harvestSnapshot = await getDocs(harvestHistoryRef);
          const harvestRecords: HarvestRecord[] = [];

          harvestSnapshot.forEach((doc) => {
            const data = doc.data();
            harvestRecords.push({
              id: doc.id,
              harvestDate: data.harvestDate.toDate().toISOString(),
              season: data.season,
              yieldQuantity: data.yieldQuantity,
              yieldUnit: data.yieldUnit,
              cropType: data.cropType,
              notes: data.notes,
              images: data.images,
              fieldId: fieldId,
              createdAt: data.createdAt.toDate().toISOString(),
              createdBy: data.createdBy
            });
          });

          // Update the field's local state with harvest history
          set(state => ({
            fields: state.fields.map(field =>
              field.id === fieldId
                ? { ...field, harvestHistory: harvestRecords }
                : field
            ),
            isLoading: false
          }));

          return harvestRecords;
        } catch (error: any) {
          console.error('Get field harvest history error:', error);
          set({
            error: error.message || 'Failed to get field harvest history',
            isLoading: false
          });
          return [];
        }
      },


      ///financial module 

      // Add these methods to the store
      addCropTransaction: async (data: Omit<Transaction, 'id' | 'category' | 'isHighCost' | 'createdAt' | 'updatedAt'>) => {
        set({ isLoading: true, error: null });
        // console.log(data)
        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) {
            throw new Error('No farm selected');
          }

          // Use AI to classify the transaction
          const aiAnalysis = await analyzeTransactionWithAI(data.description);

          const now = new Date().toISOString();
          // const transactionId = uuidv4();

          // Determine if this is a high-cost input
          const isHighCost = data.amount > 500; // Threshold can be adjusted

          const transactionData: CropTransaction = {
            // id: transactionId,
            farmId,
            fieldId: data.fieldId,
            cropId: data.cropId,
            category: aiAnalysis.category as TransactionCategory,
            isHighCost,
            createdAt: now,
            updatedAt: now,
            ...data
          };

          // // Add to Firestore
          // await setDoc(
          //   doc(firestore, `farms/${farmId}/cropTransactions/${transactionId}`),
          //   transactionData
          // );
          // Add to Firestore with auto-generated ID
          const docRef = await addDoc(
            collection(firestore, `farms/${farmId}/cropTransactions`),
            transactionData
          );

          // Optionally get the generated ID
          const transactionId = docRef.id;
          // Update local state
          set(state => ({
            cropTransactions: [...state.cropTransactions, { ...transactionData, transactionId }],
            isLoading: false
          }));

          // Recalculate financials for this crop
          await get().calculateCropFinancials(data.cropId);

          return transactionData;
        } catch (error: any) {
          console.error('Add crop transaction error:', error);
          set({
            error: error.message || 'Failed to add crop transaction',
            isLoading: false
          });
          throw error;
        }
      },

      fetchCropTransactions: async (cropId?: string) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) {
            throw new Error('No farm selected');
          }

          let q;
          if (cropId) {
            q = query(
              collection(firestore, `farms/${farmId}/cropTransactions`),
              where('cropId', '==', cropId)
            );
          } else {
            q = collection(firestore, `farms/${farmId}/cropTransactions`);
          }

          const snapshot = await getDocs(q);
          const transactions: CropTransaction[] = [];

          snapshot.forEach(doc => {
            transactions.push({ id: doc.id, ...doc.data() } as CropTransaction);
          });

          // Update local state
          set(state => ({
            cropTransactions: cropId
              ? [...state.cropTransactions.filter(t => t.cropId !== cropId), ...transactions]
              : transactions,
            isLoading: false
          }));

          return transactions;
        } catch (error: any) {
          console.error('Fetch crop transactions error:', error);
          set({
            error: error.message || 'Failed to fetch crop transactions',
            isLoading: false
          });
          return [];
        }
      },

      calculateCropFinancials: async (cropId: string) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) {
            throw new Error('No farm selected');
          }

          // Fetch crop data
          const crop = get().getCrop(cropId);
          if (!crop) {
            throw new Error('Crop not found');
          }

          // Fetch field data to get size
          const field = get().getField(crop.fieldId);
          if (!field) {
            throw new Error('Field not found');
          }

          // Fetch transactions for this crop
          const transactions = await get().fetchCropTransactions(cropId);

          // Calculate total costs and revenue
          let totalCosts = 0;
          let totalRevenue = 0;
          const costBreakdown: Record<TransactionCategory, number> = {} as Record<TransactionCategory, number>;

          transactions.forEach(transaction => {
            if (transaction.category === 'harvest_sale') {
              totalRevenue += transaction.amount;
            } else {
              totalCosts += transaction.amount;

              // Add to cost breakdown
              if (!costBreakdown[transaction.category]) {
                costBreakdown[transaction.category] = 0;
              }
              costBreakdown[transaction.category] += transaction.amount;
            }
          });

          // Calculate expected revenue based on crop type and field size
          // This would ideally use market data or historical data
          const expectedRevenuePerAcre = await get().getExpectedRevenueForCrop(crop.cropType);
          const fieldSizeInAcres = field.sizeUnit === 'acres' ? field.size : field.size * 0.404686; // Convert hectares to acres
          const expectedRevenue = expectedRevenuePerAcre * fieldSizeInAcres;

          // Calculate ROI
          const roi = totalRevenue > 0 ? ((totalRevenue - totalCosts) / totalCosts) * 100 : -100;

          // Calculate revenue per acre
          const revenuePerAcre = totalRevenue / fieldSizeInAcres;

          // Detect inconsistencies
          const inconsistencies = [];

          // Check if harvest sale exists but crop is still active
          if (transactions.some(t => t.category === 'harvest_sale') && crop.status === 'active') {
            inconsistencies.push({
              type: 'lifecycle_mismatch',
              description: 'Harvest sale recorded but crop is still marked as active',
              severity: 'high'
            });
          }

          // Check if planting date is after any transaction date
          const plantedDate = new Date(crop.plantedDate);
          transactions.forEach(transaction => {
            const transactionDate = new Date(transaction.date);
            if (transactionDate < plantedDate && transaction.category !== 'seed_cost') {
              inconsistencies.push({
                type: 'date_mismatch',
                description: `Transaction dated ${transaction.date} is before planting date ${crop.plantedDate}`,
                severity: 'medium'
              });
            }
          });

          // Check if costs are unusually high compared to expected revenue
          if (totalCosts > expectedRevenue * 0.8) {
            inconsistencies.push({
              type: 'high_cost_ratio',
              description: 'Total costs are more than 80% of expected revenue',
              severity: 'medium'
            });
          }

          const financials: CropFinancials = {
            cropId,
            fieldId: crop.fieldId,
            totalCosts,
            totalRevenue,
            expectedRevenue,
            revenuePerAcre,
            roi,
            costBreakdown,
            inconsistencies
          };

          // Save to Firestore
          await setDoc(
            doc(firestore, `farms/${farmId}/cropFinancials/${cropId}`),
            financials
          );

          // Update local state
          set(state => ({
            cropFinancials: {
              ...state.cropFinancials,
              [cropId]: financials
            },
            isLoading: false
          }));

          return financials;
        } catch (error: any) {
          console.error('Calculate crop financials error:', error);
          set({
            error: error.message || 'Failed to calculate crop financials',
            isLoading: false
          });
          throw error;
        }
      },

      getExpectedRevenueForCrop: async (cropType: string) => {
        // This would ideally fetch from an API or database with current market prices
        // For now, we'll use a simple lookup table
        const revenueTable: Record<string, number> = {
          'Wheat': 600,
          'Corn': 800,
          'Soybeans': 700,
          'Rice': 900,
          'Cotton': 1200,
          'Potatoes': 3000,
          'Tomatoes': 5000,
          'Lettuce': 4000,
          'Strawberries': 10000,
          'Apples': 8000,
          'Grapes': 12000,
          'Default': 500
        };

        return revenueTable[cropType] || revenueTable['Default'];
      },


      // deleteYield: async (yieldId) => {
      //   set({ isLoading: true, error: null });

      //   try {
      //     const farmId = get().currentFarm?.id;

      //     if (!farmId) {
      //       throw new Error('No farm selected');
      //     }

      //     await deleteDoc(doc(firestore, `farms/${farmId}/activity_logs`, yieldId));

      //     set(state => ({
      //       yields: state.yields.filter(yieldItem => yieldItem.id !== yieldId),
      //       isLoading: false,
      //     }));
      //   } catch (error: any) {
      //     console.error('Delete yield error:', error);
      //     set({
      //       error: error.message || 'Failed to delete yield',
      //       isLoading: false
      //     });
      //   }
      // },
      // Add plant transaction
      addPlantTransaction: async (plantId: string, transactionData: any) => {
        try {
          const plantRef = doc(firestore, `farms/${get().currentFarm?.id}/plants`, plantId);
          const plantDoc = await getDoc(plantRef);

          if (!plantDoc.exists()) {
            throw new Error('Plant not found');
          }

          const transactionRef = collection(firestore, `farms/${get().currentFarm?.id}/plant_transactions`);
          const newTransactionRef = await addDoc(transactionRef, {
            ...transactionData,
            plantId: plantId,
            createdAt: new Date(),
            updatedAt: new Date(),
          });

          // Update the plant with a reference to this transaction
          await updateDoc(plantRef, {
            transactions: arrayUnion(newTransactionRef.id),
            updatedAt: new Date(),
          });

          return newTransactionRef.id;
        } catch (error) {
          console.error('Error adding plant transaction:', error);
          throw error;
        }
      },
      // Get plant transactions
      getPlantTransactions: async (plantId: string) => {
        try {
          const q = query(
            collection(firestore, `farms/${get().currentFarm?.id}/plant_transactions`),
            where('plantId', '==', plantId),
            // orderBy('date', 'desc')
          );

          const querySnapshot = await getDocs(q);
          const transactions = querySnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
          }));

          return transactions;
        } catch (error) {
          console.error('Error getting plant transactions:', error);
          throw error;
        }
      },
      // Add garden transaction
      addGardenTransaction: async (gardenId: string, transactionData: any) => {
        try {
          const gardenRef = doc(firestore, `farms/${get().currentFarm?.id}/gardens`, gardenId);
          const gardenDoc = await getDoc(gardenRef);

          if (!gardenDoc.exists()) {
            throw new Error('Garden not found');
          }

          const transactionRef = collection(firestore, `farms/${get().currentFarm?.id}/garden_transactions`);
          const newTransactionRef = await addDoc(transactionRef, {
            ...transactionData,
            gardenId: gardenId,
            createdAt: new Date(),
            updatedAt: new Date(),
          });

          // Update the garden with a reference to this transaction
          await updateDoc(gardenRef, {
            transactions: arrayUnion(newTransactionRef.id),

            updatedAt: new Date(),
          });

          return newTransactionRef.id;
        } catch (error) {
          console.error('Error adding garden transaction:', error);
          throw error;
        }
      },
      // Get garden transactions
      getGardenTransactions: async (gardenId: string) => {
        try {
          const q = query(
            collection(firestore, `farms/${get().currentFarm?.id}/garden_transactions`),
            where('gardenId', '==', gardenId),
            // orderBy('date', 'desc')
          );

          const querySnapshot = await getDocs(q);
          const transactions = querySnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
          }));

          return transactions;
        } catch (error) {
          console.error('Error getting garden transactions:', error);
          throw error;
        }
      },
      // Add Animal Cost
      addAnimalCost: async (animalId, data) => {
        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) throw new Error('No farm selected');

          const { user } = useAuthStore.getState();
          const now = new Date().toISOString();

          const costData: AnimalCost = {
            animal_id: animalId,
            farmId,
            createdAt: now,
            createdBy: user?.id,
            ...data
          };

          const costRef = await addDoc(
            collection(firestore, `farms/${farmId}/animal_costs`),
            costData
          );

          // Update local state
          set(state => ({
            animalCosts: [...state.animalCosts, { ...costData, id: costRef.id }]
          }));

          return costRef.id;
        } catch (error) {
          console.error('Error adding animal cost:', error);
          throw error;
        }
      },

      // Get Animal Costs
      getAnimalCosts: async (animalId) => {
        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) throw new Error('No farm selected');

          const q = query(
            collection(firestore, `farms/${farmId}/animal_costs`),
            where('animal_id', '==', animalId),
            orderBy('date', 'desc')
          );

          const querySnapshot = await getDocs(q);
          const costs = querySnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as AnimalCost[];

          // Update local state
          set(state => ({
            animalCosts: costs
          }));

          return costs;
        } catch (error) {
          console.error('Error getting animal costs:', error);
          throw error;
        }
      },

      // Add Animal Milk Record
      addAnimalMilkRecord: async (animalId, data) => {
        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) throw new Error('No farm selected');

          const { user } = useAuthStore.getState();
          const now = new Date().toISOString();

          const milkData: AnimalMilkRecord = {
            animal_id: animalId,
            farmId,
            createdAt: now,
            createdBy: user?.id,
            ...data
          };

          const milkRef = await addDoc(
            collection(firestore, `farms/${farmId}/animal_milk_records`),
            milkData
          );

          // Update local state
          set(state => ({
            animalMilkRecords: [...state.animalMilkRecords, { ...milkData, id: milkRef.id }]
          }));

          return milkRef.id;
        } catch (error) {
          console.error('Error adding milk record:', error);
          throw error;
        }
      },

      // Get Animal Milk Records
      getAnimalMilkRecords: async (animalId) => {
        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) throw new Error('No farm selected');

          const q = query(
            collection(firestore, `farms/${farmId}/animal_milk_records`),
            where('animal_id', '==', animalId),
            orderBy('date', 'desc')
          );

          const querySnapshot = await getDocs(q);
          const records = querySnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as AnimalMilkRecord[];

          // Update local state
          set(state => ({
            animalMilkRecords: records
          }));

          return records;
        } catch (error) {
          console.error('Error getting milk records:', error);
          throw error;
        }
      },

      // Add Animal Cleanliness Record
      addAnimalCleanlinessRecord: async (animalId, data) => {
        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) throw new Error('No farm selected');

          const { user } = useAuthStore.getState();
          const now = new Date().toISOString();

          const cleanlinessData: AnimalCleanliness = {
            animal_id: animalId,
            farmId,
            createdAt: now,
            createdBy: user?.id,
            ...data
          };

          const cleanlinessRef = await addDoc(
            collection(firestore, `farms/${farmId}/animal_cleanliness`),
            cleanlinessData
          );

          // Update local state
          set(state => ({
            animalCleanlinessRecords: [...state.animalCleanlinessRecords, { ...cleanlinessData, id: cleanlinessRef.id }]
          }));

          return cleanlinessRef.id;
        } catch (error) {
          console.error('Error adding cleanliness record:', error);
          throw error;
        }
      },

      // Get Animal Cleanliness Records
      getAnimalCleanlinessRecords: async (animalId) => {
        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) throw new Error('No farm selected');

          const q = query(
            collection(firestore, `farms/${farmId}/animal_cleanliness`),
            where('animal_id', '==', animalId),
            orderBy('date', 'desc')
          );

          const querySnapshot = await getDocs(q);
          const records = querySnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as AnimalCleanliness[];

          // Update local state
          set(state => ({
            animalCleanlinessRecords: records
          }));

          return records;
        } catch (error) {
          console.error('Error getting cleanliness records:', error);
          throw error;
        }
      },
      // Add finance methods implementation
      addFinanceRecord: async (data) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) {
            throw new Error('No farm selected');
          }

          const { user } = useAuthStore.getState();
          if (!user) {
            throw new Error('User not authenticated');
          }

          const record = {
            ...data,
            farmId,
            createdBy: user.id,
            createdAt: serverTimestamp()
          };

          const docRef = await addDoc(collection(firestore, 'finance_records'), record);

          // Update local state
          const newRecord = {
            ...record,
            id: docRef.id,
            createdAt: Timestamp.now() // Use client timestamp for immediate UI update
          };

          set(state => ({
            financeRecords: [newRecord, ...state.financeRecords],
            isLoading: false
          }));

          return docRef.id;
        } catch (error: any) {
          console.error('Add finance record error:', error);
          set({
            error: error.message || 'Failed to add finance record',
            isLoading: false
          });
          throw error;
        }
      },
      fetchFinanceRecords: async (filters = {}) => {
        set({ isLoading: true, error: null });

        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) {
            throw new Error('No farm selected');
          }

          // Build query
          let q = query(
            collection(firestore, 'finance_records'),
            where('farmId', '==', farmId),
            // orderBy('date', 'desc')
          );

          // Apply filters
          if (filters.entityType && filters.entityType !== 'all') {
            q = query(q, where('entityType', '==', filters.entityType));
          }

          if (filters.entityId && filters.entityId !== 'all') {
            q = query(q, where('entityId', '==', filters.entityId));
          }

          if (filters.transactionType && filters.transactionType !== 'all') {
            q = query(q, where('transactionType', '==', filters.transactionType));
          }

          if (filters.currency) {
            q = query(q, where('currency', '==', filters.currency));
          }

          if (filters.category) {
            q = query(q, where('category', '==', filters.category));
          }

          const querySnapshot = await getDocs(q);
          let records = querySnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as FinanceRecord[];

          // Apply date range filter in JS since Firestore doesn't support range queries on multiple fields
          if (filters.startDate) {
            records = records.filter(record => {
              const recordDate = record.date.toDate();
              return recordDate >= filters.startDate!;
            });
          }

          if (filters.endDate) {
            records = records.filter(record => {
              const recordDate = record.date.toDate();
              const endOfDay = new Date(filters.endDate!);
              endOfDay.setHours(23, 59, 59, 999);
              return recordDate <= endOfDay;
            });
          }

          // Calculate summary
          let totalIncome = 0;
          let totalExpense = 0;

          records.forEach(record => {
            if (record.transactionType === 'income') {
              totalIncome += record.amount;
            } else {
              totalExpense += record.amount;
            }
          });

          const netBalance = totalIncome - totalExpense;

          // Prepare monthly data for chart
          const monthlyData: Record<string, { income: number; expense: number }> = {};

          // Initialize all months
          const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          months.forEach(month => {
            monthlyData[month] = { income: 0, expense: 0 };
          });

          // Populate with actual data
          records.forEach(record => {
            const date = record.date.toDate();
            const month = months[date.getMonth()];

            if (record.transactionType === 'income') {
              monthlyData[month].income += record.amount;
            } else {
              monthlyData[month].expense += record.amount;
            }
          });

          // Convert to chart format
          const labels = Object.keys(monthlyData);
          const income = labels.map(month => monthlyData[month].income);
          const expense = labels.map(month => monthlyData[month].expense);

          // Update state
          set({
            financeRecords: records,
            financeSummary: {
              totalIncome,
              totalExpense,
              netBalance,
              currency: "PKR",
            },
            monthlyFinanceData: {
              labels,
              income,
              expense
            },
            isLoading: false
          });

          return records;
        } catch (error: any) {
          console.error('Fetch finance records error:', error);
          set({
            error: error.message || 'Failed to fetch finance records',
            isLoading: false
          });
          return [];
        }
      },

      getEntityName: async (entityType, entityId) => {
        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) {
            return entityId;
          }

          let collectionPath = '';

          switch (entityType) {
            case 'animal':
              collectionPath = `farms/${farmId}/animals`;
              break;
            case 'crop':
              // For crops, we need to find which field it belongs to
              const crops = get().crops;
              const crop = crops.find(c => c.id === entityId);
              if (crop) {
                return crop.name || entityId;
              }
              return entityId;
            case 'field':
              collectionPath = `farms/${farmId}/zones`;
              break;
            case 'garden':
              collectionPath = `farms/${farmId}/zones`;
              break;
            case 'plant':
              collectionPath = `farms/${farmId}/plants`;
              break;
            case 'equipment':
              collectionPath = `farms/${farmId}/eqiupments`;
              break;
            default:
              return entityId;
          }

          const docRef = doc(firestore, collectionPath, entityId);
          const docSnap = await getDoc(docRef);

          if (docSnap.exists()) {
            return docSnap.data().name || entityId;
          }

          return entityId;
        } catch (error) {
          console.error('Error getting entity name:', error);
          return entityId;
        }
      },

      exportFinanceRecordsToCSV: async () => {
        try {
          const records = get().financeRecords;
          if (records.length === 0) {
            throw new Error('No records to export');
          }

          // Create CSV content
          let csvContent = 'Entity Type,Entity ID,Transaction Type,Category,Amount,Currency,Date,Notes\n';

          for (const record of records) {
            const entityName = await get().getEntityName(record.entityType, record.entityId);
            const date = record.date.toDate().toISOString().split('T')[0];

            const row = [
              record.entityType,
              entityName,
              record.transactionType,
              record.category,
              record.amount,
              record.currency,
              date,
              `"${record.notes || ''}"`
            ].join(',');

            csvContent += row + '\n';
          }

          // Return CSV content for the UI to handle file creation and sharing
          return csvContent;
        } catch (error: any) {
          console.error('Error exporting to CSV:', error);
          throw error;
        }
      },
      saveAnimalChecklist: async (farmId: string, animalId: string, items: ChecklistItem[], userId: string) => {

        // console.log(animalId)
        try {
          const checklistRef1 = collection(firestore, `farms/${farmId}/animals-checklist`);
          const q = query(checklistRef1, where('animalId', '==', animalId));
          const snapshot = await getDocs(q);
          const today = new Date();
          const alreadyExists = snapshot.docs.some(doc => {
            const data = doc.data();
            return isSameDay(data.date, today);
          });

          if (alreadyExists) {
            return { status: -1, message: 'Checklist already submitted for today.' }
            // throw new Error();
          }
          const processedItems = await Promise.all(
            items.map(async (item) => {
              let imageUrl = '';
              if (item.image) {
                imageUrl = await uploadImageAsync(item.image, animalId);
              }

              return {
                id: item.id,
                title: item.value,
                completed: item.completed,
                image: imageUrl || null,
              };
            })
          );
          let checklistObj = {
            //  id: uuidv4(),
            userId,
            farmId,
            animalId,
            date: new Date().toISOString(),
            items: processedItems,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
          }
          // `farms/${farmId}/zones/${fieldId}/crops/${cropData.id}`
          const checklistRef = await addDoc(collection(firestore, `farms/${farmId}/animals-checklist`), checklistObj);
          return checklistRef.id;
          // 
          // alert('Success', 'Checklist saved successfully.');
        } catch (error) {
          console.error('Error saving checklist:', error);
          //   Alert.alert('Error', 'Failed to save checklist. Please try again.');
        }

        return

      },
      getAnimalChecklists: async (farmId: string, animalId: string) => {
        try {
          const checklistRef = collection(firestore, `farms/${farmId}/animals-checklist`);
          const q = query(checklistRef, where('animalId', '==', animalId));
          const snapshot = await getDocs(q);
          const checksData: Field[] = [];

          const checklists = await Promise.all(
            snapshot.docs.map(async (docSnap) => {
              const data = docSnap.data();
              let username = '';

              if (data.userId) {
                try {
                  const userDoc = await getDoc(doc(firestore, `users/${data.userId}`));
                  if (userDoc.exists()) {
                    const userData = userDoc.data();
                    username = userData.username || userData.name || '';
                  }
                } catch (err) {
                  console.warn(`Failed to fetch user for ID ${data.userId}:`, err);
                }
              }

              return {
                id: docSnap.id,
                ...data,
                username,
              };
            })
          );



          set({
            animalCheckList: checklists,
            isLoading: false
          })


          // console.log({checklists})

          return checklists;
        } catch (error) {
          console.error('Error fetching animal checklists:', error);
          throw error;
        }
        // return
      },

      saveEquipmentChecklist: async (farmId: string, equipmentId: string, userId: string, items: EquipmentChecklistItem[]) => {
        try {
          const checklistRef = collection(firestore, `farms/${farmId}/equipment-checklist`);
          const q = query(checklistRef, where('equipmentId', '==', equipmentId));
          const snapshot = await getDocs(q);

          const today = new Date();
          const alreadyExists = snapshot.docs.some(doc => {
            const data = doc.data();
            return isSameDay(data.date, today);
          });

          if (alreadyExists) {
            return { status: -1, message: 'Checklist already submitted for today.' }
            // throw new Error('Checklist already submitted for today.');
          }

          const processedItems = await Promise.all(
            items.map(async (item) => {
              let imageUrl = '';
              if (item.image) {
                imageUrl = await uploadImageAsync(item.image, equipmentId);
              }

              return {
                id: item.id,
                title: item.value,
                completed: item.completed,
                image: imageUrl || null,
              };
            })
          );

          const checklistObj = {
            farmId,
            equipmentId,
            userId, // 🔥 Save user ID here
            date: today,
            items: processedItems,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
          };

          const savedRef = await addDoc(checklistRef, checklistObj);
          return savedRef.id;
        } catch (error) {
          console.error('Error saving equipment checklist:', error);
          // throw ;
          return { status: -1, message: 'Error saving equipment checklist' }
        }
      },
      getEquipmentChecklists: async (farmId: string, equipmentId: string) => {
        try {
          const checklistRef = collection(firestore, `farms/${farmId}/machinery-checklist`);
          const q = query(checklistRef, where('equipmentId', '==', equipmentId));
          const snapshot = await getDocs(q);

          const checklists = await Promise.all(
            snapshot.docs.map(async (docSnap) => {
              const data = docSnap.data();
              let userName = 'Unknown';

              if (data.userId) {
                try {
                  const userRef = doc(firestore, `users/${data.userId}`);
                  const userSnap = await getDoc(userRef);
                  if (userSnap.exists()) {
                    const userData = userSnap.data();
                    userName = userData.fullName || userData.name || 'Unknown';
                  }
                } catch (err) {
                  console.warn(`Error fetching user info for ID ${data.userId}:`, err);
                }
              }

              return {
                id: docSnap.id,
                ...data,
                userName, // 👈 returned to show in UI
              };
            })
          );

          return checklists;
        } catch (error) {
          console.error('Error fetching equipment checklists:', error);
          throw error;
        }
      },
      getDailyReport: async (farmId: string, selectedDate: Date, title: string) => {
        // Format the date as 'yyyy-MM-dd'
        try {
          const dateToQuery = selectedDate instanceof Date ? selectedDate : new Date();
          const startOfDay = new Date(dateToQuery);
          startOfDay.setHours(0, 0, 0, 0);

          const endOfDay = new Date(dateToQuery);
          endOfDay.setHours(23, 59, 59, 999);

          // Fetch tasks
          const tasksRef = collection(firestore, 'farms', farmId, 'tasks');

          // Query for tasks created on the selected date
          const createdQuery = query(tasksRef,
            where('createdAt', '>=', startOfDay),
            where('createdAt', '<=', endOfDay)
          );

          // Query for tasks due on the selected date
          const dueQuery = query(tasksRef,
            where('dueDate', '>=', startOfDay),
            where('dueDate', '<=', endOfDay)
          );

          const [createdSnapshot, dueSnapshot] = await Promise.all([
            getDocs(createdQuery),
            getDocs(dueQuery)
          ]);

          // Get lookups for mapping IDs to names
          const { lookups } = useLookupStore.getState();
          const taskCategories = lookups.taskCategory || [];
          const taskPriorities = lookups.taskPriority || [];

          // Merge and deduplicate tasks

          const tasksMap = new Map();
          createdSnapshot.docs.forEach(doc => tasksMap.set(doc.id, { id: doc.id, ...doc.data() }));
          dueSnapshot.docs.forEach(doc => tasksMap.set(doc.id, { id: doc.id, ...doc.data() }));

          const allTasksRaw = Array.from(tasksMap.values());

          // Get unique user IDs from tasks to fetch their names efficiently
          const userIds = [...new Set(allTasksRaw.map(task => task.assignedTo).filter(Boolean))];
          const userPromises = userIds.map(userId => get().getUserById(userId as string));
          const users = (await Promise.all(userPromises)).filter(Boolean) as User[];
          const usersMap = new Map(users.map(user => [user.id, user]));

          const tasks = allTasksRaw.map(task => {
            const category = taskCategories.find(cat => cat.id === task.category);
            const priority = taskPriorities.find(prio => prio.id === task.priority);
            const assignedToUser = usersMap.get(task.assignedTo);

            return {
              ...task,
              category: category ? category.title : task.category,
              priority: priority ? priority.title : task.priority,
              // Replace assignedTo ID with user's name, fallback to email or ID to prevent 'undefined'
              assignedTo: assignedToUser ? (assignedToUser.name || assignedToUser.displayName || assignedToUser) : '--'
            };
          });
          // console.log({ taskCategories }, { taskPriorities }, { tasks })
          // Fetch attendance
          // const attendanceRef = collection(firestore, 'farms', farmId, 'attendance');
          // const attendanceQuery = query(attendanceRef, where('date', '==', dateStr));
          // const attendanceSnapshot = await getDocs(attendanceQuery);
          // const staffAttendance = attendanceSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

          // Fetch notes
          const notesRef = collection(firestore, 'farms', farmId, 'notes');
          const notesQuery = query(notesRef, where('createdAt', '>=', startOfDay), where('createdAt', '<=', endOfDay));
          const notesSnapshot = await getDocs(notesQuery);
          const notesAndObservations = notesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

          // Optional: Fetch weather (you may use an API or store daily weather in Firestore)
          // const weatherRef = collection(firestore, 'farms', farmId, 'weather');
          // const weatherQuery = query(weatherRef, where('date', '==', dateStr));
          // const weatherSnapshot = await getDocs(weatherQuery);
          // const weatherData = weatherSnapshot.empty ? null : weatherSnapshot.docs[0].data();

          // Task summary
          const taskSummary = {
            total: tasks.length,
            completed: tasks.filter(t => t.status === 'completed').length,
            inProgress: tasks.filter(t => t.status === 'in_progress').length,
            pending: tasks.filter(t => t.status === 'pending').length,
            categories: tasks.reduce((acc: Record<string, number>, task: any) => {
              // Since task.category is now the name, we can use it directly
              const categoryName = task.category || 'Uncategorized';
              acc[categoryName] = (acc[categoryName] || 0) + 1;
              return acc;
            }, {}),
          };

          // return {
          //   date: dateStr,
          //   farmId,
          //   taskSummary,
          //   tasks,
          //   staffAttendance,
          //   notesAndObservations,
          //   weatherData,
          // };

          const now = new Date();
          const dateStr = now.toISOString().split('T')[0];
          const user = useAuthStore.getState().user;
          const reportData = {
            type: 'daily',
            date: dateToQuery.toISOString().split('T')[0],
            createdAt: Timestamp.fromDate(now),
            createdBy: user?.name || user?.displayName || 'Unknown',
            farmId,
            taskSummary,
            notesAndObservations,
            tasks,
            title,
            // inventorySummary,
            // equipmentSummary,
            // recentActivities: recentActivitiesData,
            // inventoryItems: inventoryItems,
            // equipmentItems: equipmentItems,
          };


          const reportRef = doc(collection(firestore, 'reports'));
          await setDoc(reportRef, reportData);

          return reportRef.id;

        } catch (error) {
          console.error('Error generating daily report:', error);
          throw error;
        }
      },
      generateInventoryStatusReport: async (farmId: string, title: string) => {
        try {
          // Fetch inventory items
          const inventoryRef = collection(firestore, `farms/${farmId}/inventory`);
          const inventorySnap = await getDocs(inventoryRef);
          const inventoryItems = inventorySnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

          // Fetch equipment items
          const equipmentRef = collection(firestore, `farms/${farmId}/equipment`);
          const equipmentSnap = await getDocs(equipmentRef);
          const equipmentItems = equipmentSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));


          //// Recent Activities
          const recentActivities = collection(firestore, `farms/${farmId}/activities`)
          const recentActivitiesSnap = await getDocs(recentActivities)
          const recentActivitiesData = recentActivitiesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }))
          // const recentActivitiesCount = recentActivitiesData.length
          // Inventory summary
          const inventorySummary = {
            totalItems: inventoryItems.length,
            sufficientItems: inventoryItems.filter(item => item.status === 'sufficient').length,
            lowItems: inventoryItems.filter(item => item.status === 'low').length,
            depletedItems: inventoryItems.filter(item => item.status === 'depleted').length,
            categories: {
              seeds: inventoryItems.filter(item => item.category === 'seeds').length,
              fertilizer: inventoryItems.filter(item => item.category === 'fertilizer').length,
              pesticide: inventoryItems.filter(item => item.category === 'pesticide').length,
              equipment: inventoryItems.filter(item => item.category === 'equipment').length,
              tools: inventoryItems.filter(item => item.category === 'tools').length,
            },
            inventoryItems: inventoryItems,
            equipmentItems: equipmentItems,
            // recentActivities: recentActivitiesData
          };

          // Equipment summary
          const equipmentSummary = {
            totalItems: equipmentItems.length,
            operationalItems: equipmentItems.filter(item => item.status === 'operational').length,
            maintenanceItems: equipmentItems.filter(item => item.status === 'maintenance').length,
            repairItems: equipmentItems.filter(item => item.status === 'repair').length,
          };

          // Optionally: fetch recent activity log from a subcollection like `farms/{farmId}/inventoryActivities`
          // const recentActivities = [
          //   { id: '1', item: 'Fertilizer', action: 'added', quantity: 200, unit: 'kg', date: '2023-05-10', user: 'John Doe' },
          //   { id: '2', item: 'Pesticide', action: 'used', quantity: 5, unit: 'liters', date: '2023-05-12', user: 'Sarah Williams' },
          //   { id: '3', item: 'Wheat Seeds', action: 'added', quantity: 100, unit: 'kg', date: '2023-05-15', user: 'Mike Johnson' },
          // ];

          const now = new Date();
          const dateStr = now.toISOString().split('T')[0];
          const user = useAuthStore.getState().user;

          const reportData = {
            type: 'inventory',
            date: dateStr,
            createdAt: Timestamp.fromDate(now),
            createdBy: user?.name || user?.displayName || 'Unknown',
            farmId,
            inventorySummary,
            title,
            equipmentSummary,
            recentActivities: recentActivitiesData,
            inventoryItems: inventoryItems,
            equipmentItems: equipmentItems,
          };

          const reportRef = doc(collection(firestore, 'reports'));
          await setDoc(reportRef, reportData);

          return reportRef.id;

        } catch (error) {
          console.error('Error generating inventory report:', error);
          throw new Error('Failed to generate inventory status report.');
        }
      },
      getReportById: async (reportId: string) => {
        try {
          const reportRef = doc(firestore, 'reports', reportId);
          const reportSnap = await getDoc(reportRef);

          if (!reportSnap.exists()) {
            console.warn('Report not found for ID:', reportId);
            return null;
          }

          const reportData = reportSnap.data();

          return {
            id: reportSnap.id,
            ...reportData,
          };
        } catch (error) {
          console.error('Error fetching report by ID:', error);
          throw new Error('Failed to load report');
        }
      },

      // saveChecklist: async (farmId: string, data: Record<string, any>) => {
      //   try {
      //     const checkCollectionRef = collection(
      //       doc(firestore, "farms", farmId),
      //       "DailyPlantHealthCheck"
      //     );

      //     const docRef = await addDoc(checkCollectionRef, {
      //       ...data,
      //       createdAt: new Date().toISOString(), // optional: timestamp
      //     });

      //     return docRef.id
      //     // console.log("Checklist saved successfully");
      //   } catch (error) {
      //     console.error("Error saving checklist:", error);
      //     // throw error;
      //     return
      //   }
      // },
      saveChecklist: async (farmId: string, data: Record<string, any>) => {
        try {
          const { images, ...otherData } = data;

          // Step 1: Add the checklist entry without images
          const checkCollectionRef = collection(
            doc(firestore, "farms", farmId),
            "DailyPlantHealthCheck"
          );

          const docRef = await addDoc(checkCollectionRef, {
            ...otherData,
            createdAt: new Date().toISOString(),
            imageUrls: [],
          });

          // Step 2: Upload images and get URLs
          const imageUrls: string[] = [];

          if (images && images.length > 0) {
            for (let i = 0; i < images.length; i++) {
              const imageUri = images[i];
              const storagePath = `checklists/${farmId}/${docRef.id}/image_${i}.jpg`;

              const downloadUrl = await uploadImageAsync(imageUri, storagePath);
              imageUrls.push(downloadUrl);
            }

            // Step 3: Update checklist with image URLs
            await updateDoc(docRef, { imageUrls });
          }

          return docRef.id;
        } catch (error) {
          console.error("Error saving checklist:", error);
          return;
        }
      },

      getTodayChecklist: async (farmId: string, plantId: string) => {
        try {
          const today = new Date().toISOString().split("T")[0]; // e.g., "2025-06-30"

          const checkCollectionRef = collection(
            doc(firestore, "farms", farmId),
            "DailyPlantHealthCheck"
          );

          const q = query(
            checkCollectionRef,
            where("plantId", "==", plantId),
            // where("date", ">=", `${today}T00:00:00`),
            // where("date", "<=", `${today}T23:59:59`)
          );

          const snapshot = await getDocs(q);
          // console.log({ snapshot });

          if (snapshot.empty) {
            return []; // return empty array instead of null
          }

          const checklistArray = snapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
          }));

          // console.log({ checklistArray });
          return checklistArray;
        } catch (error) {
          console.error("Error fetching today's checklist:", error);
          return null;
        }
      },

      fetchChecklists: async (category?: string) => {
        try {
          let baseQuery = query(collection(firestore, 'checklists'));

          if (category) {
            baseQuery = query(
              collection(firestore, 'checklists'),
              where('category', '==', category),
              // orderBy('createdAt', 'desc')
            );
          }

          const snapshot = await getDocs(baseQuery);

          let results = snapshot.docs.map(doc => ({
            id: doc.id,
            ...(doc.data() as Omit<ChecklistRecord, 'id'>),
          }));

          // if (searchQuery) {
          //   const lowerQuery = searchQuery.toLowerCase();
          //   results = results.filter(c =>
          //     c.title.toLowerCase().includes(lowerQuery)
          //   );
          // }

          return results;
        } catch (error) {
          console.error('Error fetching checklists:', error);
          throw error;
        }
      },

      // Animal Health Checks
      fetchAnimalHealthChecks: async (animalId: string) => {
        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) throw new Error('No farm selected');

          const healthChecksRef = collection(firestore, `farms/${farmId}/animals/${animalId}/healthChecks`);
          const q = query(healthChecksRef, orderBy('date', 'desc'));
          const snapshot = await getDocs(q);

          const healthChecks: AnimalHealthCheck[] = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
            date: doc.data().date?.toDate?.()?.toISOString() || doc.data().date,
            createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
            updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
          })) as AnimalHealthCheck[];

          set(state => ({
            animalHealthChecks: state.animalHealthChecks.filter(hc => hc.animalId !== animalId).concat(healthChecks)
          }));

          return healthChecks;
        } catch (error) {
          console.error('Error fetching animal health checks:', error);
          throw error;
        }
      },

      addAnimalHealthCheck: async (animalId: string, healthCheckData: Omit<AnimalHealthCheck, 'id' | 'animalId' | 'createdAt' | 'updatedAt'>) => {
        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) throw new Error('No farm selected');

          const healthCheckWithMeta = {
            ...healthCheckData,
            animalId,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
          };

          const healthChecksRef = collection(firestore, `farms/${farmId}/animals/${animalId}/healthChecks`);
          const docRef = await addDoc(healthChecksRef, healthCheckWithMeta);

          const newHealthCheck: AnimalHealthCheck = {
            id: docRef.id,
            ...healthCheckData,
            animalId,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };

          set(state => ({
            animalHealthChecks: [...state.animalHealthChecks, newHealthCheck]
          }));

          return docRef.id;
        } catch (error) {
          console.error('Error adding animal health check:', error);
          throw error;
        }
      },

      // Animal Pregnancies
      fetchAnimalPregnancies: async (animalId: string) => {
        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) throw new Error('No farm selected');

          const pregnanciesRef = collection(firestore, `farms/${farmId}/animals/${animalId}/pregnancies`);
          const q = query(pregnanciesRef, orderBy('matingDate', 'desc'));
          const snapshot = await getDocs(q);

          const pregnancies: AnimalPregnancy[] = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
            matingDate: doc.data().matingDate?.toDate?.()?.toISOString() || doc.data().matingDate,
            expectedDueDate: doc.data().expectedDueDate?.toDate?.()?.toISOString() || doc.data().expectedDueDate,
            actualDueDate: doc.data().actualDueDate?.toDate?.()?.toISOString() || doc.data().actualDueDate,
            createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
            updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
          })) as AnimalPregnancy[];

          set(state => ({
            animalPregnancies: state.animalPregnancies.filter(p => p.animalId !== animalId).concat(pregnancies)
          }));

          return pregnancies;
        } catch (error) {
          console.error('Error fetching animal pregnancies:', error);
          throw error;
        }
      },

      addAnimalPregnancy: async (animalId: string, pregnancyData: Omit<AnimalPregnancy, 'id' | 'animalId' | 'createdAt' | 'updatedAt'>) => {
        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) throw new Error('No farm selected');

          const pregnancyWithMeta = {
            ...pregnancyData,
            animalId,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
          };

          const pregnanciesRef = collection(firestore, `farms/${farmId}/animals/${animalId}/pregnancies`);
          const docRef = await addDoc(pregnanciesRef, pregnancyWithMeta);

          const newPregnancy: AnimalPregnancy = {
            id: docRef.id,
            ...pregnancyData,
            animalId,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };

          set(state => ({
            animalPregnancies: [...state.animalPregnancies, newPregnancy]
          }));

          return docRef.id;
        } catch (error) {
          console.error('Error adding animal pregnancy:', error);
          throw error;
        }
      },

      // Animal Records
      fetchAnimalRecords: async (animalId: string) => {
        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) throw new Error('No farm selected');

          const recordsRef = collection(firestore, `farms/${farmId}/animals/${animalId}/records`);
          const q = query(recordsRef, orderBy('date', 'desc'));
          const snapshot = await getDocs(q);

          const records: AnimalRecord[] = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
            date: doc.data().date?.toDate?.()?.toISOString() || doc.data().date,
            createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
            updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
          })) as AnimalRecord[];

          set(state => ({
            animalRecords: state.animalRecords.filter(r => r.animalId !== animalId).concat(records)
          }));

          return records;
        } catch (error) {
          console.error('Error fetching animal records:', error);
          throw error;
        }
      },

      addAnimalRecord: async (animalId: string, recordData: Omit<AnimalRecord, 'id' | 'animalId' | 'createdAt' | 'updatedAt'>) => {
        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) throw new Error('No farm selected');

          const recordWithMeta = {
            ...recordData,
            animalId,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
          };

          const recordsRef = collection(firestore, `farms/${farmId}/animals/${animalId}/records`);
          const docRef = await addDoc(recordsRef, recordWithMeta);

          const newRecord: AnimalRecord = {
            id: docRef.id,
            ...recordData,
            animalId,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };

          set(state => ({
            animalRecords: [...state.animalRecords, newRecord]
          }));

          return docRef.id;
        } catch (error) {
          console.error('Error adding animal record:', error);
          throw error;
        }
      },

      // Machinery Management
      fetchMachinery: async () => {
        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) throw new Error('No farm selected');

          const machineryRef = collection(firestore, `farms/${farmId}/machinery`);
          const q = query(machineryRef, orderBy('name', 'asc'));
          const snapshot = await getDocs(q);

          const machinery: Machinery[] = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
            purchaseDate: doc.data().purchaseDate?.toDate?.()?.toISOString() || doc.data().purchaseDate,
            lastServiceDate: doc.data().lastServiceDate?.toDate?.()?.toISOString() || doc.data().lastServiceDate,
            nextServiceDate: doc.data().nextServiceDate?.toDate?.()?.toISOString() || doc.data().nextServiceDate,
            lastUsedDate: doc.data().lastUsedDate?.toDate?.()?.toISOString() || doc.data().lastUsedDate,
            warrantyExpiry: doc.data().warrantyExpiry?.toDate?.()?.toISOString() || doc.data().warrantyExpiry,
            insuranceExpiry: doc.data().insuranceExpiry?.toDate?.()?.toISOString() || doc.data().insuranceExpiry,
            createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
            updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
          })) as Machinery[];

          set({ machinery });
          return machinery;
        } catch (error) {
          console.error('Error fetching machinery:', error);
          throw error;
        }
      },

      addMachinery: async (machineryData: Omit<Machinery, 'id' | 'createdAt' | 'updatedAt'>) => {
        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) throw new Error('No farm selected');

          const machineryWithMeta = {
            ...machineryData,
            farmId,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
          };

          const machineryRef = collection(firestore, `farms/${farmId}/machinery`);
          const docRef = await addDoc(machineryRef, machineryWithMeta);

          const newMachinery: Machinery = {
            id: docRef.id,
            ...machineryData,
            farmId,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };

          set(state => ({
            machinery: [...state.machinery, newMachinery]
          }));

          return docRef.id;
        } catch (error) {
          console.error('Error adding machinery:', error);
          throw error;
        }
      },

      updateMachinery: async (machineryId: string, updates: Partial<Machinery>) => {
        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) throw new Error('No farm selected');

          const machineryRef = doc(firestore, `farms/${farmId}/machinery`, machineryId);
          const updateData = {
            ...updates,
            updatedAt: serverTimestamp(),
          };

          await updateDoc(machineryRef, updateData);

          set(state => ({
            machinery: state.machinery.map(m =>
              m.id === machineryId
                ? { ...m, ...updates, updatedAt: new Date().toISOString() }
                : m
            )
          }));
        } catch (error) {
          console.error('Error updating machinery:', error);
          throw error;
        }
      },

      deleteMachinery: async (machineryId: string) => {
        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) throw new Error('No farm selected');

          await deleteDoc(doc(firestore, `farms/${farmId}/machinery`, machineryId));

          set(state => ({
            machinery: state.machinery.filter(m => m.id !== machineryId)
          }));
        } catch (error) {
          console.error('Error deleting machinery:', error);
          throw error;
        }
      },

      // Maintenance Records
      fetchMaintenanceRecords: async (machineryId: string) => {
        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) throw new Error('No farm selected');

          const recordsRef = collection(firestore, `farms/${farmId}/machinery/${machineryId}/maintenance`);
          const q = query(recordsRef, orderBy('date', 'desc'));
          const snapshot = await getDocs(q);

          const records: MaintenanceRecord[] = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
            date: doc.data().date?.toDate?.()?.toISOString() || doc.data().date,
            nextServiceDue: doc.data().nextServiceDue?.toDate?.()?.toISOString() || doc.data().nextServiceDue,
            createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
          })) as MaintenanceRecord[];

          set(state => ({
            maintenanceRecords: state.maintenanceRecords.filter(r => r.machineryId !== machineryId).concat(records)
          }));

          return records;
        } catch (error) {
          console.error('Error fetching maintenance records:', error);
          throw error;
        }
      },

      addMaintenanceRecord: async (machineryId: string, recordData: Omit<MaintenanceRecord, 'id' | 'machineryId' | 'createdAt'>) => {
        try {
          const farmId = get().currentFarm?.id;
          if (!farmId) throw new Error('No farm selected');

          const recordWithMeta = {
            ...recordData,
            machineryId,
            createdAt: serverTimestamp(),
          };

          const recordsRef = collection(firestore, `farms/${farmId}/machinery/${machineryId}/maintenance`);
          const docRef = await addDoc(recordsRef, recordWithMeta);

          const newRecord: MaintenanceRecord = {
            id: docRef.id,
            ...recordData,
            machineryId,
            createdAt: new Date().toISOString(),
          };

          set(state => ({
            maintenanceRecords: [...state.maintenanceRecords, newRecord]
          }));

          // Update machinery's last service date if this is a service record
          if (recordData.type === 'routine' || recordData.type === 'repair') {
            await get().updateMachinery(machineryId, {
              lastServiceDate: recordData.date,
              nextServiceDate: recordData.nextServiceDue,
            });
          }

          return docRef.id;
        } catch (error) {
          console.error('Error adding maintenance record:', error);
          throw error;
        }
      },
    }),
    {
      name: 'kissan-dost-farm',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        currentFarm: state.currentFarm,
      }),
    }
  )
);


function isSameDay(timestamp: any, date: Date): boolean {
  const tsDate = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
  return (
    tsDate.getFullYear() === date.getFullYear() &&
    tsDate.getMonth() === date.getMonth() &&
    tsDate.getDate() === date.getDate()
  );
}

function getLookupsByCategory(arg0: string) {
  throw new Error('Function not implemented.');
}

export const getLookupTitle = async (lookupId: string): Promise<string> => {
  if (!lookupId) return '';

  const ref = doc(firestore, 'lookups', lookupId);
  const snap = await getDoc(ref);

  if (!snap.exists()) {
    console.warn(`Lookup "${lookupId}" not found, falling back to ID`);
    return lookupId;
  }

  const data = snap.data();
  return data.title ?? lookupId;
};


export const getLookupTitleLocal = (lookupId: string): string => {
  if (!lookupId) return '';

  const lookups = useLookupStore.getState().lookupsList || [];
  const match = lookups.find(item => item.id === lookupId);

  return match?.title || lookupId;
};
