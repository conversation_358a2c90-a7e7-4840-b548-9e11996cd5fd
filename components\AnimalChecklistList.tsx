import React from 'react';
import { View, Text, FlatList, StyleSheet, Alert, TouchableOpacity } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons'; // Or react-native-vector-icons
import { normalizeDate } from '@/utils/dateUtils';
import { useFarmStore } from '@/store/farm-store';
import { useTranslation } from '@/i18n/useTranslation';
import { capitalizeFirstLetter } from '@/utils/util';
// import moment from 'moment';

const AnimalChecklistList = ({ checklists }: { checklists: any[] }) => {
  const {
    animals,
    fields,
    getAnimal,
    deleteAnimal,
    updateAnimal,
    currentFarm,
    animalCheckList,
    markAnimalInactive,
    saveAnimalChecklist,
    getAnimalChecklists
  } = useFarmStore();
  const { t, isRTL } = useTranslation()
  const handleShowMore = (items) => {
    Alert.alert('Checklist Items', items.map(i => `${i.title}: ${i.completed ? '✓' : '✗'}`).join('\n'));
  };
  const renderItem = ({ item }) => (
    <View style={styles.card} key={item?.Id}>
      <View style={[styles.headerRow,isRTL && {flexDirection:'row-reverse'}]}>
        <MaterialIcons name="person" size={20} color="#4CAF50" />
        <Text style={[styles.animalIdText,isRTL && {textAlign:'right',marginRight:8}]}>{capitalizeFirstLetter(item?.username)}</Text>
        <MaterialIcons name="event" size={16} color="#888" style={{ marginLeft: 8 }} />
        <Text style={styles.dateText}>{normalizeDate(item.date)}</Text>
      </View>

      <View style={styles.iconContainer}>
        {item.items.map((check, index) => (
          <View key={check.value} style={[styles.checkItem,isRTL && {flexDirection:'row-reverse'}]}>
            <MaterialIcons
              name={check.completed ? 'check-circle' : 'cancel'}
              size={20}
              color={check.completed ? '#4CAF50' : '#F44336'}
            />

            <Text style={[styles.checkText,isRTL && {textAlign:'right',marginRight:8}]}>{t(`entity.animal.checklistArray.${check.title}`)}</Text>
          </View>
        ))}
      </View>
      

    </View>
  );

  return (
    <FlatList
      data={checklists}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      contentContainerStyle={styles.listContent}
    />
  );
};

const styles = StyleSheet.create({
  listContent: {
    padding: 10,
  },
  iconContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
    rowGap: 4, // Optional: adds space between rows
    columnGap: 12,
  },
  checkItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flexBasis: '45%', // Adjust to control how many per row
    marginBottom: 4,
  },
  checkText: {
    fontSize: 12,
    color: '#333',
    marginLeft: 4,
    flexShrink: 1, // Ensure long text wraps
  },
  card: {
    borderRadius: 10,
    backgroundColor: '#fff',
    padding: 12,
    marginBottom: 10,
    elevation: 2,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  animalIdText: {
    marginLeft: 6,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  dateText: {
    marginLeft: 4,
    fontSize: 12,
    color: '#666',
  },
  // checkItem: {
  //   alignItems: 'center',
  //   marginRight: 12,
  //   width: 70,
  // },
  // checkText: {
  //   fontSize: 10,
  //   textAlign: 'center',
  //   color: '#444',
  //   marginTop: 2,
  // },
  moreText: {
    fontSize: 12,
    color: '#0066cc',
    textAlign: 'center',
    marginTop: 6,
  },
  iconRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'nowrap',
  },

  iconItem: {
    alignItems: 'center',
    marginRight: 12,
    maxWidth: 60,
  },
  iconLabel: {
    fontSize: 10,
    textAlign: 'center',
    color: '#444',
    marginTop: 2,
  },
  // moreText: {
  //   fontSize: 12,
  //   color: '#888',
  //   marginLeft: 4,
  // },
});

export default AnimalChecklistList;
