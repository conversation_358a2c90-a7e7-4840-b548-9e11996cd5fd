import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';
import { useFarmStore } from '@/store/farm-store';
import { Machinery } from '@/types';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';

interface MachineryListViewProps {
  onItemPress?: (machinery: Machinery) => void;
  showAddButton?: boolean;
}

export const MachineryListView: React.FC<MachineryListViewProps> = ({
  onItemPress,
  showAddButton = true,
}) => {
  const { t, isRTL } = useTranslation();
  const { fetchMachinery, machinery, currentFarm } = useFarmStore();
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (currentFarm?.id) {
      loadMachinery();
    }
  }, [currentFarm?.id]);

  const loadMachinery = async () => {
    try {
      setLoading(true);
      await fetchMachinery();
    } catch (error) {
      console.error('Error loading machinery:', error);
      Alert.alert(t('common.error'), t('machinery.loadError'));
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadMachinery();
    setRefreshing(false);
  };

  const getMachineryIcon = (type: string) => {
    switch (type) {
      case 'tractor':
        return 'car-sport';
      case 'harvester':
        return 'leaf';
      case 'planter':
        return 'flower';
      case 'cultivator':
        return 'grid';
      case 'sprayer':
        return 'water';
      case 'mower':
        return 'cut';
      default:
        return 'construct';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'operational':
        return colors.success;
      case 'maintenance':
        return colors.warning;
      case 'repair':
        return colors.danger;
      case 'inactive':
        return colors.gray[500];
      default:
        return colors.gray[500];
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'operational':
        return 'checkmark-circle';
      case 'maintenance':
        return 'build';
      case 'repair':
        return 'warning';
      case 'inactive':
        return 'pause-circle';
      default:
        return 'help-circle';
    }
  };

  const handleItemPress = (item: Machinery) => {
    if (onItemPress) {
      onItemPress(item);
    } else {
      router.push(`/machinery/${item.id}`);
    }
  };

  const renderMachineryItem = ({ item }: { item: Machinery }) => (
    <TouchableOpacity
      style={[styles.machineryCard, isRTL && styles.machineryCardRTL]}
      onPress={() => handleItemPress(item)}
      activeOpacity={0.7}
    >
      <View style={[styles.machineryHeader, isRTL && styles.machineryHeaderRTL]}>
        <View style={[styles.machineryIconContainer, isRTL && styles.machineryIconContainerRTL]}>
          <View style={[styles.iconBackground, { backgroundColor: colors.primary + '20' }]}>
            <Ionicons
              name={getMachineryIcon(item.type) as any}
              size={24}
              color={colors.primary}
            />
          </View>
          <View style={styles.machineryInfo}>
            <Text style={[styles.machineryName, isRTL && styles.textRTL]}>
              {item.name}
            </Text>
            <Text style={[styles.machineryModel, isRTL && styles.textRTL]}>
              {item.manufacturer} {item.model}
            </Text>
          </View>
        </View>
        
        <View style={[styles.statusContainer, isRTL && styles.statusContainerRTL]}>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) + '20' }]}>
            <Ionicons
              name={getStatusIcon(item.status) as any}
              size={16}
              color={getStatusColor(item.status)}
            />
            <Text style={[styles.statusText, { color: getStatusColor(item.status) }, isRTL && styles.textRTL]}>
              {t(`machinery.status.${item.status}`)}
            </Text>
          </View>
        </View>
      </View>

      <View style={[styles.machineryDetails, isRTL && styles.machineryDetailsRTL]}>
        {item.horsepower && (
          <View style={[styles.detailItem, isRTL && styles.detailItemRTL]}>
            <Ionicons name="flash" size={14} color={colors.gray[600]} />
            <Text style={[styles.detailText, isRTL && styles.textRTL]}>
              {item.horsepower} HP
            </Text>
          </View>
        )}

        {item.assignedField && (
          <View style={[styles.detailItem, isRTL && styles.detailItemRTL]}>
            <Ionicons name="location" size={14} color={colors.gray[600]} />
            <Text style={[styles.detailText, isRTL && styles.textRTL]}>
              {t('machinery.assignedField')}: {item.assignedField}
            </Text>
          </View>
        )}

        {item.engineHours && (
          <View style={[styles.detailItem, isRTL && styles.detailItemRTL]}>
            <Ionicons name="time" size={14} color={colors.gray[600]} />
            <Text style={[styles.detailText, isRTL && styles.textRTL]}>
              {item.engineHours}h / {item.maxEngineHours || '∞'}h
            </Text>
          </View>
        )}

        {item.nextServiceDate && (
          <View style={[styles.detailItem, isRTL && styles.detailItemRTL]}>
            <Ionicons name="calendar" size={14} color={colors.warning} />
            <Text style={[styles.detailText, { color: colors.warning }, isRTL && styles.textRTL]}>
              {t('machinery.nextService')}: {new Date(item.nextServiceDate).toLocaleDateString()}
            </Text>
          </View>
        )}
      </View>

      {item.notes && (
        <Text style={[styles.machineryNotes, isRTL && styles.textRTL]} numberOfLines={2}>
          {item.notes}
        </Text>
      )}

      <View style={[styles.machineryFooter, isRTL && styles.machineryFooterRTL]}>
        <Text style={[styles.machineryType, isRTL && styles.textRTL]}>
          {t(`machinery.type.${item.type}`)}
        </Text>
        <Ionicons 
          name={isRTL ? "chevron-back" : "chevron-forward"} 
          size={16} 
          color={colors.gray[400]} 
        />
      </View>
    </TouchableOpacity>
  );

  if (loading && machinery.length === 0) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, isRTL && styles.textRTL]}>
          {t('machinery.loading')}
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={[styles.header, isRTL && styles.headerRTL]}>
        <Text style={[styles.sectionTitle, isRTL && styles.textRTL]}>
          {t('machinery.title')}
        </Text>
        {showAddButton && (
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => router.push('/machinery/create')}
          >
            <Ionicons name="add" size={20} color={colors.primary} />
            <Text style={[styles.addButtonText, isRTL && styles.textRTL]}>
              {t('machinery.add')}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {machinery.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="construct" size={48} color={colors.gray[400]} />
          <Text style={[styles.emptyText, isRTL && styles.textRTL]}>
            {t('machinery.noMachinery')}
          </Text>
          {showAddButton && (
            <TouchableOpacity
              style={styles.emptyAddButton}
              onPress={() => router.push('/machinery/create')}
            >
              <Text style={styles.emptyAddButtonText}>
                {t('machinery.addFirst')}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      ) : (
        <FlatList
          data={machinery}
          renderItem={renderMachineryItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[colors.primary]}
            />
          }
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  headerRTL: {
    flexDirection: 'row-reverse',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.gray[800],
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: colors.primary + '20',
    gap: 6,
  },
  addButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.primary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.gray[600],
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  machineryCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  machineryCardRTL: {
    alignItems: 'flex-end',
  },
  machineryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  machineryHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  machineryIconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  machineryIconContainerRTL: {
    flexDirection: 'row-reverse',
  },
  iconBackground: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  machineryInfo: {
    flex: 1,
  },
  machineryName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 2,
  },
  machineryModel: {
    fontSize: 14,
    color: colors.gray[600],
  },
  statusContainer: {
    alignItems: 'flex-end',
  },
  statusContainerRTL: {
    alignItems: 'flex-start',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  machineryDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
    gap: 12,
  },
  machineryDetailsRTL: {
    flexDirection: 'row-reverse',
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  detailItemRTL: {
    flexDirection: 'row-reverse',
  },
  detailText: {
    fontSize: 12,
    color: colors.gray[600],
  },
  machineryNotes: {
    fontSize: 12,
    color: colors.gray[600],
    fontStyle: 'italic',
    marginBottom: 8,
    lineHeight: 16,
  },
  machineryFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  machineryFooterRTL: {
    flexDirection: 'row-reverse',
  },
  machineryType: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  emptyAddButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyAddButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  textRTL: {
    textAlign: 'right',
  },
});
