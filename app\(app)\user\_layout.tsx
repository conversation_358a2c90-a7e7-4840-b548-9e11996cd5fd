import React from 'react';
import { Tabs } from 'expo-router';
import { colors } from '@/constants/colors';
import { User, UserPlus } from 'lucide-react-native';
import { useAuthStore } from '@/store/auth-store';

export default function UserLayout() {
  const { user } = useAuthStore();
  const isOwnerOrManager = user?.role === 'owner' || user?.role === 'admin';

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.gray[500],
        tabBarStyle: {
          height: 60,
          paddingBottom: 8,
          paddingTop: 8,
        },
        tabBarLabelStyle: {
          fontSize: 12,
        },
        headerShown: true,
      }}
    >
      <Tabs.Screen
        name="list"
        options={{
          title: 'Users',
          tabBarIcon: ({ color }) => <User size={24} color={color} />,
        }}
      />
      
      {/* Only show invite tab for owners and managers */}
      {isOwnerOrManager && (
        <Tabs.Screen
          name="invite"
          options={{
            title: 'Invite',
            tabBarIcon: ({ color }) => <UserPlus size={24} color={color} />,
          }}
        />
      )}
    </Tabs>
  );
}