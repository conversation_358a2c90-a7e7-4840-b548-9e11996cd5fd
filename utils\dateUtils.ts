import { Timestamp } from 'firebase/firestore'; // Only if using Firestore

export function normalizeDate(
  dateInput: any,
  format: 'DD-MM-YYYY' | 'YYYY-MM-DD' = 'DD-MM-YYYY'
): string {
  let date: Date | null = null;

  try {
    if (dateInput instanceof Timestamp) {
      date = dateInput.toDate();
    } else if (Array.isArray(dateInput) && dateInput[0] instanceof Timestamp) {
      date = dateInput[0].toDate();
    } else if (typeof dateInput === 'string' && !isNaN(Date.parse(dateInput))) {
      date = new Date(dateInput);
    } else if (dateInput instanceof Date) {
      date = dateInput;
    }

    if (!date) return '';
  } catch (err) {
    console.error('Error parsing date:', err);
    return '';
  }

  // Format the date
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear().toString();

  switch (format) {
    case 'YYYY-MM-DD':
      return `${year}-${month}-${day}`;
    case 'DD-MM-YYYY':
    default:
      return `${day}-${month}-${year}`;
  }
}

export function differenceInDays(dueDate:any, today:any) {
  const due = new Date(dueDate);
  const current = new Date(today);

  // Reset time part to midnight to avoid partial day issues
  due.setHours(0, 0, 0, 0);
  current.setHours(0, 0, 0, 0);

  const diffTime = due.getTime() - current.getTime();
  const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));

  return diffDays;
}
