import React from 'react';
import { View, ViewStyle, StyleProp } from 'react-native';
import { I18nManager } from 'react-native';

interface RTLViewProps {
  children: React.ReactNode;
  style?: StyleProp<ViewStyle>;
  [key: string]: any;
}

const RTLView: React.FC<RTLViewProps> = ({ children, style, ...props }) => {
  // Apply row-reverse only if RTL is enabled
  const rtlStyle = I18nManager.isRTL 
    ? { flexDirection: 'row-reverse' as const } 
    : { flexDirection: 'row' as const };
  
  return (
    <View style={[rtlStyle, style]} {...props}>
      {children}
    </View>
  );
};

export default RTLView;
