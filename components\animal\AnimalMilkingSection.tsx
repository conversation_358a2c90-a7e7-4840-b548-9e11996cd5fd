import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';
import { useFarmStore } from '@/store/farm-store';
import { AnimalMilkRecord } from '@/types';
import { Ionicons } from '@expo/vector-icons';
// import { format } from 'date-fns';
import { router } from 'expo-router';
import { normalizeDate } from '@/utils/dateUtils';

interface AnimalMilkingSectionProps {
  animalId: string;
  isEditable?: boolean;
}

export const AnimalMilkingSection: React.FC<AnimalMilkingSectionProps> = ({
  animalId,
  isEditable = true,
}) => {
  const { t, isRTL } = useTranslation();
  const { getAnimalMilkRecords, animalMilkRecords } = useFarmStore();
  const [loading, setLoading] = useState(false);
  const [milkRecords, setMilkRecords] = useState<AnimalMilkRecord[]>([]);

  useEffect(() => {
    loadMilkRecords();
  }, [animalId]);

  const loadMilkRecords = async () => {
    try {
      setLoading(true);
      const records = await getAnimalMilkRecords(animalId);
      setMilkRecords(records);
    } catch (error) {
      console.error('Error loading milk records:', error);
      Alert.alert(t('common.error'), t('animal.milkRecords.loadError'));
    } finally {
      setLoading(false);
    }
  };

  const navigateToAddMilkRecord = () => {
    router.push(`/animal/milk/add?id=${animalId}`);
  };

  const navigateToMilkRecordDetail = () => {
    router.push(`/animal/milk-record/${animalId}`);
  };

  const renderMilkRecordItem = ({ item }: { item: AnimalMilkRecord }) => {
    const totalMilk = item.morning_milk + item.evening_milk;
    
    return (
      <TouchableOpacity 
        style={[styles.milkRecordCard, isRTL && styles.milkRecordCardRTL]}
        onPress={navigateToMilkRecordDetail}
        activeOpacity={0.7}
      >
        <View style={[styles.milkRecordHeader, isRTL && styles.milkRecordHeaderRTL]}>
          <View style={[styles.milkRecordIconContainer, isRTL && styles.milkRecordIconContainerRTL]}>
            <Ionicons
              name="water"
              size={20}
              color={colors.primary}
            />
            <Text style={[styles.milkRecordDate, isRTL && styles.textRTL]}>
              {normalizeDate(new Date(item.date))}
            </Text>
          </View>
          <View style={[styles.totalMilkContainer, isRTL && styles.totalMilkContainerRTL]}>
            <Text style={[styles.totalMilkText, isRTL && styles.textRTL]}>
              {totalMilk.toFixed(1)}L
            </Text>
          </View>
        </View>

        <View style={[styles.milkRecordDetails, isRTL && styles.milkRecordDetailsRTL]}>
          <View style={[styles.milkDetailRow, isRTL && styles.milkDetailRowRTL]}>
            <View style={[styles.milkDetailItem, isRTL && styles.milkDetailItemRTL]}>
              <Ionicons name="sunny" size={16} color={colors.warning} />
              <Text style={[styles.milkDetailLabel, isRTL && styles.textRTL]}>
                {t('animal.milkRecords.morning')}:
              </Text>
              <Text style={[styles.milkDetailValue, isRTL && styles.textRTL]}>
                {item.morning_milk.toFixed(1)}L
              </Text>
            </View>
            
            <View style={[styles.milkDetailItem, isRTL && styles.milkDetailItemRTL]}>
              <Ionicons name="moon" size={16} color={colors.secondary} />
              <Text style={[styles.milkDetailLabel, isRTL && styles.textRTL]}>
                {t('animal.milkRecords.evening')}:
              </Text>
              <Text style={[styles.milkDetailValue, isRTL && styles.textRTL]}>
                {item.evening_milk.toFixed(1)}L
              </Text>
            </View>
          </View>
        </View>

        {item.notes && (
          <Text style={[styles.milkRecordNotes, isRTL && styles.textRTL]}>
            {item.notes}
          </Text>
        )}

        <View style={[styles.milkRecordFooter, isRTL && styles.milkRecordFooterRTL]}>
          <Text style={[styles.milkRecordTime, isRTL && styles.textRTL]}>
            {normalizeDate(new Date(item.createdAt || item.date))}
          </Text>
          <Ionicons 
            name={isRTL ? "chevron-back" : "chevron-forward"} 
            size={16} 
            color={colors.gray[400]} 
          />
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, isRTL && styles.textRTL]}>
          {t('animal.milkRecords.loading')}
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={[styles.header, isRTL && styles.headerRTL]}>
        <Text style={[styles.sectionTitle, isRTL && styles.textRTL]}>
          {t('animal.milkRecords.title')}
        </Text>
        <View style={[styles.headerActions, isRTL && styles.headerActionsRTL]}>
          {milkRecords.length > 0 && (
            <TouchableOpacity
              style={styles.viewAllButton}
              onPress={navigateToMilkRecordDetail}
            >
              <Text style={[styles.viewAllText, isRTL && styles.textRTL]}>
                {t('common.seeAll')}
              </Text>
            </TouchableOpacity>
          )}
          {isEditable && (
            <TouchableOpacity
              style={styles.addButton}
              onPress={navigateToAddMilkRecord}
            >
              <Ionicons name="add" size={20} color={colors.primary} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {milkRecords.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="water" size={48} color={colors.gray[400]} />
          <Text style={[styles.emptyText, isRTL && styles.textRTL]}>
            {t('animal.milkRecords.noRecords')}
          </Text>
          {isEditable && (
            <TouchableOpacity
              style={styles.emptyAddButton}
              onPress={navigateToAddMilkRecord}
            >
              <Text style={styles.emptyAddButtonText}>
                {t('animal.milkRecords.addFirst')}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      ) : (
        <FlatList
          data={milkRecords.slice(0, 3)} // Show only recent 3 records
          renderItem={renderMilkRecordItem}
          keyExtractor={(item) => item.id || item.date}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerRTL: {
    flexDirection: 'row-reverse',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.primary,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerActionsRTL: {
    flexDirection: 'row-reverse',
  },
  viewAllButton: {
    marginRight: 12,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: colors.gray[100],
  },
  viewAllText: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '500',
  },
  addButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: colors.primary + '20',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.gray[600],
  },
  listContainer: {
    paddingBottom: 16,
  },
  milkRecordCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  milkRecordCardRTL: {
    alignItems: 'flex-end',
  },
  milkRecordHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  milkRecordHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  milkRecordIconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  milkRecordIconContainerRTL: {
    flexDirection: 'row-reverse',
  },
  milkRecordDate: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.primary,
    marginLeft: 8,
  },
  totalMilkContainer: {
    backgroundColor: colors.primary + '20',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  totalMilkContainerRTL: {
    alignItems: 'flex-end',
  },
  totalMilkText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.primary,
  },
  milkRecordDetails: {
    marginBottom: 8,
  },
  milkRecordDetailsRTL: {
    alignItems: 'flex-end',
  },
  milkDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  milkDetailRowRTL: {
    flexDirection: 'row-reverse',
  },
  milkDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  milkDetailItemRTL: {
    flexDirection: 'row-reverse',
  },
  milkDetailLabel: {
    fontSize: 12,
    color: colors.gray[600],
    marginLeft: 4,
    marginRight: 4,
  },
  milkDetailValue: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '500',
  },
  milkRecordNotes: {
    fontSize: 12,
    color: colors.gray[600],
    fontStyle: 'italic',
    marginBottom: 8,
    lineHeight: 16,
  },
  milkRecordFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  milkRecordFooterRTL: {
    flexDirection: 'row-reverse',
  },
  milkRecordTime: {
    fontSize: 12,
    color: colors.gray[500],
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  emptyAddButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyAddButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  textRTL: {
    textAlign: 'right',
  },
});
