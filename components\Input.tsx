import React, { useState } from 'react';
import { 
  View, 
  TextInput, 
  Text, 
  StyleSheet, 
  TextInputProps,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
  I18nManager,
} from 'react-native';
import { colors } from '@/constants/colors';
import { Eye, EyeOff } from 'lucide-react-native';
import { useTranslation } from '@/i18n/useTranslation';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  inputStyle?: TextStyle;
  errorStyle?: TextStyle;
  isPassword?: boolean;
  editable?: boolean;
  secureTextEntry?: boolean;
  onBlur?: () => void;
  required?: boolean;
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  leftIcon,
  rightIcon,
  containerStyle,
  labelStyle,
  inputStyle,
  errorStyle,
  isPassword = false,
  secureTextEntry,
  onBlur,
  editable,
  required = false,
  ...rest
}) => {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const { isRTL } = useTranslation();
  
  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };
  
  const passwordIcon = isPassword ? (
    <TouchableOpacity onPress={togglePasswordVisibility} style={styles.iconContainer}>
      {isPasswordVisible ? (
        <EyeOff size={20} color={colors.gray[500]} />
      ) : (
        <Eye size={20} color={colors.gray[500]} />
      )}
    </TouchableOpacity>
  ) : null;
  
  // Determine which icon should be on which side based on RTL
  const startIcon = isRTL ? rightIcon : leftIcon;
  const endIcon = isRTL ? leftIcon : (rightIcon || (isPassword ? passwordIcon : null));
  
  const handleBlur = () => {
    if (onBlur) {
      onBlur();
    }
  };
  
  return (
    <View style={[styles.container, containerStyle]}>
      {label ? (
        <View style={[styles.labelContainer, isRTL && styles.rtlDirection]}>
          <Text style={[styles.label, labelStyle, isRTL && styles.rtlTextAlign]}>
            {label}
          </Text>
          {required && <Text style={styles.required}>*</Text>}
        </View>
      ) : null}
      
      <View style={[
        styles.inputContainer,
        error ? styles.inputError : null,
      ]}>
        {startIcon ? (
          <View style={[styles.iconContainer]}>
            {startIcon}
          </View>
        ) : null}
        
        <TextInput
          style={[
            styles.input,
            startIcon ? styles.inputWithStartIcon : null,
            endIcon ? styles.inputWithEndIcon : null,
            { textAlign: isRTL ? 'right' : 'left' },
            inputStyle,
          ]}
          placeholderTextColor={colors.gray[400]}
          secureTextEntry={isPassword ? !isPasswordVisible : secureTextEntry}
          onBlur={handleBlur}
          editable={editable}
          {...rest}
        />
        
        {endIcon && !isPassword ? (
          <View style={styles.iconContainer}>
            {endIcon}
          </View>
        ) : null}
        
        {isPassword ? passwordIcon : null}
      </View>
      
      {error ? (
        <Text style={[styles.errorText, errorStyle]}>
          {error}
        </Text>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
    width: '100%',
  },
  rtlDirection:{
    flexDirection: 'row-reverse',
  },
  rtlTextAlign:{
    textAlign: 'right',
  },
  labelContainer: {
    flexDirection: 'row',
    marginBottom: 6,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  required: {
    color: colors.danger,
    marginLeft: 4,
    fontSize: 14,
    fontWeight: '500',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    backgroundColor: colors.white,
  },
  input: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    fontSize: 14,
    color: colors.gray[800],
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  inputWithStartIcon: {
    paddingLeft: I18nManager.isRTL ? 16 : 8,
    paddingRight: I18nManager.isRTL ? 8 : 16,
  },
  inputWithEndIcon: {
    paddingLeft: I18nManager.isRTL ? 8 : 16,
    paddingRight: I18nManager.isRTL ? 16 : 8,
  },
  iconContainer: {
    paddingHorizontal: 12,
  },
  inputError: {
    borderColor: colors.danger,
  },
  errorText: {
    color: colors.danger,
    fontSize: 12,
    marginTop: 4,
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
});

export default Input;