// import React, { useState } from 'react';
// import {
//   View,
//   Text,
//   StyleSheet,
//   ScrollView,
//   TouchableOpacity,
//   Alert,
//   ActivityIndicator,
//   SafeAreaView,
// } from 'react-native';
// import { router, Stack } from 'expo-router';
// import { colors } from '@/constants/colors';
// import { useFarmStore } from '@/store/farm-store';
// import { useAuthStore } from '@/store/auth-store';
// import Button from '@/components/Button';
// import Input from '@/components/Input';
// import {
//   UserPlus,
//   Mail,
//   Phone,
//   User,
//   Shield,
//   ChevronDown,
//   Check,
//   Lock,
// } from 'lucide-react-native';
// import { createUserWithEmailAndPassword, sendEmailVerification } from 'firebase/auth';
// import { auth } from '@/firebase/config';
// import { useTranslation } from '@/i18n/useTranslation';
// import RoleToggle from '@/components/RoleToggle';

// export default function InviteUserScreen() {
//   const { inviteUserToFarm, currentFarm } = useFarmStore();
//   const { user, assignFarmToUser } = useAuthStore();
//   const { t, isRTL } = useTranslation();

//   const [name, setName] = useState('');
//   const [email, setEmail] = useState('');
//   const [phone, setPhone] = useState('');
//   const [password, setPassword] = useState('');
//   const [role, setRole] = useState<'admin' | 'caretaker'>(user?.role === 'owner' ? 'admin' : 'caretaker');
//   const [showRoleDropdown, setShowRoleDropdown] = useState(false);
//   const [isLoading, setIsLoading] = useState(false);
//   const userRoles = [
//     {
//       label: t('user.role.admin', 'Manager'),
//       value: 'admin',
//       icon: <Shield size={18} color={colors.info} />,
//       color: colors.info,
//     },
//     {
//       label: t('user.role.caretaker', 'Caretaker'),
//       value: 'caretaker',
//       icon: <Shield size={18} color={colors.success} />,
//       color: colors.success,
//     },
//     // {
//     //   label: t('user.role.owner', 'Owner'),
//     //   value: 'owner',
//     //   icon: <Shield size={18} color={colors.primary} />,
//     //   color: colors.primary,
//     // },
//   ];
//   const handleInviteUser = async () => {
//     if (!name.trim()) {
//       Alert.alert(t('error'), t('user.nameRequired'));
//       return;
//     }

//     if (!email.trim()) {
//       Alert.alert(t('error'), t('user.emailRequired'));
//       return;
//     }

//     if (!phone.trim()) {
//       Alert.alert(t('error'), t('user.phoneRequired'));
//       return;
//     }

//     if (!password.trim() || password.length < 6) {
//       Alert.alert(t('error'), t('user.passwordRequired', 'Password is required and must be at least 6 characters long.'));
//       return;
//     }

//     if (!currentFarm) {
//       Alert.alert(t('error'), t('farm.noFarmSelected'));
//       return;
//     }

//     try {
//       setIsLoading(true);

//       // First, create the user in Firebase Authentication
//       const userCredential = await createUserWithEmailAndPassword(auth, email, password);
//       const newUserId = userCredential.user.uid;

//       // Send verification email
//       await sendEmailVerification(userCredential.user);

//       // Then, assign the farm to the user using the new method
//       // await assignFarmToUser(newUserId, currentFarm.id, role);

//       // Also invite the user to the farm (this will create notifications)
//       await inviteUserToFarm(currentFarm.id, email, phone, role, name,newUserId);

//       Alert.alert(
//         t('success'),
//         t('user.inviteSuccess', { name, role: t(`user.role.${role}`) }) +
//         ' ' + t('user.verificationEmailSent'),
//         [
//           {
//             text: t('common.ok'),
//             onPress: () => {
//               // Clear form
//               setName('');
//               setEmail('');
//               setPhone('');
//               setPassword('');
//               setRole(user?.role === 'owner' ? 'admin' : 'caretaker');
//             }
//           }
//         ]
//       );
//     } catch (error: any) {
//       console.error('Error inviting user:', error);

//       // Handle specific Firebase Auth errors
//       if (error.code === 'auth/email-already-in-use') {
//         Alert.alert(t('error'), t('user.emailInUse'));
//       } else if (error.code === 'auth/invalid-email') {
//         Alert.alert(t('error'), t('user.invalidEmail'));
//       } else {
//         Alert.alert(t('error'), t('user.inviteError'));
//       }
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   return (
//     <>
//       <Stack.Screen
//         options={{
//           title: t('user.invite'),
//           headerShown:false
//         }}
//       />

//       <SafeAreaView style={styles.container}>
//         <ScrollView contentContainerStyle={styles.scrollContent}>
//           <View style={styles.header}>
//             <UserPlus size={40} color={colors.primary} />
//             <Text style={styles.headerTitle}>{t('user.inviteNew')}</Text>
//             <Text style={styles.headerSubtitle}>
//               {t('user.inviteDescription')}
//             </Text>
//           </View>

//           <View style={styles.formContainer}>
//             <Input
//               label={t('user.fullName')}
//               placeholder={t('user.enterFullName')}
//               value={name}
//               onChangeText={setName}
//               containerStyle={styles.inputContainer}
//               leftIcon={<User size={20} color={colors.gray[500]} />}
//             // isRTL={isRTL}
//             />

//             <Input
//               label={t('user.email')}
//               placeholder={t('user.enterEmail')}
//               value={email}
//               onChangeText={setEmail}
//               keyboardType="email-address"
//               autoCapitalize="none"
//               containerStyle={styles.inputContainer}
//               leftIcon={<Mail size={20} color={colors.gray[500]} />}
//             // isRTL={isRTL}
//             />

//             <Input
//               label={t('user.phone')}
//               placeholder={t('user.enterPhone')}
//               value={phone}
//               onChangeText={setPhone}
//               keyboardType="phone-pad"
//               containerStyle={styles.inputContainer}
//               leftIcon={<Phone size={20} color={colors.gray[500]} />}
//             // isRTL={isRTL}
//             />

//             <Input
//               label={t('user.password', 'Password')}
//               placeholder={t('user.enterPassword', 'Enter password')}
//               value={password}
//               onChangeText={setPassword}
//               secureTextEntry
//               containerStyle={styles.inputContainer}
//               leftIcon={<Lock size={20} color={colors.gray[500]} />}
//             // isRTL={isRTL}
//             />

//             <Text style={[styles.label, isRTL && styles.labelRTL]}>{t('user.role.label')}</Text>
//             <RoleToggle role={role} setRole={setRole} />

//             <View style={styles.roleDescription}>
//               {role === 'admin' ? (
//                 <Text style={[styles.roleDescriptionText, isRTL && styles.roleDescriptionTextRTL]}>
//                   {t('user.role.managerDescription')}
//                 </Text>
//               ) : (
//                 <Text style={[styles.roleDescriptionText, isRTL && styles.roleDescriptionTextRTL]}>
//                   {t('user.role.caretakerDescription')}
//                 </Text>
//               )}
//             </View>
//           </View>

//           <View style={styles.farmInfoContainer}>
//             <Text style={[styles.farmInfoLabel, isRTL && styles.farmInfoLabelRTL]}>
//               {t('farm.invitingTo')}
//             </Text>
//             <Text style={[styles.farmInfoValue, isRTL && styles.farmInfoValueRTL]}>
//               {currentFarm?.name || t('farm.noFarmSelected')}
//             </Text>
//           </View>

//           <Button
//             title={isLoading ? t('user.inviting') : t('user.sendInvitation')}
//             onPress={handleInviteUser}
//             disabled={isLoading || !name || !email || !phone || !password || !currentFarm}
//             style={styles.inviteButton}
//             leftIcon={isLoading ? <ActivityIndicator size="small" color={colors.white} /> : <UserPlus size={20} color={colors.white} />}
//             // isRTL={isRTL}
//           />

//           <Text style={[styles.noteText, isRTL && styles.noteTextRTL]}>
//             {t('user.inviteNote')}
//           </Text>
//         </ScrollView>
//       </SafeAreaView>
//     </>
//   );
// }

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: colors.gray[50],
//   },
//   scrollContent: {
//     padding: 16,
//     paddingBottom: 40,
//   },
//   header: {
//     alignItems: 'center',
//     marginBottom: 24,
//   },
//   headerTitle: {
//     fontSize: 24,
//     fontWeight: '600',
//     color: colors.gray[800],
//     marginTop: 16,
//     marginBottom: 8,
//   },
//   headerSubtitle: {
//     fontSize: 16,
//     color: colors.gray[600],
//     textAlign: 'center',
//   },
//   formContainer: {
//     backgroundColor: colors.white,
//     borderRadius: 12,
//     padding: 16,
//     marginBottom: 24,
//     shadowColor: colors.gray[400],
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 4,
//     elevation: 2,
//   },
//   inputContainer: {
//     marginBottom: 16,
//   },
//   label: {
//     fontSize: 14,
//     fontWeight: '500',
//     color: colors.gray[700],
//     marginBottom: 8,
//     textAlign: 'left',
//   },
//   labelRTL: {
//     textAlign: 'right',
//   },
//   roleContainer: {
//     marginBottom: 16,
//   },
//   roleDropdown: {
//     borderWidth: 1,
//     borderColor: colors.gray[300],
//     borderRadius: 8,
//     position: 'relative',
//   },
//   roleDropdownHeader: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     paddingHorizontal: 12,
//     paddingVertical: 12,
//   },
//   roleDropdownHeaderRTL: {
//     flexDirection: 'row-reverse',
//   },
//   roleIcon: {
//     marginRight: 8,
//   },
//   roleIconRTL: {
//     marginRight: 0,
//     marginLeft: 8,
//   },
//   roleText: {
//     fontSize: 14,
//     color: colors.gray[800],
//     flex: 1,
//     textAlign: 'left',
//   },
//   roleTextRTL: {
//     textAlign: 'right',
//   },
//   roleDropdownMenu: {
//     position: 'absolute',
//     top: '100%',
//     left: 0,
//     right: 0,
//     backgroundColor: colors.white,
//     borderBottomLeftRadius: 8,
//     borderBottomRightRadius: 8,
//     borderWidth: 1,
//     borderTopWidth: 0,
//     borderColor: colors.gray[300],
//     zIndex: 10,
//     elevation: 3,
//     shadowColor: colors.gray[400],
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 4,
//   },
//   roleDropdownItem: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     paddingHorizontal: 12,
//     paddingVertical: 12,
//     borderBottomWidth: 1,
//     borderBottomColor: colors.gray[200],
//   },
//   roleDropdownItemRTL: {
//     flexDirection: 'row-reverse',
//   },
//   roleDropdownItemText: {
//     fontSize: 14,
//     color: colors.gray[800],
//     flex: 1,
//     textAlign: 'left',
//   },
//   roleDropdownItemTextRTL: {
//     textAlign: 'right',
//   },
//   roleDescription: {
//     backgroundColor: colors.gray[100],
//     borderRadius: 8,
//     padding: 12,
//   },
//   roleDescriptionText: {
//     fontSize: 14,
//     color: colors.gray[700],
//     lineHeight: 20,
//     textAlign: 'left',
//   },
//   roleDescriptionTextRTL: {
//     textAlign: 'right',
//   },
//   farmInfoContainer: {
//     backgroundColor: colors.white,
//     borderRadius: 12,
//     padding: 16,
//     marginBottom: 24,
//     shadowColor: colors.gray[400],
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 4,
//     elevation: 2,
//   },
//   farmInfoLabel: {
//     fontSize: 14,
//     color: colors.gray[500],
//     marginBottom: 4,
//     textAlign: 'left',
//   },
//   farmInfoLabelRTL: {
//     textAlign: 'right',
//   },
//   farmInfoValue: {
//     fontSize: 16,
//     fontWeight: '600',
//     color: colors.gray[800],
//     textAlign: 'left',
//   },
//   farmInfoValueRTL: {
//     textAlign: 'right',
//   },
//   inviteButton: {
//     marginBottom: 16,
//   },
//   noteText: {
//     fontSize: 14,
//     color: colors.gray[600],
//     textAlign: 'center',
//     lineHeight: 20,
//   },
//   noteTextRTL: {
//     textAlign: 'center',
//   },
// });
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  Modal,
  FlatList,
} from 'react-native';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import Button from '@/components/Button';
import Input from '@/components/Input';
import {
  UserPlus,
  Mail,
  Phone,
  User,
  Shield,
  ChevronDown,
  Check,
  Lock,
  Home,
  Plus,
  X,
} from 'lucide-react-native';
import { createUserWithEmailAndPassword, sendEmailVerification } from 'firebase/auth';
import { auth } from '@/firebase/config';
import { useTranslation } from '@/i18n/useTranslation';
import RoleToggle from '@/components/RoleToggle';
// import Toast from 'react-native-toast-message';
import Toast from 'react-native-toast-message';

export default function InviteUserScreen() {
  const { inviteUserToFarm, currentFarm, farms, fetchFarms, removeUserFromFarm } = useFarmStore();
  const { user, assignFarmToUser, getUserById, updateUserProfile } = useAuthStore();
  const { t, isRTL } = useTranslation();
  const params = useLocalSearchParams();
  const editMode = params.editMode === 'true';
  const userId = params.userId as string;

  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [role, setRole] = useState<'admin' | 'caretaker'>('caretaker'); // Default to caretaker, allow selection based on permissions
  const [isLoading, setIsLoading] = useState(false);
  const [showFarmModal, setShowFarmModal] = useState(false);
  const [selectedFarms, setSelectedFarms] = useState<string[]>([]);
  const [userToEdit, setUserToEdit] = useState<any>(null);

  // Field validation errors
  const [fieldErrors, setFieldErrors] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    role: '',
    farms: ''
  });

  // Load farms on component mount
  // useEffect(() => {
  //   if (user?.id) {
  //     fetchFarms(user.id);
  //   }
  // }, [user]);

  // Load user data if in edit mode
  useEffect(() => {
    if (editMode && userId) {
      loadUserData();
    } else if (currentFarm) {
      // In create mode, pre-select current farm
      setSelectedFarms([currentFarm.id]);
    }
  }, [editMode, userId, currentFarm]);

  const loadUserData = async () => {
    try {
      setIsLoading(true);
      const userData = await getUserById(userId);
      if (userData) {
        setUserToEdit(userData);
        setName(userData.displayName || '');
        setEmail(userData.email || '');
        setPhone(userData.phoneNumber || '');
        setRole((userData.role === 'owner' || userData.role === 'admin') ? 'admin' : 'caretaker');
        setSelectedFarms(userData.assignedFarmIds || []);
      }
    } catch (error) {
      console.error('Error loading user data:', error);
      Alert.alert(t('error'), t('user.loadError'));
    } finally {
      setIsLoading(false);
    }
  };

  const validateFields = () => {
    const errors = {
      name: '',
      email: '',
      phone: '',
      password: '',
      role: '',
      farms: ''
    };

    let hasErrors = false;

    if (!name.trim()) {
      errors.name = t('user.nameRequired');
      hasErrors = true;
    }

    if (!email.trim()) {
      errors.email = t('user.emailRequired');
      hasErrors = true;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      errors.email = t('user.validEmailRequired');
      hasErrors = true;
    }

    if (!phone.trim()) {
      errors.phone = t('user.phoneRequired');
      hasErrors = true;
    }

    if (!editMode && (!password || password.length < 6)) {
      errors.password = !password ? t('user.passwordRequired') : t('user.passwordMinLength');
      hasErrors = true;
    }

    if (!role) {
      errors.role = t('user.roleRequired');
      hasErrors = true;
    }

    if (selectedFarms.length === 0) {
      errors.farms = t('user.farmRequired');
      hasErrors = true;
    }

    setFieldErrors(errors);
    return !hasErrors;
  };

  const handleInviteUser = async () => {
    if (!validateFields()) {
      Toast.show({
        type: 'overlay',
        text1: t('common.error'),
        text2: t('common.fillAllFields'),
      });
      return;
    }

    if (!editMode && (!password.trim() || password.length < 6)) {
      Alert.alert(t('error'), t('user.passwordRequired', 'Password is required and must be at least 6 characters long.'));
      return;
    }

    if (selectedFarms.length === 0) {
      Alert.alert(t('error'), t('farm.selectAtLeastOne'));
      return;
    }

    try {
      setIsLoading(true);

      if (editMode) {
        // Update user
        const updatedUserData = { // Do not spread userToEdit to avoid merging
          id: userId, // Ensure the ID is included for the update
          displayName: name,
          phoneNumber: phone,
          role: role,
          assignedFarmIds: selectedFarms,
          updatedAt: new Date()
        };

        // await inviteUserToFarm(selectedFarms, email, phone, role, name, userId)//updateUserProfile(updatedUserData);
        // Assign selected farms to the user
        // for (const farmId of selectedFarms) {
          await inviteUserToFarm(selectedFarms[0], email, phone, role, name, userId,selectedFarms);
        // }
        Alert.alert(
          t('success'),
          t('user.updateSuccess'),
          [
            {
              text: t('common.ok'),
              onPress: () => router.back()
            }
          ]
        );
      } else {
        // Create new user
        const userCredential = await createUserWithEmailAndPassword(auth, email, password);
        const newUserId = userCredential.user.uid;

        // Send verification email
        await sendEmailVerification(userCredential.user);

        // Assign selected farms to the user
        // for (const farmId of selectedFarms) {
          await inviteUserToFarm(selectedFarms, email, phone, role, name, newUserId);
        // }

        Alert.alert(
          t('success'),
          t('user.inviteSuccess', { name, role: t(`user.role.${role}`) }) +
          ' ' + t('user.verificationEmailSent'),
          [
            {
              text: t('common.ok'),
              onPress: () => {
                // Clear form
                setName('');
                setEmail('');
                setPhone('');
                setPassword('');
                setRole('caretaker'); // Reset to default
                setSelectedFarms(currentFarm ? [currentFarm.id] : []);
              }
            }
          ]
        );
      }
    } catch (error: any) {
      console.error('Error inviting/updating user:', error);

      // Handle specific Firebase Auth errors
      if (error.code === 'auth/email-already-in-use') {
        Alert.alert(t('error'), t('user.emailInUse'));
      } else if (error.code === 'auth/invalid-email') {
        Alert.alert(t('error'), t('user.invalidEmail'));
      } else {
        Alert.alert(t('error'), editMode ? t('user.updateError') : t('user.inviteError'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  const toggleFarmSelection = (farmId: string) => {
    setSelectedFarms(prev => {
      if (prev.includes(farmId)) {
        return prev.filter(id => id !== farmId);
      } else {
        return [...prev, farmId];
      }
    });
  };

  const renderFarmItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={[styles.modalItem, isRTL && { flexDirection: 'row-reverse' }]}
      onPress={() => toggleFarmSelection(item.id)}
    >
      <View style={[styles.modalItemContent, isRTL && { flexDirection: 'row-reverse' }]}>
        <Home size={20} color={colors.primary} style={styles.modalItemIcon} />
        <Text style={[styles.modalItemText, isRTL && { marginRight: 8 }]}>{item.name}</Text>
      </View>
      {selectedFarms.includes(item.id) && (
        <Check size={20} color={colors.success} />
      )}
    </TouchableOpacity>
  );

  return (
    <>
      <Stack.Screen
        options={{
          title: editMode ? t('user.edit') : t('user.invite'),
          headerShown: true
        }}
      />

      <SafeAreaView style={styles.container}>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : (
          <ScrollView contentContainerStyle={styles.scrollContent}>
            <View style={styles.header}>
              <UserPlus size={40} color={colors.primary} />
              <Text style={styles.headerTitle}>
                {editMode ? t('user.editUser') : t('user.inviteNew')}
              </Text>
              <Text style={styles.headerSubtitle}>
                {editMode ? t('user.editDescription') : t('user.inviteDescription')}
              </Text>
            </View>

            <View style={styles.formContainer}>
              <Input
                label={t('user.fullName')}
                placeholder={t('user.enterFullName')}
                value={name}
                onChangeText={setName}
                containerStyle={styles.inputContainer}
                leftIcon={<User size={20} color={colors.gray[500]} />}
              />

              <Input
                label={t('user.email')}
                placeholder={t('user.enterEmail')}
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                containerStyle={styles.inputContainer}
                leftIcon={<Mail size={20} color={colors.gray[500]} />}
                editable={!editMode} // Email can't be edited
              />

              <Input
                label={t('user.phone')}
                placeholder={t('user.enterPhone')}
                value={phone}
                onChangeText={setPhone}
                keyboardType="phone-pad"
                containerStyle={styles.inputContainer}
                leftIcon={<Phone size={20} color={colors.gray[500]} />}
              />

              {!editMode && (
                <Input
                  label={t('user.password', 'Password')}
                  placeholder={t('user.enterPassword', 'Enter password')}
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry
                  containerStyle={styles.inputContainer}
                  leftIcon={<Lock size={20} color={colors.gray[500]} />}
                />
              )}

              <Text style={[styles.label, isRTL && styles.labelRTL]}>{t('user.role.label')}</Text>
              {/* Only owners and admins can assign admin roles */}
              {user?.role === 'owner' || user?.role === 'admin' ? (
                <RoleToggle role={role} setRole={setRole} />
              ) : (
                <View style={styles.roleInfoContainer}>
                  <Text style={styles.roleInfoText}>
                    {t('user.role.caretaker')} - {t('user.caretakerDescription')}
                  </Text>
                </View>
              )}

              <View style={styles.farmSelectionContainer}>
                <Text style={[styles.label, isRTL && styles.labelRTL]}>{t('farm.assignFarms')}</Text>
                <TouchableOpacity
                  style={[styles.farmSelectionButton, isRTL && { flexDirection: 'row-reverse' }]}
                  onPress={() => setShowFarmModal(true)}
                >
                  <Home size={20} color={colors.gray[500]} style={styles.farmIcon} />
                  <Text style={[styles.farmSelectionText, isRTL && { textAlign: 'right' }]}>
                    {selectedFarms.length > 0
                      ? t('farm.selectedFarmsCount', { count: selectedFarms.length })
                      : t('farm.selectFarms')}
                  </Text>
                  <ChevronDown size={20} color={colors.gray[500]} />
                </TouchableOpacity>
              </View>

              {selectedFarms.length > 0 && (
                <View style={styles.selectedFarmsContainer}>
                  {selectedFarms.map(farmId => {
                    const farm = farms.find(f => f.id === farmId);
                    if (!farm) return null;

                    return (
                      <View key={farmId} style={styles.selectedFarmChip}>
                        <Text style={styles.selectedFarmText}>{farm.name}</Text>
                        <TouchableOpacity
                          onPress={() => toggleFarmSelection(farmId)}
                          style={styles.removeChipButton}
                        >
                          <X size={16} color={colors.white} />
                        </TouchableOpacity>
                      </View>
                    );
                  })}
                </View>
              )}

              <View style={styles.roleDescription}>
                {role === 'admin' ? (
                  <Text style={[styles.roleDescriptionText, isRTL && styles.roleDescriptionTextRTL]}>
                    {t('user.adminDescription', 'Managers can create and assign tasks, manage all farm entities, and view reports.')}
                  </Text>
                ) : (
                  <Text style={[styles.roleDescriptionText, isRTL && styles.roleDescriptionTextRTL]}>
                    {t('user.caretakerDescription', 'Caretakers can view and complete assigned tasks, and update the status of farm entities.')}
                  </Text>
                )}
              </View>

              <Button
                title={editMode ? t('common.update') : t('user.sendInvitation')}
                onPress={handleInviteUser}
                style={styles.inviteButton}
                loading={isLoading}
              />
            </View>
          </ScrollView>
        )}

        {/* Farm Selection Modal */}
        <Modal
          visible={showFarmModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowFarmModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={[styles.modalHeader, isRTL && { flexDirection: 'row-reverse' }]}>
                <Text style={styles.modalTitle}>{t('farm.selectFarms')}</Text>
                <TouchableOpacity onPress={() => setShowFarmModal(false)}>
                  <Text style={styles.modalCloseText}>{t('common.done')}</Text>
                </TouchableOpacity>
              </View>

              {farms.length > 0 ? (
                <FlatList
                  data={farms}
                  renderItem={renderFarmItem}
                  keyExtractor={item => item.id}
                  style={styles.modalList}
                />
              ) : (
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>{t('farm.noFarmsCreateFirst')}</Text>
                </View>
              )}

              <Button
                title={t('common.done')}
                onPress={() => setShowFarmModal(false)}
                style={styles.doneButton}
              />
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContent: {
    padding: 16,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.gray[800],
    marginTop: 16,
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
  },
  formContainer: {
    marginBottom: 24,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
  },
  labelRTL: {
    textAlign: 'right',
  },
  roleDescription: {
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    padding: 12,
    marginTop: 16,
    marginBottom: 24,
  },
  roleDescriptionText: {
    fontSize: 14,
    color: colors.gray[700],
    lineHeight: 20,
  },
  roleDescriptionTextRTL: {
    textAlign: 'right',
  },
  inviteButton: {
    marginTop: 8,
  },
  farmSelectionContainer: {
    marginTop: 16,
  },
  farmSelectionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 12,
    backgroundColor: colors.white,
  },
  farmIcon: {
    marginRight: 8,
  },
  farmSelectionText: {
    flex: 1,
    fontSize: 16,
    color: colors.gray[700],
  },
  selectedFarmsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 12,
  },
  selectedFarmChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  selectedFarmText: {
    color: colors.white,
    fontSize: 14,
    marginRight: 4,
  },
  removeChipButton: {
    padding: 2,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingBottom: 24,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.gray[800],
  },
  modalCloseText: {
    fontSize: 16,
    color: colors.primary,
  },
  modalList: {
    maxHeight: 400,
  },
  modalItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalItemIcon: {
    marginRight: 12,
  },
  modalItemText: {
    fontSize: 16,
    color: colors.gray[800],
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
  },
  doneButton: {
    marginHorizontal: 16,
    marginTop: 16,
  },
  roleInfoContainer: {
    padding: 12,
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    marginVertical: 8,
  },
  roleInfoText: {
    fontSize: 14,
    color: colors.gray[700],
    textAlign: 'center',
  },
});
